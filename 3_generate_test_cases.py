# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-06      #
# --------------------------------- #

"""
Generate positive and negative test cases for XML (LLM-based approach)
    Requires JSON application report and testcase generation details TSV generated using 1_get_app_report_json.py
"""

import ast
import copy
import csv
import json
import logging
import os
import sys
import time
from collections import defaultdict
from copy import deepcopy

import pandas as pd

from utils.logging_utils import setup_logging
from utils.misc_utils import prepare_test_data
from utils.openai_utils import OpenAIUtils
from utils.prompts.xml_data_generation import XMLDataGenerationConfig
from utils.argument_utils import ApplicationArgumentParser
from utils.error_codes import (ERR_FILE_001, ERR_BATCH_001, ERR_BATCH_002, ERR_BATCH_003, ERR_BATCH_004,  ERR_RULE_001,
                               ERR_RULE_001_01, ERR_RULE_001_02, ERR_RULE_001_03, ERR_RULE_001_04, ERR_RULE_001_05)

# Use a flag to indicate if you want to generate test cases for component rules
INCLUDE_COMPONENTS = True

LOGGER_NAME = "Testcases Generator"
logger = logging.getLogger(LOGGER_NAME)


def parse_arguments(_):
    # Set the default application path directly in this file
    DEFAULT_APP_PATH = "../../applications/ACKLET"

    parser = ApplicationArgumentParser("3_generate_test_cases",
                                     "Generate positive and negative test cases for XML (LLM-based approach)",
                                     default_application_path=DEFAULT_APP_PATH)

    parser.add_file_argument('app_report_json_path',
                           'xml_parse/ACKLET Letters (AK) - PTRKTPAD AppReport.json',
                           'JSON application report')
    parser.add_file_argument('testcase_generation_details_tsv',
                           'xml_parse/app_info/testcase_generation_details.tsv',
                           'TSV containing test case generation details')
    parser.add_file_argument('complete_xml_log',
                           'complete_payload/sample_payload_log.tsv',
                           'Complete payload XML log to get the sample values')
    parser.add_file_argument('element_types_tsv_path',
                           'complete_payload/element_types.tsv',
                           'Path of the TSV file containing element type details',
                           required=False)
    parser.add_file_argument('rule_errors_tsv',
                           'logs/rule_errors_from_parsing.tsv',
                           'Path of the TSV file containing rule error details obtained during parsing',
                           required=False)

    return parser.parse_args()


def main(args):

    # Configure logging
    log_dir = os.path.join(os.path.dirname(os.path.dirname(args.app_report_json_path)), "logs")
    if not os.path.exists(log_dir):
        os.mkdir(log_dir)
    log_file = "3_testcases_generator.log"
    log_file_path = os.path.join(log_dir, log_file)
    setup_logging(log_file=log_file_path, level=logging.DEBUG, max_bytes=1 * 1024 * 1024, backup_count=3, app_name=LOGGER_NAME)

    """Check for input arguments"""
    if not os.path.exists(args.app_report_json_path):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.app_report_json_path}")
    if not os.path.exists(args.testcase_generation_details_tsv):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.testcase_generation_details_tsv}")
    if not os.path.exists(args.complete_xml_log):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.complete_xml_log}")
    if not os.path.exists(args.element_types_tsv_path):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.element_types_tsv_path}")

    with open(args.app_report_json_path, 'r') as json_file:
        app_report_json = json.load(json_file)
    input_details_df = pd.read_csv(args.testcase_generation_details_tsv, sep="\t", header=0, index_col=None,
                                   quoting=csv.QUOTE_NONE, escapechar="\\", dtype={"custom_id": str, "custom_id_original": str})
    input_details_df['variables'] = input_details_df['variables'].apply(ast.literal_eval)
    input_details_df['var_to_tag'] = input_details_df['var_to_tag'].apply(ast.literal_eval)

    sample_payload_df = pd.read_csv(args.complete_xml_log, sep="\t", header=0, index_col=None, usecols=[0, 2], na_filter=False)
    element_types_df = pd.read_csv(args.element_types_tsv_path, sep="\t", header=0, index_col=None, usecols=[0, 1, 2, 3])

    if os.path.exists(args.rule_errors_tsv):
        dtype = {"rule_id": str, "rule_type": str, "no_vars_in_rule": bool, "undefined_rule": bool}
        rule_errors_df = pd.read_csv(args.rule_errors_tsv, sep="\t", header=0, index_col=None, dtype=dtype,
                                     converters={"unmapped_sections": ast.literal_eval, "total_unmapped_vars": ast.literal_eval})
        rule_errors_df['unmapped_vars_in_conditions'] = pd.Series([[] for _ in range(len(rule_errors_df))], dtype=object)
        rule_errors_file_path_new = os.path.join(os.path.dirname(args.rule_errors_tsv), "rule_errors_from_generation.tsv")
    else:
        rule_errors_df = None

    # Create directory for test cases and prepare the output file path
    test_cases_dir = os.path.join(os.path.dirname(args.app_report_json_path), "test_cases")
    if not os.path.exists(test_cases_dir):
        os.mkdir(test_cases_dir)
    test_cases_json_file = os.path.splitext(os.path.basename(args.app_report_json_path))[0] + "_test_cases.json"
    output_json_path = os.path.join(test_cases_dir, test_cases_json_file)

    # Create TSV file path to save model output
    output_tsv = os.path.join(test_cases_dir, "model_output.tsv")

    output_json = deepcopy(app_report_json)

    # Set up generation related parameters
    config = XMLDataGenerationConfig()
    openai_utils = OpenAIUtils()

    def _process_node(node, rule_type="document"):
        """
        Process either:
          - container nodes with node['rule']
          - or rule-objects which themselves have 'rule-oi' at the top level.
        Then recurse into any child lists.
        """
        # Decide where the rule lives (and where to write back)
        section_cases = None
        write_to = None
        if "rule" in node:
            rule_obj = node["rule"]
            write_to = rule_obj
            section_cases = rule_obj.get("section-test-cases")
        elif "rule-oi" in node:
            # this node is a rule-object - for "ref-rules"
            rule_obj = node
            write_to = node
        else:
            rule_obj = None

        # If we found a rule, do the lookup + generate
        if rule_obj:
            if rule_obj.get("rule-oi") in rule_map:
                if not rule_obj["rule-status"]["error"]["no-variables-in-rule"]:
                    output, var_to_tag, updated_vars = rule_map[rule_obj["rule-oi"]]
                    write_to["variables"] = updated_vars

                    gen, cases, unmapped_vars = prepare_test_data(output, var_to_tag, section_cases)

                    # Update the rule success status if unmapped_vars has elements
                    # del write_to["rule-status"]["error"]["total-unmapped-variables"]
                    write_to["rule-status"]["error"]["unmapped-variables-in-conditions"] = unmapped_vars
                    if len(unmapped_vars) > 0:
                        if write_to["rule-status"]["success"] is True:
                            write_to["rule-status"]["success"] = False

                        # Update the unmapped_vars column
                        if rule_errors_df is not None:
                            mask = (rule_errors_df['rule_id'] == rule_obj["rule-oi"]) & (rule_errors_df['rule_type'] == rule_type)
                            if mask.any():
                                for i in rule_errors_df.index[mask]:
                                    rule_errors_df.at[i, 'unmapped_vars_in_conditions'] = unmapped_vars
                            else:
                                new_row = {"rule_id": rule_obj["rule-oi"], "rule_type": rule_type,
                                           "unmapped_sections": write_to["rule-status"]["error"]["unmapped-sections"],
                                           "no_vars_in_rule": write_to["rule-status"]["error"]["no-variables-in-rule"],
                                           "undefined_rule": write_to["rule-status"]["error"]["undefined-rule"],
                                           "total_unmapped_vars": write_to["rule-status"]["error"]["total-unmapped-variables"],
                                           "unmapped_vars_in_conditions": unmapped_vars
                                           }
                                rule_errors_df.loc[len(rule_errors_df)] = new_row

                    write_to["generated-test-cases"] = gen
                    write_to["test-cases"] = cases
                else:
                    write_to["rule-status"]["error"]["unmapped-variables-in-conditions"] = []
                    if section_cases:
                        write_to["test-cases"] = {key: [value] for key, value in section_cases.items()}
                    else:
                        write_to["test-cases"] = {"positives": [], "negatives": []}

            elif "section-test-cases" in rule_obj:  # Special case where document does not have a rule, but only depends on a section
                write_to["rule-status"]["error"]["unmapped-variables-in-conditions"] = []
                write_to["test-cases"] = {key: [value] for key, value in rule_obj["section-test-cases"].items()}

            else:
                write_to["rule-status"]["error"]["unmapped-variables-in-conditions"] = []
                write_to["test-cases"] = {"positives": [], "negatives": []}

        # Mapping from child-list to the rule_type
        type_map = {
            "pages": "page",
            "sections": "section",
            "paragraphs": "paragraph",
            "components": "component",
            "ref-rules": "component_ref",
        }
        # Recurse into nested lists if present
        for child_list_name, rtype in type_map.items():
            for child in node.get(child_list_name, []):
                _process_node(child, rule_type=rtype)

    def __update_variables_with_xml_details():
        """Updates the variables in the dataframe with xml details.
            Adds additional column in dataframe - clean variables for LLM eliminating null values from variables list
        """
        updated_variables_list, clean_variables_list_for_llm = [], []
        for idx, row in input_details_df.iterrows():

            variables = copy.deepcopy(row["variables"])
            clean_variables_for_llm = copy.deepcopy(row["variables"])
            for var_idx, var in enumerate(variables):
                if "xml-schema" in var:
                    tag_name = var["xml-schema"]["tag-name"]
                    # Get the sample value and other details if tag name is present
                    if tag_name is not None:
                        sample_row = sample_payload_df[sample_payload_df["element_name"] == tag_name]
                        sample_value = sample_row["value"].iloc[0] if len(sample_row["value"]) > 0 else None

                        variables[var_idx]["xml-schema"]["sample-value"] = sample_value

                        # Get the type and restriction
                        types_row = element_types_df[element_types_df["element_name"] == tag_name]
                        el_type, el_restriction = None, None
                        if len(types_row) != 0:
                            types_row = types_row.iloc[0]
                            if not (pd.isna(types_row["type_restriction"]) or types_row["type_restriction"] is None):
                                el_restriction = types_row["type_restriction"]
                            if not (pd.isna(types_row["type_base"]) or types_row["type_base"] is None):
                                el_type = types_row["type_base"]
                            elif not (pd.isna(types_row["type"]) or types_row["type"] is None):
                                if types_row["type"].startswith("xsd:"):
                                    el_type = types_row["type"]
                            variables[var_idx]["xml-schema"]["xsd-type"] = el_type
                            variables[var_idx]["xml-schema"]["type-restriction"] = el_restriction

                        clean_variables_for_llm[var_idx]["xml-schema"].update(
                            {"sample-value": sample_value} if sample_value is not None else {})
                        clean_variables_for_llm[var_idx]["xml-schema"].update(
                            {"xsd-type": el_type} if el_type is not None else {})
                        clean_variables_for_llm[var_idx]["xml-schema"].update(
                            {"type-restriction": el_restriction} if el_restriction is not None else {})
                    else:
                        clean_variables_for_llm[var_idx].pop("xml-schema")
                else:
                    variables[var_idx]["xml-schema"] = {"tag-name": None}

            # Add the augmented variables list
            updated_variables_list.append(variables)
            clean_variables_list_for_llm.append(clean_variables_for_llm)

        input_details_df["variables"] = updated_variables_list
        input_details_df["clean_variables_list_for_llm"] = clean_variables_list_for_llm

    def __call_model_using_rule(rule_formula, variables_list):
        """
        Generate positive and negative test cases by invoking LLM
        """
        # Create the user input and invoke the LLM
        user_input = config.create_user_input(rule_formula, variables_list)
        output, response = openai_utils.invoke_llm(config.model_config, config.system_instructions, user_input)
        return output, response

    logger.info(f"========================= APPLICATION: {app_report_json["app-name"]} =========================")
    logger.new_line()

    logger.info(f"Generating test data for {len(app_report_json['documents'])} documents and their pages..")
    logger.new_line()

    # Update the variables list with xml tag details if it exists
    __update_variables_with_xml_details()

    # Iterate through the rows of input details TSV to generate test data
    outputs, rules_list = defaultdict(dict), defaultdict(list)
    rule_map = dict()

    if len(input_details_df) <= 100:
        logger.info("Generating test cases sequentially...")
        for idx, row in input_details_df.iterrows():
            # Call the model for the rules and its relevant metadata
            model_output, _ = __call_model_using_rule(row["formula_clean"], row["clean_variables_list_for_llm"])
            outputs[idx] = model_output

            rules = row["custom_id_original"].split("_")
            rules_list[idx] = rules
            for rule_oi in rules:
                rule_map[rule_oi] = (model_output, row["var_to_tag"], row["variables"])

            print(f"\tProcessed {idx+1} out of {len(input_details_df)}")
    else:
        logger.info("Generating test cases via batch processing...")
        batch_details = dict()

        # Create directory to store details related to batch processing
        batch_dir = os.path.join(test_cases_dir, "batch_processing")
        if not os.path.exists(batch_dir):
            os.mkdir(batch_dir)
        logger.info(f"\tBatch related files will be saved in {os.path.abspath(batch_dir)}")

        # Create the input and output directory for batch
        batch_input_dir = os.path.join(batch_dir, "inputs")
        if not os.path.exists(batch_input_dir):
            os.mkdir(batch_input_dir)
        batch_output_dir = os.path.join(batch_dir, "outputs")
        if not os.path.exists(batch_output_dir):
            os.mkdir(batch_output_dir)

        # Create .jsonl file for batch processing input
        input_fnames = openai_utils.create_input_for_batch(input_details_df, config, batch_input_dir)

        for i, input_fname in enumerate(input_fnames):
            logger.new_line()
            logger.info(f"\t===== BATCH {i+1}/{len(input_fnames)} =====")
            batch_details[input_fname] = dict()

            # Upload the batch input file and create the batch
            input_file_obj = openai_utils.upload_file(os.path.join(batch_input_dir, input_fname), purpose="batch")
            batch_details[input_fname]["input_file_obj"] = input_file_obj.to_dict()
            app = os.path.basename(args.app_report_json_path[:-5])
            batch_metadata = {"description": f"TIAA: {app} [Batch {i+1} of {len(input_fnames)}]"}
            batch_obj = openai_utils.create_batch(input_file_obj.id, batch_metadata)

            # Keep checking batch status until completed
            # TODO: This can be taken outside in a different code file
            batch_status = batch_obj.status
            while batch_status != "completed":
                logger.info(f"\t\tBatch status: {batch_status} (Processed {batch_obj.request_counts.completed} of {batch_obj.request_counts.total})..")
                batch_details[input_fname]["batch_obj"] = batch_obj
                if batch_status == "failed":
                    raise Exception(f"[{ERR_BATCH_002}] The input file has failed the validation process when running the batch!")
                if batch_status == "expired":
                    raise Exception(f"[{ERR_BATCH_003}] The batch was not able to complete withing the provided completion window!")
                if batch_status == "cancelled":
                    raise Exception(f"[{ERR_BATCH_004}] The batch was cancelled!")
                time.sleep(10)
                batch_obj = openai_utils.retrieve_batch(batch_obj.id)
                batch_status = batch_obj.status

            batch_details[input_fname]["batch_obj"] = batch_obj.to_dict()
            logger.info(f"\t\tBatch status: {batch_status} (Processed {batch_obj.request_counts.completed} of {batch_obj.request_counts.total})..")
            # Save the batch details to a JSON file
            batch_details_file = os.path.join(batch_dir, "batch_details.json")

            # Get the output file name
            output_fname = f"output_{input_fname}"
            batch_details[input_fname]["output_file"] = output_fname
            output_file_path = os.path.join(batch_output_dir, output_fname)

            with open(batch_details_file, "w") as f:
                json.dump(batch_details, f, indent=4)
            logger.new_line()
            logger.info(f"\t\tBatch details: {os.path.abspath(batch_details_file)}..")

            output_file_id, error_file_id = batch_obj.output_file_id, batch_obj.error_file_id
            output_file_response = openai_utils.retrieve_file_content(output_file_id)
            output_file_response.write_to_file(output_file_path)

            # Read the outputs, validate and save it to a file
            with open(output_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    json_response = json.loads(line.strip())
                    output = json_response["response"]["body"]["output"][0]["content"][0]["text"]
                    output_valid, model_output = openai_utils.validate_output(output)
                    if not output_valid:
                        model_output = {"error": "LLM could not generate valid output"}

                    # Get the var_to_tag and variables from the input_details_df based on custom_id
                    temp_row = input_details_df[input_details_df["custom_id"] == json_response["custom_id"]].iloc[0]
                    temp_row_idx = temp_row.name    # Gets the index from series
                    outputs[temp_row_idx] = model_output

                    # Record the rules from custom_id
                    rules = temp_row["custom_id_original"].split("_")
                    rules_list[temp_row_idx] = rules
                    for rule_oi in rules:
                        rule_map[rule_oi] = (model_output, temp_row["var_to_tag"], temp_row["variables"])

            # Record failed counts and save the error details to file
            err_batch_001_msg = ""
            if batch_obj.request_counts.failed > 0:
                err_batch_001_msg += f"\t\t[{ERR_BATCH_001}] Batch failed for {batch_obj.request_counts.failed} entries.."
            if error_file_id:
                error_file_response = openai_utils.retrieve_file_content(error_file_id)
                error_fpath = os.path.join(batch_output_dir, f"error_{input_fname}")
                error_file_response.write_to_file(os.path.join(batch_output_dir, error_fpath))
                err_batch_001_msg += f"\n\t\tError entries file: {os.path.abspath(error_fpath)}.."
            if err_batch_001_msg:
                logger.new_line()
                logger.error(err_batch_001_msg)

    input_details_df["output"] = pd.Series(outputs)
    input_details_df["rules_list"] = pd.Series(rules_list)
    input_details_df.to_csv(output_tsv, sep="\t", header=True, index=False, quoting=csv.QUOTE_NONE, escapechar="\\",
                            columns=["custom_id", "custom_id_original", "formula_clean", "variables", "var_to_tag", "output", "rules_list"])
    logger.new_line()
    logger.info(f"Model Output TSV: {os.path.abspath(output_tsv)}..")

    # Apply it to every document
    for doc in output_json["documents"]:
        _process_node(doc)

    # Save the output JSON file
    with open(output_json_path, 'w') as file:
        json.dump(output_json, file, indent=4)
    logger.info(f"Test data JSON: {os.path.abspath(output_json_path)}..")
    logger.new_line()

    # Save updated error details
    # Drop duplicates based on rule id and save the errors to a file
    if rule_errors_df is not None:
        rule_errors_df.drop_duplicates(subset=["rule_id", "rule_type"], inplace=True, ignore_index=True)

        # Get total rows with no errors - does not consider total_unmapped_vars
        mask1 = rule_errors_df['unmapped_sections'].apply(lambda x: isinstance(x, list) and x == [])
        mask2 = rule_errors_df['no_vars_in_rule'] == False
        mask3 = rule_errors_df['undefined_rule'] == False
        mask4 = rule_errors_df['unmapped_vars_in_conditions'].apply(lambda x: isinstance(x, list) and x == [])
        no_error_mask = mask1 & mask2 & mask3 & mask4
        temp_errors_df = rule_errors_df.drop(index=rule_errors_df[no_error_mask].index, inplace=False)

        if len(temp_errors_df) > 0:
            # This number can be different from dataframe length itself because it is deduped on rule_ids and types both
            uniq_rules = temp_errors_df["rule_id"].unique().tolist()

            err_rule_001_msg = (f"[{ERR_RULE_001}] Some rules have missing details:"
                                f"\n\tUnique Rules affected due to errors: {len(uniq_rules)}"
                                f"\n\tRule IDs: {', '.join(uniq_rules)}"
                                f"\n\tNOTE: This count does NOT include rules that errors in ONLY total unmapped vars!")
            logger.error(err_rule_001_msg)
            logger.new_line()

        # Get total unmapped variables, unmapped sections, rules with no variables and undefined rules
        unique_unmapped_sections = list(set(item for sublist in rule_errors_df["unmapped_sections"].tolist() for item in sublist))
        rules_with_no_vars = rule_errors_df[rule_errors_df["no_vars_in_rule"] == True]["rule_id"].unique().tolist()
        undefined_rules = rule_errors_df[rule_errors_df["undefined_rule"] == True]["rule_id"].unique().tolist()
        unique_unmapped_vars_in_conditions = list(set(item for sublist in rule_errors_df["unmapped_vars_in_conditions"].tolist() for item in sublist))
        unique_total_unmapped_vars = list(set(item for sublist in rule_errors_df["total_unmapped_vars"].tolist() for item in sublist))

        if len(rules_with_no_vars) > 0:
            err_rule_001_01_msg = (f"\t[{ERR_RULE_001_01}] Rules without variables count: {len(rules_with_no_vars)}"
                                   f"\n\tRules without variables list: {', '.join(rules_with_no_vars)}")
            logger.error(err_rule_001_01_msg)
            logger.new_line()

        if len(undefined_rules) > 0:
            err_rule_001_02_msg = (f"\t[{ERR_RULE_001_02}] Undefined rules count: {len(undefined_rules)}"
                                   f"\n\tUndefined rules list: {', '.join(undefined_rules)}")
            logger.error(err_rule_001_02_msg)
            logger.new_line()

        if len(unique_unmapped_sections) > 0:
            err_rule_001_03_msg = (f"\t[{ERR_RULE_001_03}] Unique unmapped sections count: {len(unique_unmapped_sections)}"
                                   f"\n\tUnique unmapped sections list: {', '.join(unique_unmapped_sections)}")
            logger.error(err_rule_001_03_msg)
            logger.new_line()

        if len(unique_unmapped_vars_in_conditions) > 0:
            err_rule_001_04_msg = (f"\t[{ERR_RULE_001_04}] Unique unmapped variables in conditions count: {len(unique_unmapped_vars_in_conditions)}"
                                   f"\n\tUnique unmapped variables in conditions list: {', '.join(unique_unmapped_vars_in_conditions)}")
            logger.error(err_rule_001_04_msg)
            logger.new_line()

        if len(unique_total_unmapped_vars) > 0:
            err_rule_001_05_msg = (f"\t[{ERR_RULE_001_05}] Unique total unmapped variables count: {len(unique_total_unmapped_vars)}"
                                   f"\n\tUnique total unmapped variables list: {', '.join(unique_total_unmapped_vars)}")
            logger.error(err_rule_001_05_msg)
            logger.new_line()

        rule_errors_df.to_csv(rule_errors_file_path_new, sep="\t", header=True, index=False)
        logger.info(f"Error details file based on LLM output: {os.path.abspath(rule_errors_file_path_new)}")

    logger.info("Testcases were successfully generated for ALL the rules!")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))
