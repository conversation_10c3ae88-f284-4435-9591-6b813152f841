# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-19      #
# --------------------------------- #

"""
Generate complete payload XML with values from provided XSD schemas and input sample XML payload
    -> The complete payload will include ALL the elements that can occur in the XML for given application
    -> The provided XSD schemas are not complete, so there will be some assumptions based on the sample XML
    -> The values for elements are generated based on multiple criteria in the following hierarchy:
        - sample XML, variable values in app report, random generation based on data type and element
            random generation: enum, datetime, pattern, number (int/float), Indicator tags, LLM generated
                Indicator tags: ending in "Ind" with type of string length=1 should have values Y or N
                LLM generated: Based on semantic understanding of element and its path adhering to type in the schema
        - All the generated values are validated against the type in the schema
"""

import logging
import os
import sys

from utils.logging_utils import setup_logging
from utils.payload_utils import CompletePayloadGenerator
from utils.argument_utils import ApplicationArgumentParser
from utils.error_codes import ERR_FILE_001, ERR_FILE_002

LOGGER_NAME = "Complete Payload Generator"
logger = logging.getLogger(LOGGER_NAME)

def parse_arguments(_):
    # Set the default application path directly in this file
    DEFAULT_APP_PATH = "../../applications/Beneficiary Bundling"

    parser = ApplicationArgumentParser("2_get_complete_payload",
                                     "Generate complete payload XML with values from provided XSD schemas and input sample XML payload",
                                     default_application_path=DEFAULT_APP_PATH)

    parser.add_file_argument('main_xsd_path',
                           'xsd/ccp-document-request-v1-types.xsd',
                           'Main XSD file path for the application')
    parser.add_file_argument('app_xsd_path',
                           'xsd/ccp-document-request-benebund-v1.xsd',
                           'XSD file path specifying the application request')
    parser.add_file_argument('sample_xml_path',
                           'PTRKBBWI Input.xml',
                           'Sample XML instance file path')
    parser.add_file_argument('mapping_path',
                           'xml_parse/app_info/xml_tags_to_variables.tsv',
                           'Var-Tag mapping TSV file path')
    parser.add_file_argument('testcase_generation_details_tsv',
                           'xml_parse/app_info/testcase_generation_details.tsv',
                           'TSV containing test case generation details')

    return parser.parse_args()

def main(args):

    # Configure logging
    log_dir = os.path.join(os.path.dirname(args.sample_xml_path), "logs")
    if not os.path.exists(log_dir):
        os.mkdir(log_dir)
    log_file = "2_complete_payload_generator.log"
    log_file_path = os.path.join(log_dir, log_file)
    setup_logging(log_file=log_file_path, level=logging.DEBUG, max_bytes=1 * 1024 * 1024, backup_count=3, app_name=LOGGER_NAME)

    try:
        # Check for input arguments
        if not os.path.exists(args.main_xsd_path):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.main_xsd_path}")
        if not os.path.exists(args.app_xsd_path):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.app_xsd_path}")
        if not os.path.exists(args.sample_xml_path):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.sample_xml_path}")
        if not os.path.exists(args.mapping_path):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.mapping_path}")
        if not os.path.exists(args.testcase_generation_details_tsv):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.testcase_generation_details_tsv}")

        # Create output path directory
        output_dir_path = os.path.join(os.path.dirname(args.sample_xml_path), "complete_payload")
        if os.path.exists(output_dir_path):
            raise FileExistsError(f"[{ERR_FILE_002}] Directory already exists: {os.path.abspath(output_dir_path)}!")
        os.mkdir(output_dir_path)

        payload_generator = CompletePayloadGenerator(args.main_xsd_path, args.app_xsd_path, args.sample_xml_path,
                                                     args.mapping_path, args.testcase_generation_details_tsv, output_dir_path, logger)
        payload_generator.run()

    except Exception:
        logger.exception("")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))
