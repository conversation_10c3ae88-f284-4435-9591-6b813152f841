# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-29      #
# --------------------------------- #

"""
Generate XML files for positive and negative test cases using XSD file for that application
    Requires JSON application report with test cases generated using 2_generate_test_cases.py and XSD file
"""

import argparse
import ast
import csv
import json
import os
import sys

import pandas as pd
import xmlschema
from lxml import etree

from utils.misc_utils import create_xml_from_xsd

# Use a flag to indicate if you want to include component rules
INCLUDE_COMPONENTS = True

def parse_arguments(_):
    parser = argparse.ArgumentParser()

    parser.add_argument('--test_cases_json_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xml_parse/test_cases/Beneficiary Bundling (BB) - TNG PTRKBBWI & PTRKBBAT - AFP AppReport_test_cases.json',
                        help='''JSON application report containing test cases''')
    parser.add_argument('--xsd_file_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/temp/flattened-types.xsd',
                        help='''XSD file specific to the application''')
    parser.add_argument('--element_types_tsv_path', type=str, required=False,
                        default='../../../applications/Beneficiary Bundling/complete_payload/element_types.tsv',
                        help='''Path of the TSV file containing element type details''')

    return parser.parse_args()


def main(args):
    """Check for input arguments"""
    if not os.path.exists(args.test_cases_json_path):
        raise FileNotFoundError(f"File not found: {args.test_cases_json_path}")
    if not os.path.exists(args.xsd_file_path):
        raise FileNotFoundError(f"File not found: {args.xsd_file_path}")
    if not os.path.exists(args.element_types_tsv_path):
        raise FileNotFoundError(f"File not found: {args.element_types_tsv_path}")

    # Create directory for XML files and prepare the output file path
    xml_files_dir = os.path.join(os.path.dirname(args.test_cases_json_path), "xmls")
    xml_dir = {
        "positives": os.path.join(xml_files_dir, "positives"),
        "negatives": os.path.join(xml_files_dir, "negatives")
    }
    for dir_path in xml_dir.values():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

    # Load the JSON file containing test cases data
    with open(args.test_cases_json_path, 'r') as json_file:
        test_cases_data = json.load(json_file)

    # Read the element types TSV
    element_types_df = pd.read_csv(args.element_types_tsv_path, sep='\t', header=0, index_col=None,
                                   quoting=csv.QUOTE_NONE, escapechar="\\")
    element_types_df["info"] = element_types_df["info"].apply(ast.literal_eval)
    element_types_df['maxOccurs'] = element_types_df['info'].apply(lambda d: d['maxOccurs'])

    # Load the schema from XSD file
    schema = xmlschema.XMLSchema(args.xsd_file_path)
    xsd_tree = etree.parse(args.xsd_file_path)

    # Get namespaces from the XSD tree
    xsd_root = xsd_tree.getroot()
    namespaces = xsd_root.nsmap

    # Iterate through each test case in the documents and pages to create XML files
    combined_result_data = []
    for document in test_cases_data["documents"]:
        tsv_entries = create_xml_from_xsd(document, schema, xsd_tree, element_types_df, namespaces, xml_dir, include_components=INCLUDE_COMPONENTS)
        combined_result_data.extend(tsv_entries)

    # Save the combined result data to TSV dataframe
    tsv_path = os.path.splitext(args.test_cases_json_path)[0] + ".tsv"
    columns = ["xml_file", "payload_type", "payload", "document", "page", "component", "component_ref"]
    df = pd.DataFrame(combined_result_data, columns=columns)
    df.to_csv(tsv_path, sep="\t", header=True, index=False)
    print(f"\nSaved the combined test cases details to a TSV:\n{os.path.abspath(tsv_path)}")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))

