# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-18      #
# --------------------------------- #

"""
Builds a complete schema by adding relevant application request schema based on sample payload XML
    Picks from either DocumentInfo or DocumentInfoXML based on the sample XML
"""

import argparse
from lxml import etree
import sys
import os


def parse_arguments(_):
    parser = argparse.ArgumentParser()

    parser.add_argument('--main_xsd_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/ccp-document-request-v1-types.xsd',
                        help='''Main XSD file path for the application''')
    parser.add_argument('--app_xsd_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/ccp-document-request-benebund-v1.xsd',
                        help='''XSD file path specifying the application request''')
    parser.add_argument('--sample_xml_path', type=str,
                        default="../../../applications/Beneficiary Bundling/PTRKBBWI Input.xml",
                        help='Sample XML instance file path')

    return parser.parse_args()


def detect_app_namespace(app_elem, app_schema_path):
    # 1. Directly from the element’s own tag
    ns = etree.QName(app_elem).namespace
    if ns:
        return ns

    # 2. Scan children for the first namespace you see
    for child in app_elem.iter():
        ns = etree.QName(child).namespace
        if ns:
            return ns

    # 3. Fallback: read the XSD’s targetNamespace
    parser = etree.XMLParser(remove_blank_text=True)
    app_schema = etree.parse(app_schema_path, parser).getroot()
    return app_schema.get('targetNamespace')

def augment_schema(main_schema_path, app_schema_path, xml_instance_path, output_path):
    # Parse XML instance payload
    inst_tree = etree.parse(xml_instance_path)
    inst_root = inst_tree.getroot()

    # Determine which branch to augment
    if inst_root.find('.//DocumentInfoXml') is not None:
        branch = 'DocumentInfoXml'
    elif inst_root.find('.//DocumentInfo') is not None:
        branch = 'DocumentInfo'
    else:
        raise ValueError("Neither DocumentInfoXml nor DocumentInfo found in the XML instance payload")

    # Identify the application-specific element under the chosen branch
    for elem in inst_root.iter():
        if etree.QName(elem).localname == branch:
            # The first child inside this branch is the application element
            children = list(elem)
            if not children:
                raise ValueError(f"No child elements found under {branch}")
            app_elem = children[0]
            app_elem_name = etree.QName(app_elem).localname
            # Determine the namespace URI for the application element
            app_ns = detect_app_namespace(app_elem, app_schema_path)
            break
    else:
        raise ValueError(f"Unable to locate the {branch} element in the XML instance payload")

    # Parse main schema
    parser = etree.XMLParser(remove_blank_text=True)
    main_tree = etree.parse(main_schema_path, parser)
    old_root = main_tree.getroot()
    ns_uri = old_root.tag.split("}")[0].lstrip("{")

    # Rebuild root element with updated nsmap (including 'app')
    new_nsmap = old_root.nsmap.copy()
    if 'app' in new_nsmap:
        raise ValueError("Namespace prefix 'app' is already in use in the main schema nsmap")
    new_nsmap['app'] = app_ns

    # Create new root with combined nsmap
    new_root = etree.Element(old_root.tag, nsmap=new_nsmap)
    # Copy non-xmlns attributes
    for attr_name, attr_val in old_root.attrib.items():
        if not attr_name.startswith('{http://www.w3.org/2000/xmlns/}'):
            new_root.set(attr_name, attr_val)
    # Append all children under the new root
    for child in old_root:
        new_root.append(child)

    # Replace the document root
    main_tree._setroot(new_root)
    main_root = new_root

    # Create and insert an <xs:import> for the application schema
    if app_ns:
        import_elem = etree.Element(f'{{{ns_uri}}}import')
        import_elem.set('namespace', app_ns)
        import_elem.set('schemaLocation', os.path.basename(app_schema_path))

        # Insert after existing <xs:include> or <xs:import> elements
        insert_index = 0
        for idx, child in enumerate(main_root):
            if child.tag in (f'{{{ns_uri}}}include', f'{{{ns_uri}}}import'):
                insert_index = idx + 1
        main_root.insert(insert_index, import_elem)

    # Locate the xs:choice within the DocumentRequest complexType
    choice_path = (
        "./{XSD}complexType[@name='DocumentRequest']/{XSD}complexContent/"
        "{XSD}extension/{XSD}sequence/{XSD}choice"
    ).replace('{XSD}', f'{{{ns_uri}}}')
    choice = main_root.find(choice_path)
    if choice is None:
        raise ValueError("Could not locate the xs:choice for DocumentRequest in the main schema")

    # Modify the target element's type to reference the application type
    target_elem = choice.find(f"{{{ns_uri}}}element[@name='{branch}']")
    if target_elem is None:
        raise ValueError(f"Element '{branch}' not found in the xs:choice")
    target_elem.attrib.pop('type', None)

    # Create an anonymous complexType wrapping the application root element
    complex_type = etree.SubElement(target_elem, f'{{{ns_uri}}}complexType')
    seq = etree.SubElement(complex_type, f'{{{ns_uri}}}sequence')
    # Reference the application element by ref
    ref_elem = etree.SubElement(seq, f'{{{ns_uri}}}element')
    ref_attr = f'app:{app_elem_name}' if app_ns else app_elem_name
    ref_elem.set('ref', ref_attr)

    # Write the augmented schema to output
    main_tree.write(output_path, pretty_print=True, xml_declaration=True, encoding='UTF-8')


def main(args):
    """Check for input arguments"""
    if not os.path.exists(args.main_xsd_path):
        raise FileNotFoundError(f"File not found: {args.main_xsd_path}")
    if not os.path.exists(args.app_xsd_path):
        raise FileNotFoundError(f"File not found: {args.app_xsd_path}")
    if not os.path.exists(args.sample_xml_path):
        raise FileNotFoundError(f"File not found: {args.sample_xml_path}")

    # Get the output schema path
    output_schema_path = os.path.join(os.path.dirname(args.main_xsd_path), "complete-schema.xsd")

    augment_schema(args.main_xsd_path, args.app_xsd_path, args.sample_xml_path, output_schema_path)
    print(f"Generated complete schema with application request:\n{os.path.abspath(output_schema_path)}")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))

