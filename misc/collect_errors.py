# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-12      #
# --------------------------------- #

"""
Collect error cases encountered when generating test cases based on the rule
    Requires JSON application report with test cases generated using 2_generate_test_cases.py
    Saves a TSV file containing error details in the following columns:
        rule, rule_type, unmapped_vars, unmapped_sections, no_vars_in_rule
"""

import argparse
import json
import os
import sys

import pandas as pd


def parse_arguments(_):
    parser = argparse.ArgumentParser()

    parser.add_argument('--test_cases_json_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xml_parse/test_cases/Beneficiary Bundling (BB) - TNG PTRKBBWI & PTRKBBAT - AFP AppReport_test_cases.json',
                        help='''JSON application report''')

    return parser.parse_args()


def extract_error(rule_obj, rule_type):
    """
    Given a rule object and its type (document/page/component/component_ref),
    return an entry list if there was an error, otherwise None.
    """
    if not rule_obj:
        return None

    status = rule_obj.get("test-cases-status", {})
    if not status["success"]:
        error = status.get("error", {})
        return [
            rule_obj.get("rule-oi"),
            rule_type,
            error.get("unmapped-variables"),
            error.get("unmapped-sections"),
            error.get("no-variables-in-rule"),
        ]

    return None


def main(args):
    """Check for input arguments"""
    if not os.path.exists(args.test_cases_json_path):
        raise FileNotFoundError(f"File not found: {args.test_cases_json_path}")

    with open(args.test_cases_json_path, 'r') as json_file:
        test_cases_data = json.load(json_file)

    # Get the file path to save error information
    error_file_path = os.path.join(os.path.dirname(args.test_cases_json_path), "error_log.tsv")
    errors = []

    # Iterate through the test cases JSON file and collect the errors
    # Document-level
    print("Collecting error cases...")
    for doc in test_cases_data.get("documents"):
        entry = extract_error(doc.get("rule"), "document")
        if entry:
            errors.append(entry)

        # Page-level
        for page in doc.get("pages"):
            entry = extract_error(page.get("rule"), "page")
            if entry:
                errors.append(entry)

            for component in page.get("components"):
                # Component-level
                entry = extract_error(component.get("rule"), "component")
                if entry:
                    errors.append(entry)

                # Component‐ref‐rule level
                for comp_ref_rule in component.get("ref-rules"):
                    entry = extract_error(comp_ref_rule, "component_ref")
                    if entry:
                        errors.append(entry)

    # Drop duplicates based on rule id and save the errors to a file
    columns = ["rule", "rule_type", "unmapped_vars", "unmapped_sections", "no_vars_in_rule"]
    errors_df = pd.DataFrame(errors, columns=columns)
    errors_df.drop_duplicates(subset=["rule", "rule_type"], inplace=True, ignore_index=True)

    errors_df.to_csv(error_file_path, sep="\t", header=True, index=False)
    print(f"\nSaved the error details with associated rules to a TSV:\n{os.path.abspath(error_file_path)}")

    # Get total unmapped variables, unmapped sections and rules with no variables to save in a separate text file
    unique_unmapped_vars = list(set(item for sublist in errors_df["unmapped_vars"].tolist() for item in sublist))
    unique_unmapped_sections = list(set(item for sublist in errors_df["unmapped_sections"].tolist() for item in sublist))
    rules_with_no_vars = errors_df[errors_df["no_vars_in_rule"] == True]["rule"].unique().tolist()
    error_file_path_txt = os.path.splitext(error_file_path)[0] + ".txt"
    with open(error_file_path_txt, "w") as error_file:
        error_file.write(f"Unique unmapped variables count: {len(unique_unmapped_vars)}")
        error_file.write(f"\nUnique unmapped variables list:\n{', '.join(unique_unmapped_vars)}")
        error_file.write("\n")
        error_file.write(f"\nUnique unmapped sections count: {len(unique_unmapped_sections)}")
        error_file.write(f"\nUnique unmapped sections list:\n{', '.join(unique_unmapped_sections)}")
        error_file.write("\n")
        error_file.write(f"\nRules without variables count: {len(rules_with_no_vars)}")
        error_file.write(f"\nRules without variables list:\n{', '.join(rules_with_no_vars)}")
    print(f"\nSaved the error details with unique unmapped variables, sections and rules without variables to a TXT:"
          f"\n{os.path.abspath(error_file_path_txt)}")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))

