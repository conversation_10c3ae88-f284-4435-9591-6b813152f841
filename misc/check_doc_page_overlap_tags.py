# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-28      #
# --------------------------------- #

"""
Check if tags overlap in document and page rules
"""


import argparse
import json
import os
import sys

def parse_arguments(_):
    parser = argparse.ArgumentParser()

    parser.add_argument('--test_cases_json_path', type=str,
                        default='../../applications/ACKLET/html_parse/test_cases/ACKLET_test_cases.json',
                        help='''JSON application report containing generated test cases''')

    return parser.parse_args()

def main(args):
    """Check for input arguments"""
    if not os.path.exists(args.test_cases_json_path):
        raise FileNotFoundError(f"File not found: {args.test_cases_json_path}")

    # Read the json file
    with open(args.test_cases_json_path, "r") as f:
        data = json.load(f)

    documents = data["documents"]
    overlapping_tags = []

    for doc_idx, document in enumerate(documents):
        document_tags = set()
        if document["rule"]:
            document_test_cases = document["rule"]["generated-test-cases"]
            if "error" not in document_test_cases:
                for test_case_type in document_test_cases:
                    for test_data in document_test_cases[test_case_type]:
                        if test_data["data_with_tags"]:
                            tags = test_data["data_with_tags"].keys()
                            document_tags.update(tags)

        for page_idx, page in enumerate(document["pages"]):
            page_tags = set()
            if page["rule"]:
                page_test_cases = page["rule"]["generated-test-cases"]
                if "error" not in page_test_cases:
                    for test_case_type in page_test_cases:
                        for test_data in page_test_cases[test_case_type]:
                            if test_data["data_with_tags"]:
                                tags = test_data["data_with_tags"].keys()
                                page_tags.update(tags)

                # Check if document and page tags have common xml tags
                common_tags = page_tags.intersection(document_tags)
                if common_tags:
                    overlapping_tags.append({
                        "document_name": document['name'],
                        "page_name": page['name'],
                        "common_tags": common_tags,
                    })

    if overlapping_tags:
        print("Below are the details of overlapping tags:\n")
        for t in overlapping_tags:
            print(f"Document: {t['document_name']}")
            print(f"Page: {t['page_name']}")
            print(f"Common Tags: {', '.join(t['common_tags'])}")
            print("\n\n")
    else:
        print("No overlapping tags found for the given application!")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))
