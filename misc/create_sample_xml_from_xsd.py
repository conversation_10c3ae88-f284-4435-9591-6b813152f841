# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-06      #
# --------------------------------- #

"""
Creates sample XML from xsD file. The sample values are generated based on their provided data type and restrictions
Limitation: Considers elements under choice as sequential elements and includes all those choices in the sample
"""

import argparse
import random
import re

import pandas as pd
from lxml import etree
import sys
import os
from datetime import date, datetime
import rstr

from utils.openai_utils import OpenAIUtils
from utils.prompts.sample_element_value_generation import ElementValueGenerationConfig

records = []
xs = ""
values_map_app_report = dict()
values_map_sample_payload = dict()
element_types_df = pd.DataFrame()

# Built-in type groups
INT_TYPES = {"integer", "int", "long", "short", "byte", "nonPositiveInteger",
             "negativeInteger", "nonNegativeInteger", "unsignedLong", "unsignedInt",
             "unsignedShort", "unsignedByte", "positiveInteger"}
FLOAT_TYPES = {"decimal", "float", "double"}
STRING_TYPES = {"string", "normalizedString", "token", "language", "Name", "NCName",
                "ID", "IDREF", "IDREFS", "ENTITY", "ENTITIES"}

# Unified map: values are either literals or zero-arity functions
_TYPE_GENERATORS = {
    # Booleans & zeros
    "boolean": "true",
    **{t: "0" for t in INT_TYPES},
    **{t: "0.0" for t in FLOAT_TYPES},

    # Date/time / g-X types
    "date":                   lambda: date.today().isoformat(),
    "dateTime":               lambda: datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
    "time":                   lambda: datetime.now().strftime("%H:%M:%S"),
    "gYear":                  lambda: str(date.today().year),
    "gMonth":                 lambda: f"--{date.today().month:02d}",
    "gDay":                   lambda: f"---{date.today().day:02d}",
    "gYearMonth":             lambda: date.today().strftime("%Y-%m"),
    "gMonthDay":              lambda: date.today().strftime("--%m-%d"),

    # Durations
    "duration":               "P1DT0H0M0S",
    "yearMonthDuration":      "P1Y",
    "dayTimeDuration":        "P1DT0H0M0S",
}

# Set up element value generation related parameters (specially for elements where value is undetermined)
config = ElementValueGenerationConfig()
model = OpenAIUtils()


def parse_arguments(_):
    parser = argparse.ArgumentParser()

    parser.add_argument('--xsd_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/complete-schema.xsd',
                        help='''Main xsD file path for the application''')
    parser.add_argument('--mapping_file_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xml_parse/app_info/xml_tags_to_variables.tsv',
                        help='''Variable-XML tag mapping file to get sample values''')
    parser.add_argument('--element_types_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/element-types.tsv',
                        help='''Path of the TSV file containing element type details (from get_element_types.py)''')

    return parser.parse_args()


def load_xsd(xsd_path, processed_paths=None):
    """
    Parse the XSD at xsd_path plus any included/imported XSDs, collecting all
    global <xs:element>, <xs:complexType> and <xs:simpleType> definitions.

    Args:
      xsd_path (str): path to the primary XSD file
      processed_paths (set): absolute paths already loaded (for recursion)

    Returns:
      root: the etree root of the primary schema
      elements: dict name -> <xs:element> node
      complex_types: dict name -> <xs:complexType> node
      simple_types: dict name -> <xs:simpleType> node
    """
    # Initialize the processed set
    if processed_paths is None:
        processed_paths = set()
    xsd_abspath = os.path.abspath(xsd_path)
    if xsd_abspath in processed_paths:
        # already loaded; return empty dicts so merges do nothing
        return None, {}, {}, {}

    processed_paths.add(xsd_abspath)

    # Parse this schema
    tree = etree.parse(xsd_abspath)
    root = tree.getroot()
    # Determine the XSD namespace (e.g. "{http://www.w3.org/2001/XMLSchema}")
    namespace_uri = root.tag.split("}")[0].lstrip("{")
    global xs
    xs = f"{{{namespace_uri}}}"

    # Prepare containers for this file + any transitively included/imported ones
    elements = {}
    complex_types = {}
    simple_types = {}

    # 1) First, recurse into any <xs:include> or <xs:import>
    for child in root:
        if child.tag in (xs + "include", xs + "import"):
            schema_loc = child.get("schemaLocation")
            if schema_loc:
                # Resolve path relative to current XSD
                include_path = os.path.join(os.path.dirname(xsd_abspath), schema_loc)
                _, sub_elems, sub_cts, sub_sts = load_xsd(include_path, processed_paths)
                elements.update(sub_elems)
                complex_types.update(sub_cts)
                simple_types.update(sub_sts)

    # 2) Now collect this file’s own globals
    for child in root:
        if child.tag == xs + "element":
            name = child.get("name")
            if name:
                elements[name] = child
        elif child.tag == xs + "complexType":
            name = child.get("name")
            if name:
                complex_types[name] = child
        elif child.tag == xs + "simpleType":
            name = child.get("name")
            if name:
                simple_types[name] = child

    return root, elements, complex_types, simple_types

def generate_from_pattern(pattern_str):
    pattern_str = rf"^{pattern_str}$"
    result = rstr.xeger(pattern_str)
    return result

def generate_value_using_llm(xml_el):
    el_name = xml_el.tag
    el_path = xml_el.getroottree().getpath(xml_el)

    # Get the type and restriction from element_types_df
    temp_row = element_types_df[element_types_df["element_name"] == el_name]
    el_type, el_restriction = "", ""
    if len(temp_row) != 0:
        temp_row = temp_row.iloc[0]
        if not pd.isna(temp_row["type_restriction"]) or temp_row["type_restriction"] is not None:
            el_restriction = temp_row["type_restriction"]
        if not pd.isna(temp_row["type_base"]) or temp_row["type_base"] is not None:
            el_type = temp_row["type_base"]
        elif not pd.isna(temp_row["type"]) or temp_row["type"] is not None:
            if temp_row["type"].startswith("xsd:"):
                el_type = temp_row["type"]

    # Create user input for the LLM and call the model
    user_input, json_schema_details = config.create_user_input(el_name, el_path, el_type, el_restriction)
    output, response = model.invoke_llm(config.model_config, config.system_instructions, user_input, json_schema_details)

    # Get the value from the output
    value = output[el_name] if isinstance(output, dict) and el_name in output else None

    return value

def validate_builtin_value(type_local, value):
    """
    Attempt to validate a primitive value against built-in XSD type.
    Returns True if the value can be parsed/converted appropriately.
    """
    try:
        if type_local == "boolean":
            return str(value).lower() in ("true", "false")
        if type_local in INT_TYPES:
            int(value)
            return True
        if type_local in FLOAT_TYPES:
            float(value)
            return True
        # Date/Time types
        if type_local == "date":
            date.fromisoformat(value)
            return True
        if type_local == "dateTime":
            datetime.fromisoformat(value)
            return True
        if type_local == "time":
            datetime.strptime(value, "%H:%M:%S")
            return True
        if type_local in ("gYear", "gMonth", "gDay", "gYearMonth", "gMonthDay"):
            patterns = {
                "gYear": r"^-?\d{4}(Z|([+\-]\d{2}:\d{2}))?$",
                "gMonth": r"^--\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                "gDay": r"^---\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                "gYearMonth": r"^-?\d{4}-\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                "gMonthDay": r"^--\d{2}-\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
            }
            pattern = patterns[type_local]
            return bool(re.fullmatch(pattern, value))
        # Duration types: basic check for leading 'P'
        if type_local.endswith("Duration") or type_local == "duration":
            return str(value).startswith("P")
        # String-like types: accept any string
        if type_local in STRING_TYPES or type_local.lower().endswith("string"):
            str(value)
            return True
    except Exception:
        return False
    # Fallback: accept the value
    return True

def validate_simple_type(xsd_simple, value):
    """
    Validate a string value against an <xs:simpleType> restriction.
    Returns True if it satisfies enumeration, pattern, length, or numeric facets.
    """
    restriction = xsd_simple.find(xs + "restriction")
    if restriction is None:
        return True

    # Enumerations
    enums = [e.get("value") for e in restriction.findall(xs + "enumeration")]
    if enums:
        return value in enums

    # Pattern facet
    pattern = restriction.find(xs + "pattern")
    if pattern is not None and pattern.get("value"):
        return re.fullmatch(pattern.get("value"), value) is not None

    # Numeric facets
    base = restriction.get("base")
    base_local = base.split(":")[-1] if base else None
    td = restriction.find(xs + "totalDigits")
    fd = restriction.find(xs + "fractionDigits")
    if (base_local in INT_TYPES | FLOAT_TYPES) and (td is not None or fd is not None):
        # Basic check: use built-in validation
        return validate_builtin_value(base_local, value)

    # Length facets
    length = restriction.find(xs + "length")
    if length is not None:
        try:
            return len(value) == int(length.get("value"))
        except:
            pass
    min_len = restriction.find(xs + "minLength")
    max_len = restriction.find(xs + "maxLength")
    if min_len is not None or max_len is not None:
        try:
            val_len = len(value)
            if min_len is not None and val_len < int(min_len.get("value")):
                return False
            if max_len is not None and val_len > int(max_len.get("value")):
                return False
            return True
        except:
            pass

    # Fallback to base type validation
    if base_local:
        return validate_builtin_value(base_local, value)

    return False

def record_value(xml_el, strategy):
    """
    Record the element's name, its full XPath, text value, and strategy.
    """
    # compute the full XPath from the tree root
    path = xml_el.getroottree().getpath(xml_el)
    records.append({
        "element_name": xml_el.tag,
        "path": path,
        "value": xml_el.text or "",
        "strategy": strategy
    })

def generate_builtin_type(type_local, parent_xml, el_name):
    """
    Generate or override a sample value for an xsD built-in primitive type dynamically,
    without relying on a hard-coded mapping. If no restrictions are present,
    choose a reasonable default based on the type name.
    """
    # Use sample override from app report if valid (first from sample payload, then app report
    if el_name in values_map_sample_payload and validate_builtin_value(type_local, values_map_sample_payload[el_name]):
        value, strategy = str(values_map_sample_payload[el_name]), "sample_payload"

    elif el_name in values_map_app_report and validate_builtin_value(type_local, values_map_app_report[el_name]):
        value, strategy = str(values_map_app_report[el_name]), "app_report"

    else:
        generator = _TYPE_GENERATORS.get(type_local)
        if generator is not None:
            value = generator() if callable(generator) else generator
            if type_local == "boolean":
                strategy = "randomly_generated_boolean"
            elif type_local in INT_TYPES | FLOAT_TYPES:
                strategy = "randomly_generated_number"
            else:
                strategy = "randomly_generated_datetime"

        # string
        elif type_local in STRING_TYPES or type_local.lower().endswith("string"):
            value = generate_value_using_llm(parent_xml)
            if value is not None:
                strategy = "randomly_generated_llm"
            else:
                value = f"sample_{el_name}"
                strategy = "randomly_generated"
        # fallback
        else:
            value = generate_value_using_llm(parent_xml)
            if value is not None:
                strategy = "randomly_generated_llm"
            else:
                value = f"sample_{el_name}"
                strategy = "randomly_generated"

    # assign & record the strategy for the element
    parent_xml.text = value
    record_value(parent_xml, strategy)

def generate_simple_type(xsd_simple, parent_xml, el_name):
    """
    Given an <xs:simpleType> node, generate a sample text based on its <xs:restriction> or override:
      - If there are <xs:enumeration> facets, pick the first value.
      - If there's a pattern, generate a matching string via generate_from_pattern().
      - If there's totalDigits and/or fractionDigits, choose a value respecting them.
      - If there are length facets (minLength, maxLength, length), pick a string between those bounds.
      - Otherwise, if its base is a built-in primitive, defer to generate_builtin_type.
    Inserts text into parent_xml.
    """
    # Sample override from sample payload
    if el_name in values_map_sample_payload and validate_simple_type(xsd_simple, values_map_sample_payload[el_name]):
        parent_xml.text = values_map_sample_payload[el_name]
        record_value(parent_xml, "sample_payload")
        return

    # Sample override from app report
    if el_name in values_map_app_report and validate_simple_type(xsd_simple, values_map_app_report[el_name]):
        parent_xml.text = values_map_app_report[el_name]
        record_value(parent_xml, "app_report")
        return

    restriction = xsd_simple.find(xs + "restriction")
    if restriction is None:
        value = generate_value_using_llm(parent_xml)
        if value is not None:
            strategy = "randomly_generated_llm"
        else:
            value = f"sample_{el_name}"
            strategy = "randomly_generated"

        parent_xml.text = value
        record_value(parent_xml, strategy)
        return

    # 1) Enumerations
    enums = restriction.findall(xs + "enumeration")
    if enums:
        value = enums[0].get("value", f"sample_{el_name}")
        parent_xml.text = value
        if value == f"sample_{el_name}":
            record_value(parent_xml, "randomly_generated")
        else:
            record_value(parent_xml, "randomly_generated_enum")
        return

    # 2) Pattern facet
    pattern = restriction.find(xs + "pattern")
    if pattern is not None:
        pat_val = pattern.get("value")
        if pat_val:
            parent_xml.text = generate_from_pattern(pat_val)
            record_value(parent_xml, "randomly_generated_pattern")
            return

    # Determine base
    base = restriction.get("base")
    base_local = base.split(":")[-1] if base else None

    # 3) Numeric-based restrictions: totalDigits / fractionDigits
    if base_local in INT_TYPES | FLOAT_TYPES:
        td = restriction.find(xs + "totalDigits")
        fd = restriction.find(xs + "fractionDigits")
        if td is not None:
            td_val = int(td.get("value"))
            fd_val = int(fd.get("value")) if fd is not None else 0
            if base_local in INT_TYPES:
                parent_xml.text = "1" * td_val
                record_value(parent_xml, "randomly_generated_number")
                return
            int_digits = td_val - fd_val
            integer_part = "0" if int_digits <= 0 else "1" + "0" * (int_digits - 1)
            fraction_part = "0" * fd_val
            parent_xml.text = f"{integer_part}.{fraction_part}" if fd_val > 0 else integer_part
            record_value(parent_xml, "randomly_generated_number")
            return
        if fd is not None:
            fd_val = int(fd.get("value"))
            if base_local in FLOAT_TYPES:
                fraction_part = "1" + "0" * (fd_val - 1) if fd_val > 0 else "0"
                parent_xml.text = f"0.{fraction_part}"
                record_value(parent_xml, "randomly_generated_number")
                return

    # 4) Length constraints for indicator variables
    min_len = restriction.find(xs + "minLength")
    max_len = restriction.find(xs + "maxLength")
    try:
        min_val = int(min_len.get("value")) if min_len is not None else 1
        max_val = int(max_len.get("value")) if max_len is not None else min_val

        # Special case for variables ending in "Ind" (indicators) and of type string with max length = 1
        # The values for the indicators are mostly either Y or N
        if max_val == 1 and el_name.endswith("Ind"):
            value = random.choice(("Y", "N"))
            strategy ="randomly_generated_Ind"
        else:
            value = generate_value_using_llm(parent_xml)
            if value is not None:
                strategy = "randomly_generated_llm"
            else:
                value = f"sample_{el_name}"
                strategy = "randomly_generated"

        parent_xml.text = value
        record_value(parent_xml, strategy)
        return

    except (ValueError, TypeError):
        pass

    # 5) Built-in fallback
    if base_local:
        generate_builtin_type(base_local, parent_xml, el_name)
        return

    # 6) Fallback generic
    value = generate_value_using_llm(parent_xml)
    if value is not None:
        strategy = "randomly_generated_llm"
    else:
        value = f"sample_{el_name}"
        strategy = "randomly_generated"
    parent_xml.text = value
    record_value(parent_xml, strategy)

def generate_element(xsd_element, elements_dict, complex_types_dict, simple_types_dict, parent_xml):
    """
    Given an <xs:element> node (either global or local) and a parent XML node,
    create the corresponding XML element (with dummy or constrained text, or nested children).
    Recurses into complexTypes, simpleTypes, built-in types, extensions, references, etc.
    """
    ref = xsd_element.get("ref")
    if ref is not None:
        local_name = ref.split(":")[-1]
        if local_name in elements_dict:
            xsd_element = elements_dict[local_name]
            el_name = local_name
        else:
            el_name = local_name
    else:
        el_name = xsd_element.get("name")

    xml_el = etree.SubElement(parent_xml, el_name)

    type_attr = xsd_element.get("type")
    # Sample override at element level from sample payload
    if el_name in values_map_sample_payload and type_attr:
        type_local = type_attr.split(":")[-1]
        if type_local in simple_types_dict and validate_simple_type(simple_types_dict[type_local], values_map_sample_payload[el_name]):
            xml_el.text = values_map_sample_payload[el_name]
            record_value(xml_el, "sample_payload")
            return xml_el
        if type_local not in complex_types_dict and type_local not in simple_types_dict:
            if validate_builtin_value(type_local, values_map_sample_payload[el_name]):
                xml_el.text = str(values_map_sample_payload[el_name])
                record_value(xml_el, "sample_payload")
                return xml_el

    # Sample override at element level from app report
    if el_name in values_map_app_report and type_attr:
        type_local = type_attr.split(":")[-1]
        if type_local in simple_types_dict and validate_simple_type(simple_types_dict[type_local],
                                                                    values_map_app_report[el_name]):
            xml_el.text = values_map_app_report[el_name]
            record_value(xml_el, "app_report")
            return xml_el
        if type_local not in complex_types_dict and type_local not in simple_types_dict:
            if validate_builtin_value(type_local, values_map_app_report[el_name]):
                xml_el.text = str(values_map_app_report[el_name])
                record_value(xml_el, "app_report")
                return xml_el

    if type_attr is not None:
        type_local = type_attr.split(":")[-1]

        if type_local in elements_dict:
            # grab that global element
            ref_el = elements_dict[type_local]
            # first, if it has a type="SomethingType", drill into that
            alias_type = ref_el.get("type")
            if alias_type:
                alias_local = alias_type.split(":")[-1]
                if alias_local in complex_types_dict:
                    generate_complex_type(
                        complex_types_dict[alias_local],
                        elements_dict, complex_types_dict, simple_types_dict,
                        xml_el
                    )
                    return xml_el
                if alias_local in simple_types_dict:
                    generate_simple_type(
                        simple_types_dict[alias_local],
                        xml_el, el_name
                    )
                    return xml_el
            # otherwise, maybe inline <xs:complexType> or <xs:simpleType> on the referenced element:
            for child in ref_el:
                if child.tag == xs + "complexType":
                    generate_complex_type(child,
                                          elements_dict, complex_types_dict, simple_types_dict,
                                          xml_el
                                          )
                    return xml_el
                elif child.tag == xs + "simpleType":
                    generate_simple_type(child, xml_el, el_name)
                    return xml_el

        if type_local in complex_types_dict:
            generate_complex_type(complex_types_dict[type_local], elements_dict, complex_types_dict, simple_types_dict,
                                  xml_el)
        elif type_local in simple_types_dict:
            generate_simple_type(simple_types_dict[type_local], xml_el, el_name)
        else:
            generate_builtin_type(type_local, xml_el, el_name)
    else:
        for child in xsd_element:
            if child.tag == xs + "complexType":
                generate_complex_type(child, elements_dict, complex_types_dict, simple_types_dict, xml_el)
                return
            elif child.tag == xs + "simpleType":
                generate_simple_type(child, xml_el, el_name)
                return

        value = generate_value_using_llm(parent_xml)
        if value is not None:
            strategy = "randomly_generated_llm"
        else:
            value = f"sample_{el_name}"
            strategy = "randomly_generated"
        xml_el.text = value
        record_value(xml_el, strategy)

    return xml_el

def generate_complex_type(xsd_complex, elements_dict, complex_types_dict, simple_types_dict, parent_xml):
    """
    Walks an <xs:complexType> node. Handles:
      - <xs:sequence>, <xs:all>, <xs:choice> (including nested choices) by emitting all possible elements
      - <xs:complexContent> with <xs:extension> (walk base first, then extension children)
      - local <xs:element>s
    """
    complex_content = xsd_complex.find(xs + "complexContent")
    if complex_content is not None:
        extension = complex_content.find(xs + "extension")
        if extension is not None:
            base_type = extension.get("base").split(":")[-1]
            if base_type in complex_types_dict:
                generate_complex_type(complex_types_dict[base_type], elements_dict, complex_types_dict,
                                      simple_types_dict, parent_xml)

            def process_compositor(node):
                for child in node:
                    if child.tag == xs + "element":
                        generate_element(child, elements_dict, complex_types_dict, simple_types_dict, parent_xml)
                    elif child.tag in (xs + "sequence", xs + "all", xs + "choice"):
                        process_compositor(child)

            for child in extension:
                if child.tag in (xs + "sequence", xs + "all", xs + "choice"):
                    process_compositor(child)
        return

    def process_compositor(node):
        for child in node:
            if child.tag == xs + "element":
                generate_element(child, elements_dict, complex_types_dict, simple_types_dict, parent_xml)
            elif child.tag in (xs + "sequence", xs + "all", xs + "choice"):
                process_compositor(child)

    for compositor in xsd_complex:
        if compositor.tag in (xs + "sequence", xs + "all", xs + "choice"):
            process_compositor(compositor)

    for e in xsd_complex.findall(xs + "element"):
        generate_element(e, elements_dict, complex_types_dict, simple_types_dict, parent_xml)

def build_sample_instance(xsd_path, root_element_name="DocumentRequest"):
    """
    Loads the xsD, finds the global <xs:element name="DocumentRequest">,
    creates a new XML tree with <DocumentRequest> as root, and populates
    all nested children (with dummy, simpleType, or dynamically generated built-in values).
    Returns the root of the created etree.
    """
    _, elements_dict, complex_types_dict, simple_types_dict = load_xsd(xsd_path)

    if root_element_name not in elements_dict:
        raise ValueError(f"No global <xs:element name=\"{root_element_name}\"> found in {xsd_path}.")

    xml_root = etree.Element(root_element_name)
    xsd_root_elem = elements_dict[root_element_name]

    root_type = xsd_root_elem.get("type")
    if root_type:
        type_local = root_type.split(":")[-1]
        if type_local in complex_types_dict:
            generate_complex_type(complex_types_dict[type_local], elements_dict, complex_types_dict, simple_types_dict,
                                  xml_root)
        elif type_local in simple_types_dict:
            generate_simple_type(simple_types_dict[type_local], xml_root, root_element_name)
        else:
            generate_builtin_type(type_local, xml_root, root_element_name)
    else:
        for child in xsd_root_elem:
            if child.tag == xs + "complexType":
                generate_complex_type(child, elements_dict, complex_types_dict, simple_types_dict, xml_root)
                break
            elif child.tag == xs + "simpleType":
                generate_simple_type(child, xml_root, root_element_name)
                break
        else:
            xml_root.text = f"sample_{root_element_name}"

    return xml_root

def main(args):
    """Check for input arguments"""
    if not os.path.exists(args.xsd_path):
        raise FileNotFoundError(f"File not found: {args.xsd_path}")
    if args.mapping_file_path:
        if not os.path.exists(args.mapping_file_path):
            raise FileNotFoundError(f"File not found: {args.mapping_file_path}")
        global values_map_app_report
        mapping_df = pd.read_csv(args.mapping_file_path, sep="\t", usecols=[0,4], header=0, index_col=None)
        mapping_df = mapping_df.dropna(subset=["VarSampleValue"], ignore_index=True)
        values_map_app_report = dict(zip(mapping_df["Tag"], mapping_df["VarSampleValue"]))
    if not os.path.exists(args.element_types_path):
        raise FileNotFoundError(f"File not found: {args.element_types_path}")

    # Read the element types TSV and get the values from sample payload
    global element_types_df
    element_types_df = pd.read_csv(args.element_types_path, sep="\t", header=0, index_col=None)
    element_types_temp_df = element_types_df.dropna(subset=["sample_xml_value"], ignore_index=True)
    global values_map_sample_payload
    values_map_sample_payload = dict(zip(element_types_temp_df["element_name"], element_types_temp_df["sample_xml_value"]))

    # Get the output file path
    output_file_path = os.path.join(os.path.dirname(args.xsd_path), "sample_payload.xml")
    tsv_path = os.path.join(os.path.dirname(args.xsd_path), "sample_payload_log.tsv")

    # Build the sample instance and save it to file
    sample_root = build_sample_instance(args.xsd_path, root_element_name="DocumentRequest")
    xml_bytes = etree.tostring(sample_root, pretty_print=True, xml_declaration=True, encoding="UTF-8")
    with open(output_file_path, "wb") as f:
        f.write(xml_bytes)

    # Record the details of sample payload generation to a TSV
    records_df = pd.DataFrame(records, columns=["element_name", "path", "value", "strategy"])
    records_df.to_csv(tsv_path, sep="\t", header=True, index=False)

    print(f"Generated sample XML payload:\n{os.path.abspath(output_file_path)}")
    print(f"\nRecorded metadata for sample XML:\n{os.path.abspath(tsv_path)}")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))
