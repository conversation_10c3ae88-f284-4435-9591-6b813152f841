# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-07-04      #
# --------------------------------- #

"""
Get details on the assignment variables (which are not part of the conditional construct)
    This is to get insights if these variables have an XML tag mapping and if test cases should be generated for them
"""
import os

import pandas as pd
import ast
import csv

base_dir = "../../../applications/Combined RPPM Report/xml_parse/test_cases"
model_output_file = os.path.join(base_dir, "model_output.tsv")
var_analysis_file = os.path.join(base_dir, "assignment_vars_analysis.tsv")

df = pd.read_csv(model_output_file, sep='\t', header=0, index_col=None, quoting=csv.QUOTE_NONE, escapechar="\\", dtype={"custom_id": str})
df['variables'] = df['variables'].apply(ast.literal_eval)
df['var_to_tag'] = df['var_to_tag'].apply(ast.literal_eval)
df['output'] = df['output'].apply(ast.literal_eval)
df['rules_list'] = df['rules_list'].apply(ast.literal_eval)

assignment_vars_with_tags_list, assignment_vars_without_tags_list = [], []
assignment_vars_with_tags_cnt_list, assignment_vars_without_tags_cnt_list = [], []
total_assignment_vars_list = []

for idx, row in df.iterrows():
    var_to_tag = row["var_to_tag"]
    variables = row["variables"]
    output = row["output"]

    # Get all the variables that were used in the output
    vars_in_output = set()
    for out_type in ["positives", "negatives"]:
        for testcase in output[out_type]:
            vars_in_output.update(list(testcase.keys()))
    vars_in_output = list(vars_in_output)

    # Get all the variables present in the rule
    all_variables = []
    for var in variables:
        all_variables.append(var["name"])

    # Find all the variables that were not part of the output - these will be assignment variables
    # For these variables, find which of them have an XML tag associated with it
    assignment_vars_with_tags, assignment_vars_without_tags = [], []
    for var_name in all_variables:
        if var_name not in vars_in_output:
            if var_name in var_to_tag:
                assignment_vars_with_tags.append(var_name)
            else:
                assignment_vars_without_tags.append(var_name)

    assignment_vars_with_tags_list.append(assignment_vars_with_tags)
    assignment_vars_without_tags_list.append(assignment_vars_without_tags)
    assignment_vars_with_tags_cnt_list.append(len(assignment_vars_with_tags))
    assignment_vars_without_tags_cnt_list.append(len(assignment_vars_without_tags))

    assignment_vars_cnt = len(all_variables) - len(vars_in_output)
    assert assignment_vars_cnt == len(assignment_vars_with_tags) + len(assignment_vars_without_tags)
    total_assignment_vars_list.append(assignment_vars_cnt)

df['assignment_vars_with_tags'] = assignment_vars_with_tags_list
df['assignment_vars_without_tags'] = assignment_vars_without_tags_list
df['assignment_vars_with_tags_count'] = assignment_vars_with_tags_cnt_list
df['assignment_vars_without_tags_count'] = assignment_vars_without_tags_cnt_list
df['total_assignment_vars'] = total_assignment_vars_list

df.to_csv(var_analysis_file, sep="\t", header=True, index=False, quoting=csv.QUOTE_NONE, escapechar="\\")
