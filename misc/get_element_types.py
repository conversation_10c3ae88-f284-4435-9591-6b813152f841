# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-10      #
# --------------------------------- #

"""
Extract type details and attribute details for all the elements in the schema along with unique types stats
"""

import argparse
import re
import sys
import os
import csv

import pandas as pd
from lxml import etree

def parse_arguments(_):
    parser = argparse.ArgumentParser()

    parser.add_argument('--xsd_path', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/complete-schema.xsd',
                        help='''Main XSD file path for the application''')
    parser.add_argument('--sample_xml_path', type=str,
                        default="../../../applications/Beneficiary Bundling/PTRKBBWI Input.xml",
                        help='Sample XML instance file path (optional)')
    parser.add_argument('--output_tsv_element_types', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/element-types.tsv',
                        help='''TSV file path to save the elements type''')
    parser.add_argument('--output_tsv_unique_types', type=str,
                        default='../../../applications/Beneficiary Bundling/xsd/unique-types.tsv',
                        help='''TSV file path to save the unique types and elements associated with it''')

    return parser.parse_args()

def load_all_schemas(path, loaded=None):
    """
    Recursively parse the XSD at `path` plus any <xsd:include> or <xsd:import>
    under it. Returns a dict { absolute_path: root_element }.
    """
    if loaded is None:
        loaded = {}
    abspath = os.path.abspath(path)
    if abspath in loaded:
        return loaded

    tree = etree.parse(abspath)
    root = tree.getroot()
    ns = root.nsmap
    loaded[abspath] = root

    base = os.path.dirname(abspath)
    for tag in ("include", "import"):
        for node in root.findall(f"xsd:{tag}", namespaces=ns):
            loc = node.get("schemaLocation")
            if loc:
                next_path = os.path.normpath(os.path.join(base, loc))
                load_all_schemas(next_path, loaded)

    return loaded

def find_named_simpletype(type_name, schemas, ns):
    """
    Search across all loaded schemas for <xsd:simpleType name="..."> matching type_name.
    """
    local = type_name.split(":", 1)[-1]
    for root in schemas.values():
        st = root.find(f".//xsd:simpleType[@name='{local}']", namespaces=ns)
        if st is not None:
            return st
    return None

def get_leaf_simpletype(schemas, simple, ns):
    """
    If this simpleType restricts another named simpleType, recurse until base is built-in.
    """
    restr = simple.find("xsd:restriction", namespaces=ns)
    if restr is None:
        return simple
    base = restr.get("base")
    if not base:
        return simple
    # try to resolve base as a named simpleType in any schema
    other = find_named_simpletype(base, schemas, ns)
    if other is not None:
        return get_leaf_simpletype(schemas, other, ns)
    # otherwise base is a built-in XSD type → this is the leaf
    return simple

def extract_all_elements_info(schemas, ns):
    rows = []
    for root in schemas.values():
        for elem in root.findall(".//xsd:element", namespaces=ns):
            name = elem.get("name")
            elm_type = elem.get("type") or ""

            # Gather the element‐instance attributes (with defaults)
            info = {
                "name": name,
                "type": elm_type,
                "default": elem.get("default"),
                "fixed": elem.get("fixed"),
                "minOccurs": elem.get("minOccurs", "1"),
                "maxOccurs": elem.get("maxOccurs", "1"),
                "nillable": elem.get("nillable", "false")
            }

            # first, see if there's an inline simpleType
            inline = elem.find("xsd:simpleType", namespaces=ns)
            if inline is not None:
                leaf = get_leaf_simpletype(schemas, inline, ns)
            else:
                named = find_named_simpletype(elm_type, schemas, ns) if elm_type else None
                leaf = get_leaf_simpletype(schemas, named, ns) if named is not None else None

            # serialize any <xsd:restriction> under the leaf
            restriction_xml = ""
            type_base = ""
            if leaf is not None:
                r = leaf.find("xsd:restriction", namespaces=ns)
                if r is not None:
                    raw = etree.tostring(r, encoding="unicode")
                    start = raw.find(">") + 1
                    end = raw.rfind("</")
                    restriction_xml = raw[start:end]
                    restriction_xml = re.sub(r"\s+", " ", restriction_xml).strip()

                    # Get base from restriction
                    type_base = r.get("base")

            if name:
                rows.append({
                    "element_name": name,
                    "type": elm_type,
                    "type_base": type_base,
                    "type_restriction": restriction_xml,
                    "info": info,
                })

    return rows

def main(args):
    """Check for input arguments"""
    if not os.path.exists(args.xsd_path):
        raise FileNotFoundError(f"XSD file not found: {args.xsd_path}")

    # Parse the XSD file along with all the referenced schemas
    schemas = load_all_schemas(args.xsd_path)
    root = schemas[os.path.abspath(args.xsd_path)]
    ns = root.nsmap

    element_data = extract_all_elements_info(schemas, ns)

    # If XML instance provided, parse and extract text values
    if args.sample_xml_path:
        if not os.path.exists(args.sample_xml_path):
            raise FileNotFoundError(f"XML instance file not found: {args.sample_xml_path}")
        inst_tree = etree.parse(args.sample_xml_path)
        inst_root = inst_tree.getroot()
        # For each element, find first matching by local-name and get text
        for row in element_data:
            name = row["element_name"]
            inst_elem = inst_root.find(f".//{{*}}{name}")
            value = inst_elem.text.strip() if inst_elem is not None and inst_elem.text else ''
            row['sample_xml_value'] = value
    else:
        # no XML, use empty values
        for row in element_data:
            row['sample_xml_value'] = ''

    # write TSV
    out_df = pd.DataFrame(element_data, columns=["element_name", "type", "sample_xml_value", "type_base",
                                                 "type_restriction", "info"])
    out_df.drop_duplicates(subset=["element_name", "type", "sample_xml_value", "type_base", "type_restriction"], inplace=True)
    out_df.to_csv(args.output_tsv_element_types, sep="\t", header=True, index=False, quoting=csv.QUOTE_NONE, escapechar="\\")
    print(f"Wrote {len(out_df)} elements and their types to\n{os.path.abspath(args.output_tsv_element_types)}")

    # Get additional stats if xml restrictions base exist or type starts with xsd:
    out_df = out_df[(out_df['type_base'] != '') | out_df['type'].str.startswith('xsd:')]
    grouped = out_df.groupby('type').agg(elements_list=('element_name', list)).reset_index()
    grouped['elements_count'] = grouped['elements_list'].apply(len)
    grouped = grouped.sort_values(by='elements_count', ascending=False)
    grouped.to_csv(args.output_tsv_unique_types, sep="\t", header=True, index=False,
                   columns=["type", "elements_count", "elements_list"])
    print(f"\nWrote {len(grouped)} types to\n{os.path.abspath(args.output_tsv_unique_types)}")


if __name__ == "__main__":
    main(parse_arguments(sys.argv[1:]))