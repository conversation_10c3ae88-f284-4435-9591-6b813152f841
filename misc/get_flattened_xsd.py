# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-29      #
# --------------------------------- #

"""
flatten xsds
"""

import os
import xml.etree.ElementTree as ET

XS_NS = "http://www.w3.org/2001/XMLSchema"
ET.register_namespace('xsd', XS_NS)

def flatten_xsd(file_path, processed_files=None):
    if processed_files is None:
        processed_files = set()

    # Avoid processing the same file multiple times
    abs_file_path = os.path.abspath(file_path)
    if abs_file_path in processed_files:
        return []

    processed_files.add(abs_file_path)

    tree = ET.parse(file_path)
    root = tree.getroot()

    base_dir = os.path.dirname(abs_file_path)
    inlined_elements = []

    for child in list(root):
        if child.tag in (f"{{{XS_NS}}}include", f"{{{XS_NS}}}import"):
            schema_location = child.get("schemaLocation")
            if schema_location:
                include_path = os.path.join(base_dir, schema_location)
                print(f"Inlining: {include_path}")
                included_tree = flatten_xsd(include_path, processed_files)
                inlined_elements.extend(included_tree)
            root.remove(child)

    # Append all top-level elements (types, elements, etc.) except schema declaration
    for child in list(root):
        inlined_elements.append(child)

    return inlined_elements

def build_flattened_xsd(input_xsd, output_xsd):
    elements = flatten_xsd(input_xsd)
    schema = ET.Element(f"{{{XS_NS}}}schema", attrib={"xmlns:xsd": XS_NS})
    for el in elements:
        schema.append(el)

    tree = ET.ElementTree(schema)
    tree.write(output_xsd, encoding="utf-8", xml_declaration=True)
    print(f"Flattened XSD written to: {output_xsd}")

# Example usage
if __name__ == "__main__":
    input_xsd = "../../../applications/Beneficiary Bundling/xsd/ccp-document-request-v1-types.xsd"
    output_xsd = "../../../applications/Beneficiary Bundling/xsd/temp/flattened-types.xsd"
    build_flattened_xsd(input_xsd, output_xsd)
