# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-07-11      #
# --------------------------------- #

"""
Error codes for the XML generation pipeline
"""

# File System & Input Validation Errors
ERR_FILE_001 = "ERR_FILE_001"  # File not found errors
ERR_FILE_002 = "ERR_FILE_002"  # Directory already exists errors
ERR_FILE_003 = "ERR_FILE_003"  # Invalid file format errors
ERR_FILE_004 = "ERR_FILE_004"  # Application report format validation errors

# Application report processing & Parsing Errors
ERR_APP_REPORT_001 = "ERR_APP_REPORT_001"  # Application not found at XML root in XML app report
ERR_APP_REPORT_002 = "ERR_APP_REPORT_002"  # Multiple or missing DataFiles in XML app report

# Errors related to provided XSD and sample XML
ERR_XSD_001 = "ERR_XSD_001"  # Missing elements in sample XML compared to XSD
ERR_XSD_002 = "ERR_XSD_002"  # Missing elements in XSD that are used in rules
ERR_XSD_003 = "ERR_XSD_003"  # Incorrect payload XML or app XSD

# Rule Processing & Validation Errors
ERR_RULE_001 = "ERR_RULE_001"  # Rules with missing details/information
ERR_RULE_001_01 = "ERR_RULE_001_01"  # Rules without variables
ERR_RULE_001_02 = "ERR_RULE_001_02"  # Undefined rules
ERR_RULE_001_03 = "ERR_RULE_001_03"  # Unmapped sections in rules
ERR_RULE_001_04 = "ERR_RULE_001_04"  # Unmapped variables in rule conditions
ERR_RULE_001_05 = "ERR_RULE_001_05"  # Total unmapped variables in rules

# Batch Processing & API Errors
ERR_BATCH_001 = "ERR_BATCH_001"  # Batch processing failures
ERR_BATCH_002 = "ERR_BATCH_002"  # Batch validation failures
ERR_BATCH_003 = "ERR_BATCH_003"  # Batch completion timeout
ERR_BATCH_004 = "ERR_BATCH_004"  # Batch cancelled

# XML Creation Errors
ERR_XML_CREATION_001 = "ERR_XML_CREATION_001"  # Missing elements preventing XML generation

# System & Runtime Errors
ERR_SYS_001 = "ERR_SYS_001"  # Uncaught exceptions

# Error code to message mapping
ERROR_MESSAGES = {
    ERR_FILE_001: "File not found",
    ERR_FILE_002: "Directory already exists",
    ERR_FILE_003: "Invalid file format",
    ERR_FILE_004: "Application report format validation failed",
    ERR_APP_REPORT_001: "Application not found at XML root in XML app report",
    ERR_APP_REPORT_002: "Multiple or missing DataFiles in XML app report",
    ERR_XSD_001: "Missing elements in sample XML compared to XSD",
    ERR_XSD_002: "Missing elements in XSD that are used in rules",
    ERR_XSD_003: "Incorrect payload XML or app XSD",
    ERR_RULE_001: "Rules with missing details/information",
    ERR_RULE_001_01: "Rules without variables",
    ERR_RULE_001_02: "Undefined rules",
    ERR_RULE_001_03: "Unmapped sections in rules",
    ERR_RULE_001_04: "Unmapped variables in rule conditions",
    ERR_RULE_001_05: "Total unmapped variables in rules",
    ERR_BATCH_001: "Some entries in batch processing failed",
    ERR_BATCH_002: "Input file validation failed for batch",
    ERR_BATCH_003: "Batch completion timeout",
    ERR_BATCH_004: "Batch cancelled",
    ERR_XML_CREATION_001: "Test case XML creation failed",
    ERR_SYS_001: "Uncaught exception",
}

def get_error_message(error_code):
    """Get user-friendly error message for given error code"""
    return ERROR_MESSAGES.get(error_code, "Unknown error")
