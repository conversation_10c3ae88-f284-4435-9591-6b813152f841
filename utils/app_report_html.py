# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-04-29      #
# --------------------------------- #

"""
Class for HTML Application Report Parser
"""
import csv
import html
import json
import os
import re
from io import StringIO

import pandas as pd
from bs4 import BeautifulSoup

from utils.html_utils import split_html_file, convert_to_utf8, extract_tables_from_html
from utils.misc_utils import remove_comments_from_rule


class HTMLApplicationReportParser:

    def __init__(self, app_report_path):
        # App report path must be HTML
        if not (app_report_path.endswith(('.html', '.htm'))):
            raise ValueError("Application report must be HTML.")
        self.app_report_path_html = app_report_path

        # Create relevant directories to save the parsed information
        html_parse_dir_name = "html_parse"
        self.html_parse_dir = os.path.join(os.path.dirname(self.app_report_path_html), html_parse_dir_name)
        if not os.path.exists(self.html_parse_dir):
            os.mkdir(self.html_parse_dir)

        sections_dir = "app_sections"
        self.sections_dir_path = os.path.join(self.html_parse_dir, sections_dir)
        if not os.path.exists(self.sections_dir_path):
            os.mkdir(self.sections_dir_path)

        info_dir = "app_info"
        self.info_dir_path = os.path.join(self.html_parse_dir, info_dir)
        if not os.path.exists(self.info_dir_path):
            os.mkdir(self.info_dir_path)

        # Section names for extracting information
        self.variables_section = "Application Report_Variables List"   # For details of variables
        self.data_section = "Application Report_Data File List"       # For details of XML tags
        self.data_areas_section = "Application Report_Data Areas"       # For mapping of XML tags with variables
        self.rules_section = "Application Report_Rules List"   # For details of rules definitions
        self.documents_section = "Application Report_Documents List"    # To get list of pages in the document
        self.pages_section = "Application Report_Pages List"    # To get details of each page - mainly usage rules

        # Convert the HTML file to utf-8 encoding if not already in utf-8
        convert_to_utf8(self.app_report_path_html)

        # Split the HTML file into multiple HTML files (based on sections)
        self.sections = split_html_file(self.app_report_path_html, self.sections_dir_path)

        self.variables_df = pd.DataFrame()
        self.xml_tags_df = pd.DataFrame()
        self.mapping_df = pd.DataFrame()
        self.rules_df = pd.DataFrame()

        self.doc_and_pages = dict()
        self.doc_usage_rules_df = pd.DataFrame()
        self.page_usage_rules_df = pd.DataFrame()

        self.json_report = {
            "app-name": "",
            "documents": []
        }

    def run(self):
        """
        Get required information from the application report (variables, XML tags, rules)
        :return:
        """
        print("\n\n")
        # Extract variables from the application report
        # This section has only one table which is for the variables
        print("Extracting variables from the application report...")
        variable_table_headers = ["Name", "Type", "Source", "Reset Time", "User Access", "Sample", "Array"]
        tables = self.__get_table(self.variables_section, only_tables=True)
        if len(tables) == 1:
            table_headers = list(tables[0].columns.values)
            assert table_headers == variable_table_headers, (f"Table headers do not match.\nExpected:"
                                                             f" {variable_table_headers}\nFound: {table_headers}")
            self.variables_df = tables[0]
        else:
            raise ValueError("More than one table found in the variables section.")
        self.variables_df.to_csv(os.path.join(self.info_dir_path, "variables.tsv"), sep="\t", header=True, index=False)

        # Extract XML tags from the application report
        # This section has multiple tables, so we get XML tags table based on the headers
        print("Extracting XML tags from the application report...")
        xml_tags_table_headers = ["ID", "Tag Name", "Levels", "Starts Customer", "Starts Section", "First Occurence Only", "Ignore"]
        tables = self.__get_table(self.data_section, only_tables=True)
        for table in tables:
            table_headers = list(table.columns.values)
            if table_headers == xml_tags_table_headers:
                self.xml_tags_df = table
                break
        if self.xml_tags_df.empty:
            raise ValueError("XML tags table not found in the data section.")
        self.xml_tags_df.to_csv(os.path.join(self.info_dir_path, "xml_tags.tsv"), sep="\t", header=True, index=False)

        # Extract mapping of XML tags to variables
        print("Extracting XML tags to variables mapping from the application report...")
        self._extract_tags_to_vars_mapping()

        # Extract all the rules from the application report
        print("Extracting all rules from the application report...")
        self._extract_all_rules()

        # Extract documents and pages list
        print("Extracting documents and pages list from the application report...")
        self._get_documents_and_pages_list()

        # Extract usage rules from the application report
        print("Extracting document and page usage rules from the application report...")
        self._extract_usage_rules()

        print(f"\n\nApplication report details saved at {os.path.abspath(self.info_dir_path)}..")

        # Get the relevant app details in a JSON
        print("Preparing JSON report...")
        self._prepare_json_report()

    def _prepare_json_report(self):
        """
        Prepare a JSON report containing relevant details of the application
        :return:
        """
        # Get the application name (first split from the sections)
        main_section_name = next(iter(self.sections)) # Get the first section name
        app_name = main_section_name.split("_")[1]
        self.json_report["app-name"] = app_name

        # Get the details of documents and pages along with usage rules
        for doc_name in self.doc_and_pages:
            doc_info = {
                "name": doc_name,
                "section": {},
                "rule": {},
                "pages": []
            }
            # Find the usage rule for the document from dataframe
            doc_rule_id = self.doc_usage_rules_df.loc[self.doc_usage_rules_df["Document"] == doc_name, "Usage Rule"].iloc[0]
            if doc_rule_id and doc_rule_id != "None":
                doc_rule = self.__get_rule_and_vars_from_df(doc_rule_id)
                doc_info["rule"] = doc_rule

            # Get the section for the document and find corresponding XML tags
            section_name = self.doc_usage_rules_df.loc[self.doc_usage_rules_df["Document"] == doc_name, "Section"].iloc[0]
            if section_name:
                tags_for_section = self.xml_tags_df[
                    self.xml_tags_df["Starts Section"].str.contains(
                        f"[{section_name}]", na=False, regex=False)]["Tag Name"].drop_duplicates().tolist()
                doc_info["section"] = {
                    "name": section_name,
                    "xml-tags": tags_for_section
                }

            # For each page in the document, find their usage rules
            for page_name in self.doc_and_pages[doc_name]:
                page_info = {
                    "name": page_name,
                    "rule": {}
                }
                # Find the usage rule for the page from dataframe
                page_rule_id = self.page_usage_rules_df.loc[
                    (self.page_usage_rules_df["Document"] == doc_name)
                    & (self.page_usage_rules_df["Page"] == page_name), "Usage Rule"].iloc[0]
                if page_rule_id and page_rule_id != "None":
                    page_rule = self.__get_rule_and_vars_from_df(page_rule_id)
                    page_info["rule"] = page_rule

                doc_info["pages"].append(page_info)

            self.json_report["documents"].append(doc_info)

        # Save app report JSON
        json_report_file = os.path.basename(os.path.splitext(self.app_report_path_html)[0]) + '.json'
        json_report_path = os.path.join(self.html_parse_dir, json_report_file)
        with open(os.path.join(json_report_path), 'w') as file:
            json.dump(self.json_report, file, indent=4)
        print(f"\n\nJSON application report saved at {os.path.abspath(json_report_path)}..")

    def __get_rule_and_vars_from_df(self, rule_id):
        """
        Get the rule from the rules dataframe, along with the variables associated with it
        :param rule_id: Rule ID
        :return:
        """
        rule_rows = self.rules_df.loc[self.rules_df["Rule ID"] == rule_id]
        # If rule details are not found for a rule ID, the app report is missing the definition
        if rule_rows.empty:
            status = {
                "success": False,
                "error": {
                    "unmapped-variables": [],
                    "unmapped-sections": [],
                    "no-variables-in-rule": False,
                    "undefined-rule": True
                }
            }

            return {
                "id": rule_id,
                "test-cases-status": status
            }

        rule = rule_rows.iloc[0]
        rule_name = rule["Rule Name"]
        rule_formula = rule["Formula"]
        rule_formula_clean = remove_comments_from_rule(rule_formula)

        # Get variables associated with the rule
        variables = []
        rows_df = self.variables_df[self.variables_df["Name"].apply(lambda var: var in rule_formula_clean)]
        for _, row in rows_df.iterrows():
            variable_name = row["Name"]
            variable_info = {
                "name": variable_name,
                "type": row["Type"],
                "sample-value": row["Sample"] if row["Sample"] else None,
                "is-array": True if row["Array"] == "Yes" else False
            }

            # Find the corresponding XML tag for the variable
            # TODO: Considers first occurrence of the variable mapping; check if there are more than 1
            xml_tag = self.mapping_df.loc[self.mapping_df["Variable"] == variable_name, "Tag Name"]
            xml_tag = xml_tag.iloc[0] if not xml_tag.empty else None
            variable_info["xml-schema"] = {"tag-name": xml_tag}

            # Delete sample-value if it is empty
            if isinstance(variable_info["sample-value"], float) and pd.isna(variable_info["sample-value"]):
                variable_info["sample-value"] = None

            variables.append(variable_info)

        rule_info = {
            "id": rule_id,
            "name": rule_name,
            "formula": rule_formula,
            "formula-clean": rule_formula_clean,
            "variables": variables
        }

        return rule_info

    def __get_table(self, section_name, only_tables=True):
        """
        Get the table from the specified section
        :param section_name: Name of the section
        :param only_tables: If True, tables are extracted using pd.read_html, otherwise using beautifulsoup along with other content.
        :return:
        """
        # Extract tables from the HTML file
        section_file = self.sections[section_name]["html"]
        section_path = os.path.join(self.sections_dir_path, section_file)
        tables = extract_tables_from_html(section_path, only_tables=only_tables)
        return tables

    def __parse_html_section(self, section_name):
        """
        Parse HTML from the specified section
        :param section_name: Name of the section
        :return:
        """
        section_file = self.sections[section_name]["html"]
        section_path = os.path.join(self.sections_dir_path, section_file)
        with open(section_path, "r", encoding="utf-8") as f:
            soup = BeautifulSoup(f, "html.parser")
        return soup

    def _extract_tags_to_vars_mapping(self):
        """
        Extracts the tags to variables mapping from the application report
        In the application report, each XML tag has a separate table for variable mapping.
        The XML tag name is writen just before the table in format ID: Tag Name
        Hence, parsing is done accordingly
        """
        mapping_list = []
        mapping_table_headers = ["ID", "Area", "Start", "Length", "Variable", "Implied Digits", "Format", "Array Index", "Action"]
        tables = self.__get_table(self.data_areas_section, only_tables=False)

        # Get variable type and sample value into the mapping; for this variables dataframe must be extracted
        assert not self.variables_df.empty, "Variables are not extracted!"

        # Loop through and extract relevant tables
        for i, table in enumerate(tables):
            df = pd.read_html(StringIO(str(table)), header=0)[0]
            table_headers = list(df.columns.values)

            # Skip if the table has more than one row
            if len(df) != 1:
                continue

            # If the table is of interest, extract XML tag name from previous sibling
            if table_headers == mapping_table_headers:
                prev_text = table.find_previous(string=True)
                if prev_text:
                    splits = prev_text.strip().split(": ") # Expected format is "ID: Tag Name"
                    # Skip if the split does not yield exactly two parts
                    if len(splits) != 2:
                        continue
                    tag_id, tag_name = splits
                    row = [tag_id, tag_name] + df.values.tolist()[0]

                    # Find the type and sample for the variable (variable name is at index 6 in the row)
                    variable_data = self.variables_df[self.variables_df["Name"] == row[6]]
                    variable_type, variable_sample_value = "", ""
                    if len(variable_data) != 0:
                        variable_type = variable_data.iloc[0]["Type"]
                        variable_sample_value = variable_data.iloc[0]["Sample"]

                    # Append the row to the mapping list
                    row.extend([variable_type, variable_sample_value])
                    mapping_list.append(row)

        # Create a DataFrame from the mapping list
        if mapping_list:
            cols = ["Tag ID", "Tag Name"] + mapping_table_headers + ["Variable Type", "Variable Sample"]
            self.mapping_df = pd.DataFrame(mapping_list, columns=cols, index=None)
            self.mapping_df.to_csv(os.path.join(self.info_dir_path, "xml_tags_to_variables.tsv"), sep="\t", header=True, index=False)

    def _extract_all_rules(self):
        rules_list = []
        rules_soup = self.__parse_html_section(self.rules_section)

        # Find all <a name="#Rule..."></a> anchors
        for anchor in rules_soup.find_all("a"):
            if anchor.get("name", "").startswith("#Rule"):
                strong_tag = anchor.find_next("strong")
                if not strong_tag:
                    continue

                # Extract rule ID
                rule_text = strong_tag.get_text().strip()
                parts = rule_text.split(' ', 1)
                if len(parts) != 2:
                    continue
                rule_id, rule_name = parts[0], parts[1]

                # Get the formula
                pre_tag = strong_tag.find_next("pre")
                if not pre_tag:
                    continue
                formula = html.unescape(pre_tag.get_text().strip())
                formula = formula.replace('\n', '\\n')

                rules_list.append([rule_id, rule_name, formula])
        # Create a DataFrame from the rules list
        if rules_list:
            self.rules_df = pd.DataFrame(rules_list, columns=["Rule ID", "Rule Name", "Formula"], index=None)
            self.rules_df.to_csv(os.path.join(self.info_dir_path, "rules.tsv"), sep="\t", header=True, index=False,
                                 quoting=csv.QUOTE_NONE, escapechar='\\')

    def _get_documents_and_pages_list(self):
        """
        Extracts document list along with pages associated with each document
        :return:
        """
        # Documents list is a metadata information that is present in the first section of the application report
        main_section_name = next(iter(self.sections))
        tables = self.__get_table(main_section_name, only_tables=False)
        # Look for table with previous text that starts with "Document List"
        for table in tables:
            prev_text = table.find_previous(string=True).strip()
            if prev_text.startswith("Document List"):
                # Find all the rows in the table
                rows = table.find_all("tr")

                i = 0
                while i < len(rows) - 1:
                    first_row = rows[i].find_all('td')
                    second_row = rows[i + 1].find_all('td')
                    doc_name = first_row[0].get_text().strip()  # Document name is in first column of the first row
                    second_row_first_col = second_row[0].get_text().strip()
                    second_row_second_col = second_row[1]

                    # Check pattern: first row has content in col 1, second row has empty col 1 and a <ul> in col 2
                    if doc_name and not second_row_first_col and second_row_second_col.find('ul'):
                        page_names = [li.get_text().strip() for li in second_row_second_col.find_all('li')]
                        self.doc_and_pages[doc_name] = page_names
                        i += 2
                    else:
                        i += 1  # Skip if pattern doesn't match

                break

        # Save the document and pages list to a JSON file
        with open(os.path.join(self.info_dir_path, "doc_and_pages.json"), 'w') as file:
            json.dump(self.doc_and_pages, file, indent=4)

    @staticmethod
    def __find_usage_rule_and_section_from_soup(soup, name, find_section=False):
        # Find the anchor using the document/page name
        anchor = None
        for a_tag in soup.find_all("a"):
            # Check if the anchor text matches the document name
            a_tag_text = a_tag.get_text().strip()
            if a_tag_text == name or a_tag_text.startswith(name):
                anchor = a_tag
                break

        usage_rule, section_name = None, None
        if anchor:
            # From the anchor, find the next "Usage Rule" row
            for tag in anchor.find_all_next("td", string="Usage Rule:"):
                value_td = tag.find_next_sibling("td")
                if value_td:
                    usage_rule = value_td.get_text().strip()
                    break

            # From the anchor, find the next "Include Only for Section" row
            if find_section:
                for tag in anchor.find_all_next("td", string="Include Only for Section:"):
                    value_td = tag.find_next_sibling("td")
                    if value_td:
                        section_name = value_td.get_text().strip()
                        if section_name == "No":
                            section_name = None
                        break

        return usage_rule, section_name

    def _extract_usage_rules(self):
        """
        Extracts the usage rules from the application report (for documents and pages)
        """
        doc_soup = self.__parse_html_section(self.documents_section)
        page_soup = self.__parse_html_section(self.pages_section)
        doc_usage_rules, page_usage_rules = [], []

        # First, find the usage rule for documents
        for doc_name in self.doc_and_pages:
            doc_rule, section = self.__find_usage_rule_and_section_from_soup(doc_soup, doc_name, find_section=True)
            doc_usage_rules.append([doc_name, doc_rule, section])

            # For each page in the document, find their usage rules
            for page_name in self.doc_and_pages[doc_name]:
                page_rule, _ = self.__find_usage_rule_and_section_from_soup(page_soup, page_name, find_section=False)
                page_usage_rules.append([doc_name, page_name, page_rule])

        # Create DataFrames from the usage rules
        if doc_usage_rules:
            self.doc_usage_rules_df = pd.DataFrame(doc_usage_rules, columns=["Document", "Usage Rule", "Section"])
            self.doc_usage_rules_df.to_csv(os.path.join(self.info_dir_path, "doc_usage_rules.tsv"), sep="\t", header=True, index=False)
        if page_usage_rules:
            self.page_usage_rules_df = pd.DataFrame(page_usage_rules, columns=["Document", "Page", "Usage Rule"])
            self.page_usage_rules_df.to_csv(os.path.join(self.info_dir_path, "page_usage_rules.tsv"), sep="\t", header=True, index=False)

    @staticmethod
    def __get_inline_text_before(tag):
        inline_text = []
        # Walk backwards through previous elements
        for elem in tag.previous_elements:
            if elem.name == "br" or (elem.name in ["p", "table"] and elem != tag):
                break  # Stop if block boundary is hit
            if isinstance(elem, str) and elem.strip():
                inline_text.insert(0, elem.strip())
        inline_text = re.sub(r' +', ' ', " ".join(inline_text))
        return inline_text
