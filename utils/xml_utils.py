# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-04-30      #
# --------------------------------- #

"""
XML file handling utilities
"""

import copy
import os

import pandas as pd
from lxml import etree
from xmlschema import XsdElement
from xmlschema.validators import XsdSimpleType, XsdComplexType


def parse_xml(xml_file_path):
    """
    Parse an XML file and return the root element and tree.
    :param xml_file_path: Path to the XML file
    :return:
    """
    parser = etree.XMLParser(remove_blank_text=True)
    tree = etree.parse(xml_file_path, parser)
    root = tree.getroot()

    return tree, root

def load_schemas_with_definitions(path, extract_defs=False, processed=None, schemas=None,
                                 elements=None, complex_types=None, simple_types=None):
    """
        Recursively parse the XSD at `path` plus any <xs:include> / <xs:import>.
        - Always returns `schemas`: {abspath: root_element}
        - If `extract_defs` is True, also collects:
            * elements: {name: <xs:element> node}
            * complex_types: {qualified_name: <xs:complexType> node}
            * simple_types: {qualified_name: <xs:simpleType> node}
          Otherwise, the three definition maps remain empty.

        Returns:
          schemas, elements, complex_types, simple_types, type_context_map
        """
    # initialize on first call
    if processed is None:
        processed = set()
        schemas = {}
        elements = {} if extract_defs else {}
        complex_types = {} if extract_defs else {}
        simple_types = {} if extract_defs else {}

    abspath = os.path.abspath(path)
    if abspath in processed:
        return schemas, elements, complex_types, simple_types

    processed.add(abspath)

    _, root = parse_xml(abspath)
    schemas[abspath] = root

    # determine the XSD namespace URI
    nsmap = root.nsmap.copy()
    xsd_uri = nsmap.get('xsd') if 'xsd' in nsmap else nsmap.get('xs')
    if not xsd_uri:
        raise ValueError(f"Cannot determine XSD namespace for {abspath!r}")
    xsd_tag = f"{{{xsd_uri}}}"

    # recurse into includes/imports
    for tag in ('include', 'import'):
        for node in root.findall(f".//{xsd_tag}{tag}"):
            loc = node.get('schemaLocation')
            if not loc:
                continue
            next_path = os.path.normpath(os.path.join(os.path.dirname(abspath), loc))
            load_schemas_with_definitions(next_path, extract_defs, processed, schemas,
                elements, complex_types, simple_types)

    # if extraction is requested, collect globals with schema-qualified names
    if extract_defs:
        # Create a schema identifier from the filename
        schema_id = os.path.splitext(os.path.basename(abspath))[0]

        for node in root:
            if node.tag == xsd_tag + 'element' and node.get('name'):
                name = node.get('name')
                # Store both unqualified and qualified names for elements
                elements[name] = node
                elements[f"{schema_id}:{name}"] = node
            elif node.tag == xsd_tag + 'complexType' and node.get('name'):
                name = node.get('name')
                # Store both unqualified and qualified names, but prioritize qualified for conflicts
                qualified_name = f"{schema_id}:{name}"
                if name not in complex_types:
                    complex_types[name] = node
                complex_types[qualified_name] = node
            elif node.tag == xsd_tag + 'simpleType' and node.get('name'):
                name = node.get('name')
                # Store both unqualified and qualified names, but prioritize qualified for conflicts
                qualified_name = f"{schema_id}:{name}"
                if name not in simple_types:
                    simple_types[name] = node
                simple_types[qualified_name] = node

    return schemas, xsd_tag, elements, complex_types, simple_types


def build_type_context_map(schemas, xsd_tag, complex_types_dict):
    """
    Build a comprehensive mapping of type usage contexts to resolve conflicts generically.

    Returns:
        type_context_map: {
            type_name: {
                'schemas': [list of schema_ids where this type is defined],
                'element_contexts': {
                    'element_name': [list of schema_ids where this element uses this type],
                    ...
                },
                'parent_contexts': {
                    'parent_element_name': [list of schema_ids],
                    ...
                }
            }
        }
    """
    type_context_map = {}

    for schema_path, root in schemas.items():
        schema_id = os.path.splitext(os.path.basename(schema_path))[0]

        # Find all complex type definitions in this schema
        for complex_type in root.findall(f".//{xsd_tag}complexType[@name]"):
            type_name = complex_type.get('name')

            if type_name not in type_context_map:
                type_context_map[type_name] = {
                    'schemas': [],
                    'element_contexts': {},
                    'parent_contexts': {}
                }

            # Record which schema defines this type
            if schema_id not in type_context_map[type_name]['schemas']:
                type_context_map[type_name]['schemas'].append(schema_id)

        # Find all elements that use types and track their contexts
        for element in root.findall(f".//{xsd_tag}element[@type]"):
            elem_name = element.get('name')
            elem_type = element.get('type')

            if elem_type:
                type_local = elem_type.split(':')[-1]

                if type_local in type_context_map:
                    if elem_name not in type_context_map[type_local]['element_contexts']:
                        type_context_map[type_local]['element_contexts'][elem_name] = []

                    if schema_id not in type_context_map[type_local]['element_contexts'][elem_name]:
                        type_context_map[type_local]['element_contexts'][elem_name].append(schema_id)

                    # Track parent context
                    parent = element.getparent()
                    while parent is not None:
                        if parent.tag == f"{xsd_tag}complexType":
                            parent_name = parent.get('name')
                            if parent_name:
                                if parent_name not in type_context_map[type_local]['parent_contexts']:
                                    type_context_map[type_local]['parent_contexts'][parent_name] = []
                                if schema_id not in type_context_map[type_local]['parent_contexts'][parent_name]:
                                    type_context_map[type_local]['parent_contexts'][parent_name].append(schema_id)
                                break
                        elif parent.tag == f"{xsd_tag}element" and parent.get('name'):
                            parent_name = parent.get('name')
                            if parent_name not in type_context_map[type_local]['parent_contexts']:
                                type_context_map[type_local]['parent_contexts'][parent_name] = []
                            if schema_id not in type_context_map[type_local]['parent_contexts'][parent_name]:
                                type_context_map[type_local]['parent_contexts'][parent_name].append(schema_id)
                        parent = parent.getparent()

    return type_context_map

def _remove_namespaces_from_xml(tree):
    """Strip namespaces from parsed XML tree."""
    for elem in tree.getiterator():
        # strip namespace from tag
        if isinstance(elem.tag, str) and '}' in elem.tag:
            elem.tag = elem.tag.split('}', 1)[1]
        # strip namespace from attribute keys
        for attr in list(elem.attrib):
            if '}' in attr:
                new_attr = attr.split('}', 1)[1]
                elem.attrib[new_attr] = elem.attrib.pop(attr)
    return tree

def __find_tag(xml_root, tag_name):
    """Find the first child with a given tag name (ignoring namespace)."""
    for child in xml_root.iter():
        if child.tag.split('}')[-1] == tag_name:
            return child
    return None

def get_xml_tags(xml_tree):
    """
    Get all tags from an XML tree.
    :param xml_tree: Parsed XML tree
    :return: List of all tags in the XML tree
    """
    xml_tree = _remove_namespaces_from_xml(xml_tree)
    root = xml_tree.getroot()
    tags_info = []

    def _recurse(element, path="", level=0):
        current_path = f"{path}>{element.tag}" if path else element.tag
        has_value = bool(element.text and element.text.strip())
        tags_info.append((element.tag, current_path, level, has_value))
        for child in element:
            _recurse(child, current_path, level + 1)

    _recurse(root)
    df = pd.DataFrame(tags_info, columns=["Tag", "FullPath", "Level", "HasValue"])

    return df

def modify_tags_in_xml(xml_tree, update_dict, elem_types_df=None):
    """
    Modify a specific tag in an XML file. If the value is a list (> 1), it creates multiple entries of the XML tag
    :param xml_tree: Parsed XML tree
    :param update_dict: Dictionary with tag names as keys and new values as values
    :param elem_types_df: Element types dataframe to get maxoccurs
    """
    modified_tree = copy.deepcopy(xml_tree)
    modified_root = modified_tree.getroot()

    missing_elements = []

    # Modifies the first occurrence of the tag
    for tag, new_value in update_dict.items():
        element = __find_tag(modified_root, tag)

        # If element is not found, report it
        if element is None:
            missing_elements.append(tag)
        else:
            # If element found, but missing_elements already ahs entries, the tree should not be modified
            if len(missing_elements) > 0:
                continue
            # Modify tree only if element is not None and there aren't any missing elements
            else:
                parent = element.getparent()
                if parent is None:
                    # root element special-case: we won’t handle lists on the root
                    if isinstance(new_value, list) and len(new_value) > 1:
                        raise ValueError(f"Cannot create multiple root elements for tag '{tag}'")
                    val = new_value[0] if isinstance(new_value, list) else new_value
                    element.text = str(val) if val is not None else ""
                    continue

                # 1. Scalar case: just set text
                if not isinstance(new_value, list):
                    element.text = str(new_value) if new_value is not None else ""
                    continue

                # Cases where the element is a list
                # 2. Empty list: remove element
                if not new_value:
                    parent.remove(element)
                    continue

                # 3. Single-item list: update text
                if len(new_value) == 1:
                    element.text = str(new_value[0]) if new_value[0] is not None else ""
                    continue

                # 4. Multi-item list: check with XSD if available
                if elem_types_df is not None:
                    child_max = get_max_occurs(elem_types_df, element.tag)
                    if child_max == 1:
                        grand = parent.getparent()
                        parent_max = get_max_occurs(elem_types_df, parent.tag)
                        # If parent allows multiples, replicate the entire <parent>
                        if parent_max is None or parent_max > 1:
                            idx = grand.index(parent)
                            grand.remove(parent)
                            for val in new_value:
                                new_parent = copy.deepcopy(parent)
                                new_child = new_parent.find(tag)
                                new_child.text = str(val) if val is not None else ""
                                grand.insert(idx, new_parent)
                                idx += 1
                            continue

                        # else: both child_max==1 and parent_max==1 → ignore element for now (TODO: revisit this logic)
                        parent.remove(element)
                        continue
                    else:
                        # child max allows multiple elements. so replicate the element under its parent
                        idx = parent.index(element)
                        parent.remove(element)
                        for val in new_value:
                            new_elem = etree.Element(tag)
                            new_elem.text = str(val) if val is not None else ""
                            parent.insert(idx, new_elem)
                            idx += 1
                        continue

                # 5. Fallback: replicate just the element under its parent
                idx = parent.index(element)
                parent.remove(element)
                for val in new_value:
                    new_elem = etree.Element(tag)
                    new_elem.text = str(val) if val is not None else ""
                    parent.insert(idx, new_elem)
                    idx += 1

    # Return missing elements if not empty instead of modified tree
    if len(missing_elements) > 0:
        return missing_elements

    return modified_tree

def save_xml(xml_tree, output_file_path):
    """
    Save the modified XML tree to a file.
    :param xml_tree: Modified XML tree
    :param output_file_path: Path to save the modified XML file
    """
    xml_tree.write(output_file_path, pretty_print=True, xml_declaration=True, encoding='utf-8')

def allows_text(xml_schema, elem):
    """
    Check if the element text is allowed in the schema or not.
    """
    for component in xml_schema.iter_components():
        if isinstance(component, XsdElement) and component.name == elem:
            component_type = component.type

            if isinstance(component_type, XsdSimpleType):
                return True
            if isinstance(component_type, XsdComplexType) and component_type.has_simple_content():
                return True
            if isinstance(component_type, XsdComplexType) and getattr(component_type, 'mixed', False):
                return True
            return False

    raise KeyError(f"Element {elem} not present in schema!")

def find_element_path(xsd_tree, elem, ns, xsd_ns, visited=None):
    """
    For a leaf element, return its instance path as a list of element names

    1. If <xs:element name="elem"> is nested inline under other <xs:element> declarations, use those ancestors.
    2. Otherwise, if it's referenced via <xsd:element ref="elem"/> inside a named <xsd:complexType>,
       climb from that <xsd:element ref="elem"/> up to its containing complexType, then:
         a. If any global <xsd:element type="thatComplexType"/> exists, recurse on it.
         b. Otherwise, if some other <xsd:complexType> extends thatComplexType, find its name,
             then find the global <xsd:element type="DocumentRequest"/> and recurse on it.
    3. Otherwise, if it's declared under a named <xs:complexType>, find the <xs:element type="thatComplexType"> and
    recurse.
    4. Otherwise, it's a top-level global element: path = [elem].
    """
    if visited is None:
        visited = set()
    if elem in visited:
        raise RuntimeError(f"Circular type definitions encountered at '{elem}'")
    visited.add(elem)

    # Look for an <xsd:element name="tag">
    elems = xsd_tree.findall(f".//xsd:element[@name='{elem}']", namespaces=ns)
    if not elems:
        raise ValueError(f"No <xsd:element name='{elem}'> found in XSD file")
    xsd_elem = elems[0]

    # 1. Inline parents: walk up to any enclosing xs:element[@name=…]
    inline_parents = []
    parent = xsd_elem.getparent() if hasattr(xsd_elem, 'getparent') else None
    while parent is not None:
        if parent.tag == f"{{{xsd_ns}}}element" and parent.get("name") is not None:
            inline_parents.append(parent.get("name"))
        parent = parent.getparent() if hasattr(parent, "getparent") else None
    if inline_parents:
        inline_parents.reverse()
        return inline_parents + [elem]

    # 2. "Referenced" parents: find any <xsd:element ref="elem"/> in the entire schema.
    ref_node = None
    for node in xsd_tree.iter(f"{{{xsd_ns}}}element"):
        if node.get("ref") == elem:
            ref_node = node
            break

    if ref_node is not None:
        # Walk up from <xsd:element ref="elem"/> to find its containing <xsd:complexType name="…">
        parent = ref_node.getparent() if hasattr(ref_node, "getparent") else None
        while parent is not None:
            if parent.tag == f"{{{xsd_ns}}}complexType" and parent.get("name") is not None:
                base_complex = parent.get("name")

                # 2a. Look for any global <xsd:element type="base_complex"/>
                direct_parent_name = None
                for el in xsd_tree.iter(f"{{{xsd_ns}}}element"):
                    if el.get("type") == base_complex:
                        direct_parent_name = el.get("name")
                        break
                if direct_parent_name:
                    parent_path = find_element_path(xsd_tree, direct_parent_name, ns, xsd_ns, visited)
                    return parent_path + [elem]

                # 2b. If no global element @type=base_complex, look for any complexType that extends base_complex
                extender_name = None
                for ctype in xsd_tree.iter(f"{{{xsd_ns}}}complexType"):
                    # We need to see if ctype has <xsd:complexContent><xsd:extension base="base_complex">
                    for cc in ctype.iter(f"{{{xsd_ns}}}complexContent"):
                        for ext in cc.iter(f"{{{xsd_ns}}}extension"):
                            if ext.get("base") == base_complex:
                                extender_name = ctype.get("name")
                                break
                        if extender_name:
                            break
                    if extender_name:
                        break

                if extender_name:
                    # Find the global <xsd:element type="extender_name"/>
                    parent_name = None
                    for el in xsd_tree.iter(f"{{{xsd_ns}}}element"):
                        if el.get("type") == extender_name:
                            parent_name = el.get("name")
                            break
                    if parent_name:
                        parent_path = find_element_path(xsd_tree, parent_name, ns, xsd_ns, visited)
                        return parent_path + [elem]

            # If we found a matching complexType but neither step 2a nor 2b yielded a parent element,
            # keep walking up in case there is a higher wrapper. (But usually 2a or 2b will fire.)
            parent = parent.getparent() if hasattr(parent, "getparent") else None

    # 3. See if the element is under a named complexType
    #    i.e. <xsd:complexType name="Foo"> … <xsd:element name="elem"> … </xsd:complexType>
    parent = xsd_elem.getparent() if hasattr(xsd_elem, "getparent") else None
    while parent is not None:
        if parent.tag == f"{{{xsd_ns}}}complexType" and parent.get("name") is not None:
            ctype = parent.get("name")
            # Look for a global <xsd:element type="ctype"/>
            parent_name = None
            for el in xsd_tree.iter(f"{{{xsd_ns}}}element"):
                if el.get("type") == ctype:
                    parent_name = el.get("name")
                    break
            if parent_name:
                parent_path = find_element_path(xsd_tree, parent_name, ns, xsd_ns, visited)
                return parent_path + [elem]
        parent = parent.getparent() if hasattr(parent, "getparent") else None

    # 4. No inline parent and No complexType wrapper, top‐level element
    return [elem]

def get_sequence_order(xsd_tree, elem_name, ns):
    """
    For <xsd:element name="{elem_name}">, return the ordered list of its xsd:sequence children names, or [] if none.
    """
    # find the element declaration
    decl = xsd_tree.find(f".//xsd:element[@name='{elem_name}']", namespaces=ns)
    if decl is None:
        return []
    # Resolve its complexType (named or inline)
    type_name = decl.get("type")
    if type_name:
        ctype = xsd_tree.find(f".//xsd:complexType[@name='{type_name}']", namespaces=ns)
    else:
        ctype = decl.find("xsd:complexType", namespaces=ns)
    if ctype is None:
        return []
    seq = ctype.find("xsd:sequence", namespaces=ns)
    if seq is None:
        return []
    # extract the child element names in order
    return [e.get("name") for e in seq.findall("xsd:element", namespaces=ns)]

def get_max_occurs(elem_df, elem_name):
    """
    Returns the maxOccurs for element using element types dataframe
    """
    max_occurs = elem_df[elem_df["element_name"] == elem_name].iloc[0]["maxOccurs"]
    max_occurs = None if max_occurs == "unbounded" else int(max_occurs)
    return max_occurs

def build_xml_tree(xml_schema, xsd_tree, values_dict, elem_types_df, namespaces, root_element="DocumentRequest"):
    """
    Build an XML tree containing only the branches needed by `values_dict`,
    inserting each new element at the schema‐defined position.
    If values are list (greater than 1), the code splits the values into separate parent wrappers if
    the leaf itself is maxOccurs=1 but its parent is repeatable.
    """
    # Determine document root and get the root element
    roots = xml_schema.root_elements
    roots_list = list(roots.values()) if isinstance(roots, dict) else roots
    if not roots_list:
        raise RuntimeError("No global root element found in the provided schema")

    if root_element and root_element in xml_schema.elements:
        root_name = root_element
    else:
        root_name = roots_list[0].name
    root = etree.Element(root_name)

    # Maintain a dictionary containing order of children elements for a parent element
    children_order_dict = dict()
    # Get XSD namespace
    xsd_namespace = namespaces.get('xsd')

    for leaf, text in values_dict.items():
        # Check if a value is allowed for the leaf element
        if not allows_text(xml_schema, leaf):
            continue

        # Find the entire path of the element from XSD tree
        parts = find_element_path(xsd_tree, leaf, namespaces, xsd_namespace)

        # Ensure it starts with the root
        if root_element:
            if root_element in parts:
                index = parts.index(root_element)
                parts = parts[index:]
            else:
                continue

        if parts[0] != root_name:
            parts.insert(0, root_name)

        # normalize to a list of values
        if isinstance(text, list):
            if len(text) > 1:
                values = text
                is_list = True

                # Figure out parent-of-leaf and grandparent
                if len(parts) >= 2:
                    parent = parts[-2]
                    leaf_maxo = get_max_occurs(elem_types_df, leaf)
                else:
                    parent = None
                    leaf_maxo = None

                # Compute whether parent can repeat if maxoccurs of leaf is 1
                if leaf_maxo == 1 and parent and len(parts) >= 3:
                    parent_maxo = get_max_occurs(elem_types_df, parent)
                else:
                    parent_maxo = None  # irrelevant

                # Only split into new parent wrappers if leaf_maxo==1 AND parent is repeatable
                split_parent = (leaf_maxo == 1) and (parent_maxo is None or parent_maxo > 1)
            else:
                values = text
                is_list = False
                split_parent = False
        else:
            values = [text]
            is_list = False
            split_parent = False

        # Insert the leaf element in the tree based on order in the schema
        for idx, val in enumerate(values):
            parent = root
            for tag in parts[1:]:
                # If we're about to descend into the parent-of-leaf for an extra list item,
                # and we know we must split the parent, force creation of a new wrapper
                if split_parent and tag == parts[-2] and idx > 0:
                    child = None
                else:
                    # For the leaf itself in a list always create a fresh element
                    child = None if (is_list and tag == leaf) else parent.find(tag)

                if child is None:
                    # Compute the children sequence order for this parent
                    if parent.tag in children_order_dict:
                        order = children_order_dict[parent.tag]
                    else:
                        order = get_sequence_order(xsd_tree, parent.tag, namespaces)
                        children_order_dict[parent.tag] = order

                    new_child = etree.Element(tag)
                    if order:
                        # determine insertion index based on sequence
                        try:
                            my_pos = order.index(tag)
                        except ValueError:
                            my_pos = None
                        if my_pos is not None:
                            insert_idx = None
                            for idx, existing in enumerate(list(parent)):
                                try:
                                    ex_pos = order.index(existing.tag)
                                except ValueError:
                                    ex_pos = len(order)
                                if ex_pos > my_pos:
                                    insert_idx = idx
                                    break
                            if insert_idx is None:
                                parent.append(new_child)
                            else:
                                parent.insert(insert_idx, new_child)
                        else:
                            parent.append(new_child)
                    else:
                        # no sequence info, just append
                        parent.append(new_child)
                    child = new_child

                parent = child

            parent.text = str(val)

    return root

def concatenate_xmls(input_dir, output_file, root_tag="DocumentRequests"):
    # Create the new root element (DocumentRequests tag)
    root = etree.Element(root_tag)
    cnt = 0

    # Iterate over all XML files in the directory
    for fn in os.listdir(input_dir):
        if fn.endswith(".xml"):
            xml_path = os.path.join(input_dir, fn)
            tree = etree.parse(xml_path)
            src_root = tree.getroot()

            # Assuming there is only one <DocumentRequest> in the xml file, insert a comment and then the element
            dr_elem = src_root.find('DocumentRequest')
            comment = etree.Comment(f"File name: {fn}")
            root.append(comment)
            root.append(copy.deepcopy(dr_elem))  # Deep-copy to avoid re-parenting the original element
            cnt += 1

    # Build the final tree and write it out
    etree.indent(root) # Auto-indent all elements and comments
    final_tree = etree.ElementTree(root)
    save_xml(final_tree, output_file)
    return cnt
