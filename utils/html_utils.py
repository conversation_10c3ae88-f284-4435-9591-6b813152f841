# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-04-30      #
# --------------------------------- #

"""
Miscellaneous utility functions
"""

import os
import re

import chardet
import html2text
import pandas as pd
from bs4 import BeautifulSoup


def _html_file_to_markdown(html_file_path):
    """
    Convert an HTML file to Markdown ONLY if the markdown file does not already exist.
    :param html_file_path: Path to the HTML file
    :return:
    """
    # Convert the file to utf-8 encoding if not already in utf-8
    convert_to_utf8(html_file_path)

    # Read the HTML file
    with open(html_file_path, 'r', encoding="utf-8") as f:
        html_content = f.read()

    # HTML content preprocessing
    html_content = html_content.replace('<br/></td>', '</td>') # Remove line breaks at the end of <td> tags
    html_content = html_content.replace('\u00A0', ' ')  # Replace non-breaking spaces with regular spaces

    # Create an html2text converter and convert HTML to Markdown
    converter = html2text.HTML2Text()
    converter.ignore_links = False
    converter.body_width = 0
    markdown_text = converter.handle(html_content)

    # Change the file extension to .md
    output_md_path = os.path.splitext(html_file_path)[0] + '.md'

    # Write Markdown to output file
    with open(output_md_path, 'w') as f:
        f.write(markdown_text)

    return output_md_path

def _get_file_encoding(file_path):
    """
    Get the encoding of a file.
    :param file_path: Path to the file
    :return:
    """
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        encoding = chardet.detect(raw_data)["encoding"]
    return encoding

def convert_to_utf8(file_path):
    """
    Convert a file to UTF-8 encoding.
    :param file_path: Path to the file
    :return:
    """
    encoding = _get_file_encoding(file_path)
    if encoding.lower() != 'utf-8':
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
            content = content.replace('†', '\u00A0') # Replace † with non-breaking space
            content = content.replace('\u00A0', ' ')
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

def __sanitize_filename(text, max_length=50):
    # Remove unwanted characters and limit length
    text = re.sub(r'[\\/*?:\"<>|]', '_', text)
    text = re.sub(r'[\r\n\t\f\v]+', '_', text.strip())
    return text[:max_length] or "untitled"

def split_html_file(html_file_path, output_dir, tag_name="hr"):
    """
    Split an HTML file into multiple files based on the given tag.
    :param html_file_path: Path to the HTML file
    :param output_dir: Directory to save the split files
    :param tag_name: Tag name to split the file (default is <hr>)
    :return:
    """
    sections = dict()

    # Create the output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Convert the file to utf-8 encoding if not already in utf-8
    convert_to_utf8(html_file_path)
    # Read and parse the input HTML file
    with open(html_file_path, 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f, 'html.parser')

    # Split the HTML content by <hr> tags
    parts = []
    current_part = []
    for element in soup.body.contents:
        if isinstance(element, str):
            continue  # skip stray strings outside tags
        if element.name == tag_name:
            parts.append(current_part)
            current_part = []
        else:
            current_part.append(element)
    if current_part:
        parts.append(current_part)

    # Write each part to a new HTML file
    for idx, part_elements in enumerate(parts):
        new_soup = BeautifulSoup('<html><head><meta charset="utf-8"></head><body></body></html>', 'html.parser')
        for el in part_elements:
            new_soup.body.append(el)

        # Get first <p> text for naming
        first_p = new_soup.body.find('p')
        title_text = first_p.get_text(separator="\n") if first_p else f"part_{idx + 1}"
        title_text_clean = __sanitize_filename(title_text.strip())
        filename_html = f"{idx + 1}_{title_text_clean}.html"

        output_path = os.path.join(output_dir, filename_html)
        print(f"[{idx+1}/{len(parts)}] {filename_html}...")
        with open(output_path, 'w', encoding="utf-8") as out_f:
            out_f.write(str(new_soup))

        # Convert the split HTML file to Markdown
        _html_file_to_markdown(output_path)

        sections[title_text_clean] = {
            "html": filename_html,
            "markdown": filename_html[:-5] + '.md'
        }

    print(f"\n\nHTML and Markdown files created for {len(parts)} splits!")
    return sections

def extract_tables_from_html(html_file_path, only_tables=True):
    """
    Extract tables from an HTML file. Works on 2 approaches
    :param html_file_path: Path to the HTML file
    :param only_tables: If True, tables are extracted using pd.read_html, otherwise using beautifulsoup along with
    other content.
    :return:
    """
    if only_tables:
        tables = pd.read_html(html_file_path, header=0)
    else:
        with open(html_file_path, "r", encoding="utf-8") as f:
            html = f.read()
        soup = BeautifulSoup(html, "lxml")
        tables = soup.find_all("table")

    return tables
