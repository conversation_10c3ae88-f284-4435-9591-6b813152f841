# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-19      #
# --------------------------------- #

"""
Utilities to log the details when running a script
"""

import logging
import logging.handlers
import os
import sys

from utils.error_codes import ERR_SYS_001


def setup_logging(log_file="app.log", level=logging.DEBUG, max_bytes=10 * 1024 * 1024, backup_count=5, app_name="my_app"):
    """
    Configure root logger to log both to console and to a rotating file.
    - log_file: path to the log file.
    - level: minimum level for the root logger.
    - max_bytes, backup_count: for RotatingFileHandler.
    """

    # Ensure log directory exists
    os.makedirs(os.path.dirname(log_file) or ".", exist_ok=True)

    # Silence everything else by raising root to WARNING
    root = logging.getLogger()
    root.setLevel(logging.WARNING)

    # Create custom logger
    app_logger = logging.getLogger(app_name)
    app_logger.setLevel(level)

    # Console handler (DEBUG+)
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)

    # File handler (DEBUG+)
    fh = logging.handlers.RotatingFileHandler(log_file, maxBytes=max_bytes, backupCount=backup_count)
    fh.setLevel(logging.DEBUG)

    fmt = logging.Formatter(
        "%(asctime)s %(name)s %(levelname)-8s %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    ch.setFormatter(fmt)
    fh.setFormatter(fmt)

    # Avoid adding multiple handlers if setup_logging is called twice
    if not app_logger.handlers:
        app_logger.addHandler(ch)
        app_logger.addHandler(fh)

    # Capture uncaught exceptions
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            # let KeyboardInterrupt go through
            return
        app_logger.error(
            f"[{ERR_SYS_001}] Uncaught exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    sys.excepthook = handle_exception

    def _new_line(self):
        """Write a raw newline (no prefix)"""
        for handler in self.handlers:
            stream = getattr(handler, "stream", None)
            if stream:
                stream.write("\n")
                stream.flush()

    # attach to the class so *all* loggers have it
    logging.Logger.new_line = _new_line

    return app_logger