# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-04-30      #
# --------------------------------- #

"""
Utilities for OpenAI API interactions
    OPENAI_API_KEY must be set as an environment variable in your local machine
"""
import json
import re
from datetime import datetime

from jsonfinder import jsonfinder
from openai import OpenAI
import os


class OpenAIUtils:

    def __init__(self):
        openai_api_key = os.environ["OPENAI_API_KEY"]
        self.client = OpenAI(api_key=openai_api_key)
        self.max_retries = 3

        # Batch related variables
        self.batch_method = "POST"
        self.batch_url = "/v1/responses"

    @staticmethod
    def __prepare_request_body(model_config, system_instructions, user_input):
        return {
            "input": user_input,
            "instructions": system_instructions,
            "model": model_config["model"],
            "temperature": model_config["temperature"],
            "max_output_tokens": model_config["max_tokens"],
            "store": model_config["store"],
            "text": {"format": model_config["response_format"]},
            "stream": False
        }

    def create_input_for_batch(self, input_dataframe, cnf, batch_input_dir):
        """Create .jsonl file for OpenAI Batch processing. The max size allowed for .jsonl file is 200MB"""
        inputs = []
        max_bytes = 195 * 1024 * 1024   # Capping to 195MB - little less than allowed max size

        for _, row in input_dataframe.iterrows():
            user_input = cnf.create_user_input(row["formula_clean"], row["clean_variables_list_for_llm"])
            request_body = self.__prepare_request_body(cnf.model_config, cnf.system_instructions, user_input)
            inputs.append({
                "custom_id": row["custom_id"],
                "method": self.batch_method,
                "url": self.batch_url,
                "body": request_body
            })

        # Save the inputs to .jsonl file in chunks
        file_names = []
        file_index = 1
        current_size = 0

        # prepare first file
        base_name = f"tiaa_inputs_{datetime.now().strftime("%Y%m%d_%H-%M-%S")}"
        file_name = f"{base_name}_{file_index}.jsonl"
        file_path = os.path.join(batch_input_dir, file_name)
        f = open(file_path, "w", encoding="utf-8")
        file_names.append(file_name)

        for rec in inputs:
            line = json.dumps(rec) + "\n"
            line_bytes = len(line.encode("utf-8"))

            # if adding this line would exceed the limit, rotate files
            if current_size + line_bytes > max_bytes:
                f.close()
                file_index += 1
                current_size = 0
                file_name = f"{base_name}_{file_index}.jsonl"
                file_path = os.path.join(batch_input_dir, file_name)
                f = open(file_path, "w", encoding="utf-8")
                file_names.append(file_name)

            f.write(line)
            current_size += line_bytes

        f.close()

        return file_names

    def upload_file(self, file_path, purpose="batch"):
        """Upload the file to OpenAI"""
        file_obj = self.client.files.create(
            file=open(file_path, "rb"),
            purpose=purpose
        )
        return file_obj

    def create_batch(self, file_id, metadata):
        """Create batch on OpenAI for the given file"""
        batch_obj = self.client.batches.create(
            input_file_id = file_id,
            endpoint=self.batch_url,
            completion_window="24h",
            metadata=metadata
        )
        return batch_obj

    def retrieve_batch(self, batch_id):
        batch_obj = self.client.batches.retrieve(batch_id)
        return batch_obj

    def retrieve_file_content(self, file_id):
        file_response = self.client.files.content(file_id)
        return file_response

    def invoke_llm(self, model_config, system_instruction, user_input, json_schema_details=None):

        # Add JSON schem details to response format if not None
        if json_schema_details and model_config["response_format"]["type"] == "json_schema":
            model_config["response_format"].update(json_schema_details)

        response = None
        request_body = self.__prepare_request_body(model_config, system_instruction, user_input)
        for attempt in range(self.max_retries):
            # Invoke the LLM using Responses API
            response = self.client.responses.create(**request_body)
            response_status = response.status

            # Wait until the LLM invocation is completed
            while response_status != "completed":
                if response_status == "failed":
                    raise Exception("LLM invocation failed!")
                if response_status == "incomplete":
                    raise Exception(f"LLM invocation is incomplete!\nDetails: {response.incomplete_details}")

                response = self.client.responses.retrieve(response.id)
                response_status = response.status

            # Validate the output; if not valid, retry
            output = response.output_text
            if not json_schema_details:
                output_valid, json_output = self.validate_output(output)
            else:
                output_valid = True
                json_output = json.loads(output)

            if output_valid:
                return json_output, response
            print("\n\n")
            print(output)

        # If the output is not valid after max retries, return the last output and error
        return {"error": "LLM could not generate valid output"}, response

    def validate_output(self, output):
        """
        Check if the LLM output is valid JSON and has the required keys (positives and negatives)
        :param output: LLM output text
        :return:
        """
        is_valid = self.is_json_convertible(output)
        if is_valid:
            output = json.loads(output)
            if "positives" in output and "negatives" in output:
                if isinstance(output["positives"], list) and isinstance(output["negatives"], list):
                    return True, output

        # If the json is enclosed in markdown style ```json...```
        pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(pattern, output, re.DOTALL)
        for block in matches:
            is_valid = self.is_json_convertible(block)
            if is_valid:
                output = json.loads(block)
                if "positives" in output and "negatives" in output:
                    if isinstance(output["positives"], list) and isinstance(output["negatives"], list):
                        return True, output

        # If the output has additional text apart from JSON, extract the json
        for _, _, j in jsonfinder(output):
            if j:
                is_valid = self.is_json_convertible(j)
                if is_valid:
                    output = json.loads(j)
                    if "positives" in output and "negatives" in output:
                        if isinstance(output["positives"], list) and isinstance(output["negatives"], list):
                            return True, output

        return False, output

    @staticmethod
    def is_json_convertible(s):
        try:
            json.loads(s)
            return True
        except Exception as e:
            return False