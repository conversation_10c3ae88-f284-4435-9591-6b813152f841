# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-07-22      #
# --------------------------------- #

"""
Common argument parsing utilities for application scripts
"""

import argparse
import os


def extract_application_path(*paths):
    """
    Extract the common application directory path from multiple file paths.

    Args:
        *paths: Variable number of file paths

    Returns:
        str: Common application directory path
    """
    if not paths:
        return None

    # Get the first path and extract application directory
    first_path = paths[0]
    # Find the applications directory in the path
    parts = first_path.split(os.sep)
    if 'applications' in parts:
        app_index = parts.index('applications')
        if app_index + 1 < len(parts):
            # Return path up to and including the application name
            return os.sep.join(parts[:app_index + 2])

    # Fallback: return directory of the first path
    return os.path.dirname(first_path)


def create_common_parser():
    """
    Create a common argument parser with application path.

    Returns:
        argparse.ArgumentParser: Parser with common arguments
    """
    parser = argparse.ArgumentParser()
    parser.add_argument('--application_path', type=str, required=False,
                        help='Base path to the application directory')
    return parser


def join_application_paths(application_path, *relative_paths):
    """
    Join application path with relative paths.

    Args:
        application_path (str): Base application path
        *relative_paths: Relative paths to join

    Returns:
        tuple: Joined paths
    """
    if not application_path:
        return relative_paths

    joined_paths = []
    for rel_path in relative_paths:
        if os.path.isabs(rel_path):
            # If already absolute, use as-is
            joined_paths.append(rel_path)
        else:
            # Join with application path
            joined_paths.append(os.path.join(application_path, rel_path))

    return tuple(joined_paths) if len(joined_paths) > 1 else joined_paths[0]


class ApplicationArgumentParser:
    """
    A parser that separates application path from other arguments for reusability.
    """

    def __init__(self, script_name, description="", default_application_path=None):
        self.script_name = script_name
        self.description = description
        self.default_application_path = default_application_path
        self.parser = argparse.ArgumentParser(description=description)
        self.file_arguments = {}

        # Add common application path argument with default if provided
        help_text = 'Base path to the application directory.'
        if default_application_path:
            help_text += f' Default: {default_application_path}'
        else:
            help_text += ' If not provided, will be auto-detected from file paths.'

        self.parser.add_argument('--application_path', '--app_path', type=str,
                                required=False, default=default_application_path,
                                help=help_text)

    def add_file_argument(self, name, relative_default_path, help_text, required=False):
        """
        Add a file argument that will be joined with application path.

        Args:
            name (str): Argument name (without --)
            relative_default_path (str): Default relative path from application directory
            help_text (str): Help text for the argument
            required (bool): Whether the argument is required
        """
        self.file_arguments[name] = relative_default_path
        # Update help text to indicate it can be relative to application_path
        enhanced_help = f"{help_text} (can be relative to --application_path or absolute)"
        self.parser.add_argument(f'--{name}', type=str, required=required,
                               help=enhanced_help)

    def add_regular_argument(self, *args, **kwargs):
        """Add a regular (non-file) argument to the parser."""
        self.parser.add_argument(*args, **kwargs)

    def parse_args(self, args=None):
        """
        Parse arguments and resolve file paths.

        Args:
            args: Arguments to parse (defaults to sys.argv)

        Returns:
            argparse.Namespace: Parsed arguments with resolved paths
        """
        parsed_args = self.parser.parse_args(args)

        # Use the default application path if none was provided via command line
        if not parsed_args.application_path and self.default_application_path:
            parsed_args.application_path = self.default_application_path

        # If still no application_path, try to extract from first file argument
        if not parsed_args.application_path:
            for arg_name, default_rel_path in self.file_arguments.items():
                file_path = getattr(parsed_args, arg_name, None)
                if file_path:
                    parsed_args.application_path = extract_application_path(file_path)
                    break
                # If no file path provided, try to extract from default
                elif default_rel_path:
                    # Try to build a potential path using default and see if we can extract app path
                    potential_path = f"../../applications/EXAMPLE/{default_rel_path}"
                    extracted = extract_application_path(potential_path)
                    if extracted:
                        parsed_args.application_path = extracted.replace("/EXAMPLE", "")
                        break

        # Resolve file paths
        for arg_name, default_rel_path in self.file_arguments.items():
            current_value = getattr(parsed_args, arg_name, None)
            if current_value is None and parsed_args.application_path:
                # Use default relative path with application path
                setattr(parsed_args, arg_name,
                       os.path.join(parsed_args.application_path, default_rel_path))
            elif current_value and parsed_args.application_path and not os.path.isabs(current_value):
                # Make relative path absolute by joining with application path
                setattr(parsed_args, arg_name,
                       os.path.join(parsed_args.application_path, current_value))
            elif current_value is None:
                # No application path and no value provided, use the relative default as-is
                setattr(parsed_args, arg_name, default_rel_path)

        return parsed_args

