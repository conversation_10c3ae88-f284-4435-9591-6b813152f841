# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-08      #
# --------------------------------- #

"""
Utilities for validating test data generated by LLM
NOTE: This is an experimental code and does not function completely as required
"""

from lark import Lark, Tree, Token


def parse_rule(r):
    grammar = r"""
        start: stmt+

        ?stmt: assignment
             | include_stmt
             | if_stmt

        assignment: NAME "=" expr
        include_stmt: "INCLUDE"

        if_stmt: "IF" "(" condition ")" "THEN" stmt* "ENDIF"

        condition: disjunction
        disjunction: conjunction ("OR" conjunction)*
        conjunction: comparison ("AND" comparison)*

        comparison: expr comp_op expr
        comp_op: "=" | "<>" | ">" | "<" | ">=" | "<="

        ?expr: atom
             | expr "&" expr   -> concat

        ?atom: NAME
             | STRING
             | NUMBER
             | func_call

        func_call: NAME "(" [arg_list] ")"
        arg_list: expr ("," expr)*

        %import common.CNAME   -> NAME
        %import common.SIGNED_NUMBER -> NUMBER
        STRING : /"([^"\\]|\\.)*"/ | /'([^'\\]|\\.)*'/

        %import common.WS
        %ignore WS
    """
    parser = Lark(grammar, start='start', parser='lalr')
    parse_tree = parser.parse(r)
    return parse_tree

def extract_branches(cond_tree):
    """
    Given a condition Tree, return a list of Trees, one per atomic branch (i.e. split on OR).
    """
    if isinstance(cond_tree, Tree) and cond_tree.data == "disjunction":
        # children are conjunctions
        branches = []
        for child in cond_tree.children:
            branches += extract_branches(child)
        return branches
    else:
        # treat whole conjunction/comparison as one branch
        return [cond_tree]

def gather_paths(stmts):
    """
    Recursively walk through the statements to build all paths
    stmts: list of Tree nodes
    returns list of dicts: { conditions: [...], actions: [...] }
    """
    paths = [{"conditions": [], "actions": []}]
    for stmt in stmts:
        new_paths = []
        for p in paths:
            if stmt.data == "assignment":
                var = stmt.children[0].value
                expr = stmt.children[1]
                p2 = {"conditions": p["conditions"][:], "actions": p["actions"][:]}
                p2["actions"].append(("assign", var, expr))
                new_paths.append(p2)

            elif stmt.data == "include_stmt":
                p2 = {"conditions": p["conditions"][:], "actions": p["actions"][:]}
                p2["actions"].append(("include",))
                new_paths.append(p2)

            elif stmt.data == "if_stmt":
                cond_tree = stmt.children[0]
                body = stmt.children[1:]
                for branch in extract_branches(cond_tree):
                    # for each OR‐branch, recurse into the THEN block
                    subpaths = gather_paths(body)
                    for sp in subpaths:
                        p2 = {
                            "conditions": p["conditions"][:] + [branch] + sp["conditions"],
                            "actions": p["actions"][:] + sp["actions"],
                        }
                        new_paths.append(p2)
            else:
                # unknown node: skip
                new_paths.append(p)
        paths = new_paths
    return paths

def eval_expr(node, values):
    """Evaluate an expr Tree or Token against values dict."""
    # Token
    if isinstance(node, Token):
        if node.type == "NAME":
            return values.get(node.value)
        if node.type == "STRING":
            # strip quotes
            return node.value[1:-1]
        if node.type == "NUMBER":
            return float(node.value) if "." in node.value else int(node.value)

    # Tree
    if isinstance(node, Tree):
        if node.data == "concat":
            l = eval_expr(node.children[0], values)
            r = eval_expr(node.children[1], values)
            return str(l) + str(r)
        if node.data == "comparison":
            left = eval_expr(node.children[0], values)
            op = node.children[1].value
            right = eval_expr(node.children[2], values)
            if op == "=":  return left == right
            if op == "<>": return left != right
            if op == ">":  return left > right
            if op == "<":  return left < right
            if op == ">=": return left >= right
            if op == "<=": return left <= right
        if node.data == "conjunction":
            return all(eval_expr(ch, values) for ch in node.children)
        # fallback: single child
        if len(node.children) == 1:
            return eval_expr(node.children[0], values)
    return None

def validate_paths(paths, values):
    """
    Evaluate each path’s conditions to tag positive/negative
    For each path dict, adds a 'result' key True/False.
    Returns new list of dicts.
    """
    out = []
    for p in paths:
        ok = all(eval_expr(cond, values) for cond in p["conditions"])
        d = p.copy()
        d["result"] = ok
        out.append(d)
    return out


rule = ("IF ( CS_AK_AK_REC_ID = \"I1\" OR CS_AK_AK_REC_ID = \"I2\" OR CS_AK_AK_REC_ID = \"I3\" ) THEN\n     "
        "INCLUDE\nENDIF")
tree = parse_rule(rule)
print(tree.pretty())

ps = gather_paths(tree.children)

print()