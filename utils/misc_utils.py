# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-08      #
# --------------------------------- #

"""
Miscellaneous utility functions
"""

import os
import string
import random
from itertools import product

from lxml import etree

from utils.xml_utils import save_xml, modify_tags_in_xml, build_xml_tree
from utils.error_codes import ERR_XML_CREATION_001


def remove_comments_from_rule(rule_formula):
    # Single-line comments start with //
    rule_lines = rule_formula.split('\\n') if "\\n" in rule_formula else [rule_formula]
    rule_lines = [line for line_temp in rule_lines for line in line_temp.split('\n')]
    rule_lines = [line for line in rule_lines if not line.strip().startswith('//')]
    return "\n".join(rule_lines).strip()

def __freeze(obj):
    if isinstance(obj, dict):
        return tuple(sorted((k, __freeze(v)) for k, v in obj.items()))
    elif isinstance(obj, list):
        return tuple(__freeze(v) for v in obj)
    elif isinstance(obj, set):
        return tuple(sorted(__freeze(v) for v in obj))
    else:
        # assume it's already hashable (int, str, etc.)
        return obj

def remove_duplicate_dicts_from_list(lst):
    seen = set()
    result = []
    for d in lst:
        if "testcase" in d: # Handling duplicate removal for final testcases set obtained from cross multiplication
            items = __freeze(d["testcase"])
        else:
            items = __freeze(d)
        if items not in seen:
            seen.add(items)
            result.append(d)
    return result

def prepare_test_data(model_output, mapping, section_test_cases=None):
    """
    Prepare test data from LLM-generated output for app report JSON
    Map the variables to corresponding XML tags if present and save these as test cases as well
    Also record unmapped variables actually being used in rule conditions
    """
    generated_data_for_json = {"positives": [], "negatives": []}
    final_data_for_json = {"positives": [], "negatives": []}    # Combination of generated data and section data
    unmapped_variables = []

    # Model output must always be a dictionary
    assert isinstance(model_output, dict)
    if "error" not in model_output:  # Look only for correct output obtained from LLM
        for key, value in model_output.items(): # {"positives": [], "negatives": []}
            value = remove_duplicate_dicts_from_list(value)

            # Model generates test cases for variables; map these to their XML tags
            for sample_data in value:
                data_with_vars = sample_data

                data_with_tags = dict()
                for var, xml_tag in sample_data.items():
                    if var in mapping:
                        data_with_tags[mapping[var]] = xml_tag
                    else:
                        if var not in unmapped_variables:
                            unmapped_variables.append(var)

                generated_data = {
                    "data_with_vars": data_with_vars,
                    "data_with_tags": data_with_tags,
                }
                generated_data_for_json[key].append(generated_data)

                # Add generated data and section data to final data
                if section_test_cases:
                    final_data_for_json[key].append({**data_with_tags, **section_test_cases[key]})
                else:
                    if data_with_tags:
                        final_data_for_json[key].append(data_with_tags)
    else:
        # If generated model output had error, LLM produced an invalid output even after retries
        generated_data_for_json = model_output
        final_data_for_json = model_output

    return generated_data_for_json, final_data_for_json, unmapped_variables

def generate_random_string(length=6):
    characters = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
    return ''.join(random.choices(characters, k=length))

def __normalize_test_data(data):
    """
    Turns dictionaries of a list to a generic wrapper format of type: {"testcase": {}, "srcs": [...]}
    Only normalizes if the incoming data is not of this type
    """
    # First, handle empty data or already in the required format
    if len(data) != 0:
        if "testcase" in data[0] and "srcs" in data[0]:
            return data
    else:
        return [{"testcase": {}, "srcs": [{}]}]

    normalized_data = [{"testcase": item, "srcs": [item]} for item in data]
    return normalized_data


def __combine_testcase_from_dicts(part1_cases, part2_cases, test_case_type):
    """
    Combine test cases for test cases of 2 parts (ex. doc, pages) - positives and negatives separately
        1. If both are empty, return empty list
        2. If one of the test cases is empty, return the non-empty test case list
        3. For each (document, page) pair:
            - If they share no keys, just merge all the keys
            - If they share keys and every shared key has identical values, merge everything.
            - If they share keys of which some have different values,
                For positives: that is contradiction in document and page rules, ignore such cases
                For negatives: merge list of all possible dictionaries where each shared key takes either its value
                from doc test case or page test case.
        """
    combined = []

    # If one of them is empty, return the other
    if not part1_cases and not part2_cases:
        return []
    if not part1_cases:
        return [{"testcase": p["testcase"], "srcs": p["srcs"][:-1] + [{}] + p["srcs"][-1:]} for p in part2_cases]
    if not part2_cases:
        # Add 2 empty dictionaries if length is even: case where component and its ref both are empty
        return [{"testcase": p["testcase"], "srcs": p["srcs"] + [{}] * (2 - len(p["srcs"]) % 2)} for p in part1_cases]

    for w1 in part1_cases:
        for w2 in part2_cases:
            # Get the testcase and sources from the wrapper testcase
            p1, srcs1 = w1["testcase"], w1["srcs"]
            p2, srcs2 = w2["testcase"], w2["srcs"]

            # Check if there are shared keys, if so find if any shared key has different value
            shared_keys = set(p1.keys()) & set(p2.keys())
            diff_value_keys = [k for k in shared_keys
                               if (isinstance(p1[k], list) and isinstance(p2[k], list) and set(p1[k]) != set(p2[k]))
                               or (not (isinstance(p1[k], list) and isinstance(p2[k], list))and p1[k] != p2[k])
                               ]

            # Merge all the keys if document and page test case does not share any key OR
            # if all shared keys have equal values
            if not shared_keys or not diff_value_keys:
                combined.append({
                    "testcase": {**p1, **p2},
                    "srcs": srcs1 + srcs2
                })
            else:
                if test_case_type == "negatives":
                    # Get 2 possible values for common keys with different value
                    diff_value_keys = sorted(diff_value_keys)
                    value_choices_per_key = [(p1[k], p2[k]) for k in diff_value_keys]

                    # Cartesian product over all choices for each overlapping key
                    for choice in product(*value_choices_per_key):
                        result = dict()
                        # Add keys from doc test case and page test case
                        for k, v in p1.items():
                            if k not in diff_value_keys:
                                result[k] = v
                        for k, v in p2.items():
                            if k not in diff_value_keys:
                                result[k] = v

                        # Assign each shared key to the choice in this iteration
                        for k, v in zip(diff_value_keys, choice):
                            result[k] = v

                        combined.append({
                            "testcase": result,
                            "srcs": srcs1 + srcs2
                        })
                else:
                    # For positive test case with contradiction in key values are not considered
                    pass

    # Remove duplicate dictionaries from the list
    combined = remove_duplicate_dicts_from_list(combined)

    return combined

def __cross_multiply_testcases(dict1, dict2):
    """Create testcases by combining testcases for rules of 2 parts"""
    final_test_data = dict()

    # Combine testcases for positives and negatives separately
    for test_type in ["positives", "negatives"]:
        test_data_1 = __normalize_test_data(dict1.get(test_type, []))
        test_data_2 = __normalize_test_data(dict2.get(test_type, []))

        final_test_data[test_type] = __combine_testcase_from_dicts(test_data_1, test_data_2, test_type)

    # Check for equal number of sources if one of the test type is empty
    pos = final_test_data["positives"]
    neg = final_test_data["negatives"]
    if len(pos) == 0 and len(neg) != 0:
        src_len = len(neg[0]["srcs"])
        final_test_data["positives"] = [{"testcase": {}, "srcs": [{}] * src_len}]
    elif len(neg) == 0 and len(pos) != 0:
        src_len = len(pos[0]["srcs"])
        final_test_data["negatives"] = [{"testcase": {}, "srcs": [{}] * src_len}]

    return final_test_data

def __get_rule_data_and_id(rule):
    if rule:
        try:
            rule_id = rule["rule-oi"]
        except KeyError:
            if "id" in rule:
                rule_id = rule["id"]
            else:   # If rule id is not present, only a section test case may be present
                # Even if rule id is not present, rule-status and test-cases will be present
                return rule["test-cases"], "norule", rule["rule-status"]["success"]
        if "test-cases" not in rule:    # undefined rule
            return dict(), rule_id, False
        return rule["test-cases"], rule_id, rule["rule-status"]["success"]
    return dict(), "", True

def __combine_and_yield(document_rule_id, page_rule_id, component_rule_id, component_ref_rule_id, doc_page_data,
                        component_testcases, component_ref_testcases):
    """Combine testcases and yield with XML path prefix"""
    combined_ref_data = __cross_multiply_testcases(component_testcases, component_ref_testcases)
    final_data = __cross_multiply_testcases(doc_page_data, combined_ref_data)

    xml_path_prefix = f"doc_{document_rule_id}_page_{page_rule_id}_comp_{component_rule_id}_ref_{component_ref_rule_id}_"
    yield final_data, xml_path_prefix

def __combine_and_yield_sections(document_rule_id, section_rule_id, paragraph_rule_id, component_rule_id, component_ref_rule_id,
                                doc_section_paragraph_data, component_testcases, component_ref_testcases):
    """Combine testcases and yield with XML path prefix for sections hierarchy"""
    combined_ref_data = __cross_multiply_testcases(component_testcases, component_ref_testcases)
    final_data = __cross_multiply_testcases(doc_section_paragraph_data, combined_ref_data)

    xml_path_prefix = f"doc_{document_rule_id}_sec_{section_rule_id}_para_{paragraph_rule_id}_comp_{component_rule_id}_ref_{component_ref_rule_id}_"
    yield final_data, xml_path_prefix

def __get_testcases_and_path_prefix(document, include_components):
    """Get test cases by considering combination of rules for documents, pages, components and component refs"""
    document_testcases, document_rule_id, document_success = __get_rule_data_and_id(document["rule"])
    if not document_success:
        # Yield the failure and stop processing this document
        yield document_success
        return

    # Iterate through the pages in the document and get test cases for each page
    for page in document["pages"]:
        page_testcases, page_rule_id, page_success = __get_rule_data_and_id(page["rule"])
        if not page_success:
            # Yield the failure and skip to next page
            yield page_success
            continue

        # Cross multiply document and page test cases
        combined_doc_page_data = __cross_multiply_testcases(document_testcases, page_testcases)

        # Iterate through the components if present and requested
        if include_components and page.get("components"):
            for component in page["components"]:
                component_testcases, component_rule_id, component_success = __get_rule_data_and_id(component["rule"])
                if not component_success:
                    # Yield the failure and skip to next component
                    yield component_success
                    continue

                # Get testcases for each ref-rule of the component if present
                if not component["ref-rules"]:
                    # No ref-rules, use empty dict and id
                    yield from __combine_and_yield(
                        document_rule_id, page_rule_id, component_rule_id, "",
                        combined_doc_page_data, component_testcases, dict()
                    )
                else:
                    for component_ref_rule in component["ref-rules"]:
                        ref_testcases, ref_rule_id, ref_success = __get_rule_data_and_id(component_ref_rule)
                        if not ref_success:
                            # Yield the failure and skip to next ref-rule
                            yield ref_success
                            continue

                        yield from __combine_and_yield(
                            document_rule_id, page_rule_id, component_rule_id, ref_rule_id,
                            combined_doc_page_data, component_testcases, ref_testcases
                        )

        else:
            # No components in page or not requested
            yield from __combine_and_yield(
                document_rule_id, page_rule_id, "", "",
                combined_doc_page_data, dict(), dict()
            )

def __get_testcases_and_path_prefix_sections(document, include_components):
    """Get test cases by considering combination of rules for documents, sections, paragraphs, components and component refs"""
    document_testcases, document_rule_id, document_success = __get_rule_data_and_id(document["rule"])
    if not document_success:
        # Yield the failure and stop processing this document
        yield document_success
        return

    # Iterate through the sections in the document and get test cases for each section
    for section in document["sections"]:
        section_testcases, section_rule_id, section_success = __get_rule_data_and_id(section["rule"])
        if not section_success:
            # Yield the failure and skip to next section
            yield section_success
            continue

        # Cross multiply document and section test cases
        combined_doc_section_data = __cross_multiply_testcases(document_testcases, section_testcases)

        # Iterate through the paragraphs in the section
        for paragraph in section["paragraphs"]:
            paragraph_testcases, paragraph_rule_id, paragraph_success = __get_rule_data_and_id(paragraph["rule"])
            if not paragraph_success:
                # Yield the failure and skip to next paragraph
                yield paragraph_success
                continue

            # Cross multiply document-section data with paragraph test cases
            combined_doc_section_paragraph_data = __cross_multiply_testcases(combined_doc_section_data, paragraph_testcases)

            # Iterate through the components if present and requested
            if include_components and paragraph.get("components"):
                for component in paragraph["components"]:
                    component_testcases, component_rule_id, component_success = __get_rule_data_and_id(component["rule"])
                    if not component_success:
                        # Yield the failure and skip to next component
                        yield component_success
                        continue

                    # Get testcases for each ref-rule of the component if present
                    if not component["ref-rules"]:
                        # No ref-rules, use empty dict and id
                        yield from __combine_and_yield_sections(
                            document_rule_id, section_rule_id, paragraph_rule_id, component_rule_id, "",
                            combined_doc_section_paragraph_data, component_testcases, dict()
                        )
                    else:
                        for component_ref_rule in component["ref-rules"]:
                            ref_testcases, ref_rule_id, ref_success = __get_rule_data_and_id(component_ref_rule)
                            if not ref_success:
                                # Yield the failure and skip to next ref-rule
                                yield ref_success
                                continue

                            yield from __combine_and_yield_sections(
                                document_rule_id, section_rule_id, paragraph_rule_id, component_rule_id, ref_rule_id,
                                combined_doc_section_paragraph_data, component_testcases, ref_testcases
                            )

            else:
                # No components in paragraph or not requested
                yield from __combine_and_yield_sections(
                    document_rule_id, section_rule_id, paragraph_rule_id, "", "",
                    combined_doc_section_paragraph_data, dict(), dict()
                )

def create_xml_from_payload(document, xml_tree, xml_dir_dict, skipped_elems_df, element_types_df, logger,
                            include_components=True):
    """
    Combine testcases for different parts of document and create XMLs from XSD schema
    :param document: document from app report json
    :param xml_tree: Parsed XML tree
    :param xml_dir_dict: Dictionary containing relevant XML directory paths for positives and negatives
    :param skipped_elems_df: Dataframe for elements skipped in XML because of 'choice'
    :param element_types_df: Element types dataframe to get the maxoccurs
    :param logger: Logger object to log information
    :param include_components: If true, include component rules
    :return: None
    """
    tsv_data = []
    missing_tags, missed_xml_count = set(), 0

    # Process both hierarchies: Document → Page → Component → Component Refs
    for res in __get_testcases_and_path_prefix(document, include_components):
        if not isinstance(res, bool):   # Only process for successful results; for failures this will be a boolean
            combined_data, xml_prefix = res

            # If neither of doc, page, component or component ref had any rules, skip
            if xml_prefix == "doc__page__comp__ref__":
                continue

            for test_type in combined_data:  # "positives" or "negatives"
                data = combined_data[test_type]
                for d in data:
                    test_data = d["testcase"]

                    # Continue if test_data is empty
                    if len(test_data) == 0:
                        continue

                    # The output of modify_tags_in_xml could be modified tree or missing elements list
                    test_data_xml = modify_tags_in_xml(xml_tree, test_data, skipped_elems_df, element_types_df)

                    if not isinstance(test_data_xml, list):
                        file_name = xml_prefix + generate_random_string() + ".xml"
                        output_xml_path = os.path.join(xml_dir_dict[test_type], file_name)
                        save_xml(test_data_xml, output_xml_path)
                        missing_tags_for_test_data = []
                    else:
                        file_name = xml_prefix + generate_random_string() + "-NOT-CREATED" + ".xml"
                        missing_tags_for_test_data = test_data_xml

                        # Update the missing tags set
                        missing_tags.update(test_data_xml)
                        missed_xml_count += 1

                    # Write the entry for tsv data - pad sources to 5 levels
                    sources = d["srcs"] + [{}] * (5 - len(d["srcs"]))  # Pad to 5 levels
                    entry = [file_name, test_type, test_data, sources[0], sources[1], sources[2], sources[3], sources[4], missing_tags_for_test_data]
                    tsv_data.append(entry)

    # Process second hierarchy: Document → Section → Paragraph → Component → Component Ref
    if document.get("sections"):  # Only process if sections exist
        for res in __get_testcases_and_path_prefix_sections(document, include_components):
            if not isinstance(res, bool):   # Only process for successful results; for failures this will be a boolean
                combined_data, xml_prefix = res

                # If neither of doc, section, paragraph, component or component ref had any rules, skip
                if xml_prefix == "doc__sec__para__comp__ref__":
                    continue

                for test_type in combined_data:  # "positives" or "negatives"
                    data = combined_data[test_type]
                    for d in data:
                        test_data = d["testcase"]

                        # Continue if test_data is empty
                        if len(test_data) == 0:
                            continue

                        # The output of modify_tags_in_xml could be modified tree of missing elements list
                        test_data_xml = modify_tags_in_xml(xml_tree, test_data, skipped_elems_df, element_types_df)

                        if not isinstance(test_data_xml, list):
                            file_name = xml_prefix + generate_random_string() + ".xml"
                            output_xml_path = os.path.join(xml_dir_dict[test_type], file_name)
                            save_xml(test_data_xml, output_xml_path)
                            missing_tags_for_test_data = []
                        else:
                            file_name = xml_prefix + generate_random_string() + "-NOT-CREATED" + ".xml"
                            missing_tags_for_test_data = test_data_xml

                            # Update the missing tags set
                            missing_tags.update(test_data_xml)
                            missed_xml_count += 1

                        # Write the entry for tsv data - pad sources to 5 levels
                        sources = d["srcs"] + [{}] * (5 - len(d["srcs"]))  # Pad to 5 levels
                        entry = [file_name, test_type, test_data, sources[0], sources[1], sources[2], sources[3], sources[4], missing_tags_for_test_data]
                        tsv_data.append(entry)

    # If missing tags are present, log them in the error
    if missed_xml_count > 0:
        err_xml_creation_001_msg = (f"[{ERR_XML_CREATION_001}] Total unique missing tags: {' '.join(missing_tags)}"
                                    f"\n\t#XML files not created because of missing tags: {missed_xml_count}")
        logger.new_line()
        logger.error(err_xml_creation_001_msg)

    return tsv_data

def create_xml_from_xsd(document, xsd_schema, xsd_tree, element_types_df, namespaces, xml_dir_dict, include_components=True):
    """Combine testcases for different parts of document and create XMLs from XSD schema"""
    tsv_data = []

    # Process both hierarchies: Document → Page → Component → Component Refs
    for res in __get_testcases_and_path_prefix(document, include_components):
        if not isinstance(res, bool):   # Only process for successful results; for failures this will be a boolean
            combined_data, xml_prefix = res
            # For each test type (positives and negatives), create XML file for every test case
            for test_type in combined_data: # "positives" or "negatives"
                data = combined_data[test_type]
                for d in data:
                    test_data = d["testcase"]
                    # Build the tree with ordered inserts
                    xml_root = build_xml_tree(xsd_schema, xsd_tree, test_data, element_types_df, namespaces)
                    xml_bytes = etree.tostring(xml_root, pretty_print=True, xml_declaration=True, encoding="UTF-8")

                    # # Validate the generated xml with the schema
                    # try:
                    #     xsd_schema.validate(xml_bytes)
                    # except Exception as e:
                    #     print("\n\n===================================")
                    #     print(f"\n{type(e).__name__}: {e}")
                    #     print("===================================")
                    #     continue

                    # Write the xml to a file
                    file_name = xml_prefix + "_" + generate_random_string() + ".xml"
                    output_xml_path = os.path.join(xml_dir_dict[test_type], file_name)
                    with open(output_xml_path, "wb") as f:
                        f.write(xml_bytes)
                    print(f"XML file saved at {os.path.abspath(output_xml_path)}")

                    # Write the entry for tsv data - pad sources to 5 levels
                    sources = d["srcs"] + [{}] * (5 - len(d["srcs"]))  # Pad to 5 levels
                    entry = [file_name, test_type, test_data, sources[0], sources[1], sources[2], sources[3], sources[4]]
                    tsv_data.append(entry)

    # Process second hierarchy: Document → Section → Paragraph → Component → Component Ref
    if document.get("sections"):  # Only process if sections exist
        for res in __get_testcases_and_path_prefix_sections(document, include_components):
            if not isinstance(res, bool):   # Only process for successful results; for failures this will be a boolean
                combined_data, xml_prefix = res
                # For each test type (positives and negatives), create XML file for every test case
                for test_type in combined_data: # "positives" or "negatives"
                    data = combined_data[test_type]
                    for d in data:
                        test_data = d["testcase"]
                        # Build the tree with ordered inserts
                        xml_root = build_xml_tree(xsd_schema, xsd_tree, test_data, element_types_df, namespaces)
                        xml_bytes = etree.tostring(xml_root, pretty_print=True, xml_declaration=True, encoding="UTF-8")

                        # # Validate the generated xml with the schema
                        # try:
                        #     xsd_schema.validate(xml_bytes)
                        # except Exception as e:
                        #     print("\n\n===================================")
                        #     print(f"\n{type(e).__name__}: {e}")
                        #     print("===================================")
                        #     continue

                        # Write the xml to a file
                        file_name = xml_prefix + "_" + generate_random_string() + ".xml"
                        output_xml_path = os.path.join(xml_dir_dict[test_type], file_name)
                        with open(output_xml_path, "wb") as f:
                            f.write(xml_bytes)
                        print(f"XML file saved at {os.path.abspath(output_xml_path)}")

                        # Write the entry for tsv data - pad sources to 5 levels
                        sources = d["srcs"] + [{}] * (5 - len(d["srcs"]))  # Pad to 5 levels
                        entry = [file_name, test_type, test_data, sources[0], sources[1], sources[2], sources[3], sources[4]]
                        tsv_data.append(entry)

    return tsv_data
