# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-06      #
# --------------------------------- #

"""
Instructions, prompts, and utilities for XML data generation using LLM
"""
import json
from textwrap import dedent


class XMLDataGenerationConfig:

    def __init__(self, model="gpt-4.1", temperature=0.5, max_tokens=8000):
        self.model_config = {
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "store": False,
            "response_format": {"type": "json_object"}
        }

        self.system_instructions = self.__get_system_instructions()

    @staticmethod
    def __get_system_instructions():
        system_instructions = dedent("""\
        You are an expert in rule-based logic and synthetic data generation. Your task is to generate synthetic test cases based on logical rules and provided variable definitions. Strictly follow these steps:

        1. Boolean Logic Analysis
        - Understand the boolean logic structure of the given rule.
        - Identify variables used within the rule from the provided variables list.
        - Identify all logical branches that cause the rule to evaluate as TRUE or FALSE.
        
        2. Testcases Generation
        - First, identify ALL the conditional constructs from the entire rule to extract the set of variables it uses.
        - Next, filter the extracted variables set to ONLY those variables included in the provided variable list.
        - Generate test cases for the filtered variables ONLY.
        - DO NOT generate values for any deterministic or derived variables that are NOT used in any conditional construct.
        - DO NOT generate values if no conditional constructs are present in the rule.
        - *SPECIAL CASE:* In cases of no conditional constructs in the rule, you must generate test cases ONLY if the 
        rule is a comparison statement involving single variable only; for example, VAR_1 = "VALUE_1"
        
        - Positive Cases
            - Generate ALL possible valid combinations of variable values that evaluate the rule to TRUE.
            - Include every logical permutation (e.g., all branches of OR/AND)
        - Negative Cases
            - Generate valid combinations of variable values that cause the rule to evaluate to FALSE.
            - Include every distinct logical pathway that leads to a FALSE evaluation.
        
        3. Metadata-driven Values
        - Respect the variable definitions provided
            - Use the provided variable type (e.g., String, Number, Currency etc.) and infer the semantics from XML tag if provided (xml-schema.tag-name).
            - Adhere strictly to xml-schema.type-restriction if provided
            - The final generated values must conform exactly to the xml-schema.xsd-type format.
            - Match the formatting of the provided sample-value only if it adheres to the provided xml-schema.xsd-type type.
            - If is-array is true, generate an array of correctly typed values.  
        - Never emit empty strings or 'null' - unless the rule explicitly compares to an empty string.
        - Never generate negative values for amounts and currency.
        - *Important Formatting constraints*
            - For numeric or date-related values representing raw data (e.g., counts, numbers, percentages, amounts, or dates), output ONLY digits WITHOUT decimals, commas, percentage symbols, hyphens, currency symbols, or other formatting.
            - For PDF file names, output only the name, without .pdf extension.
        
        4. Output Format
            - Your output must ONLY be a valid JSON format as follows:
                {
                    "positives": [ {"$VAR_1_NAME": "$VAR_1_VALUE", "$VAR_2_NAME": "$VAR_2_VALUE",  .... }, .... ],
                    "negatives": [ {"$VAR_1_NAME": "$VAR_1_VALUE", "$VAR_2_NAME": "$VAR_2_VALUE",  .... }, .... ]
                }
            - If no conditional constructs are present in the rule, your output must be empty positives and negatives as follows:
                { "positives": [], "negatives": [] }
        
        
        EXAMPLE 1
        **Rule:**
        IF(CK_BA_CUST_AMT > 0 OR CK_BB_CUST_NAME=="John") THEN
             INCLUDE
        ENDIF
        
        **Variables:**
        [
            {"name": "CK_BA_CUST_AMT", "type":"String", "is-array": false, "xml-schema": {"tag-name": "CustAmt", "sample-value": 10, "xsd-type": "xsd:string", "type-restriction": "<xsd:maxLength value='10'/>"}},
            {"name": "CK_BB_CUST_NAME", "type":"String", "is-array": false, "xml-schema": {"tag-name": "CustName", "sample-value": "James, "xsd-type": "xsd:string"}}
        ]
        
        **Output JSON:**
        { 
            "positives" : [
                {"CK_BA_CUST_AMT": 21243, "CK_BB_CUST_NAME": "James"},
                {"CK_BA_CUST_AMT": 0, "CK_BB_CUST_NAME": "John"}
            ],
            "negatives" : [
                {"CK_BA_CUST_AMT": 0, "CK_BB_CUST_NAME": "Angela"},
                {"CK_BA_CUST_AMT": 0, "CK_BB_CUST_NAME": "SSDD"}
            ]
        }
        
        EXAMPLE 2
        **Rule:**
        CK_BB_CUST_ID = "11"
        
        **Variables:**
        [
            {"name": "CK_BB_CUST_ID", "type":"String", "is-array": false, "xml-schema": {"tag-name": "CustId", 
            "sample-value": 10, "xsd-type": "xsd:string", "type-restriction": "<xsd:maxLength value='2'/>"}}
        ]
        
        **Output JSON:**
        { 
            "positives" : [
                {"CK_BB_CUST_ID": 11}
            ],
            "negatives" : [
                {"CK_BB_CUST_ID": 10},
                {"CK_BB_CUST_ID": 12}
            ]
        }
        
        EXAMPLE 3
        **Rule:**
        IF (SYS_QueueCurrent <>  "BB_GO PScript - Duplex - Indiv Files - IMAGE ONLY  (PROD)")
        and  (SYS_QueueCurrent <> "BB_GO PScript - Duplex - Indiv Files - IMAGE ONLY (TEST)") THEN
            IF BB_CB_ONETIME_TRANSFER_BENE_IND= "Y" or BB_CB_ONETIME_TRANSFER_SPOUSE_IND= "Y"  THEN 
                GO_DOC_STITCH_SET_VALUE_CNTR = GO_DOC_STITCH_SET_VALUE_CNTR + 1
                INCLUDE
            ENDIF
        ENDIF
        
        **Variables:**
        [
            {"name": "SYS_QueueCurrent", "type":"String", "is-array": false},
            {"name": "BB_CB_ONETIME_TRANSFER_BENE_IND", "type":"String", "is-array": false, "xml-schema": {"tag-name": "OnetimeTransferBeneInd", "sample-value": Y, "xsd-type": "xsd:string", "type-restriction": "<xsd:maxLength value='1'/>"}},
            {"name": "BB_CB_ONETIME_TRANSFER_SPOUSE_IND", "type":"String", "is-array": false, "xml-schema": {"tag-name": "OnetimeTransferBeneInd", "sample-value": Y, "xsd-type": "xsd:string", "type-restriction": "<xsd:enumeration value='Y'/><xsd:enumeration value='N'/>"}}
        ]
        
        **Output JSON:**
        { 
            "positives" : [
                {SYS_QueueCurrent: "Duplex Sandbox", "BB_CB_ONETIME_TRANSFER_BENE_IND": "Y", "BB_CB_ONETIME_TRANSFER_SPOUSE_IND": "N"},
                {SYS_QueueCurrent: "SomeValue", "BB_CB_ONETIME_TRANSFER_BENE_IND": "Y", "BB_CB_ONETIME_TRANSFER_SPOUSE_IND": "Y"},
                {SYS_QueueCurrent: "SomeValue", "BB_CB_ONETIME_TRANSFER_BENE_IND": "N", "BB_CB_ONETIME_TRANSFER_SPOUSE_IND": "Y"},
                {"name":"GO_DOC_STITCH_SET_VALUE_CNTR","type":"Integer","is-array":false}
            ],
            "negatives" : [
                {SYS_QueueCurrent: "BB_GO PScript - Duplex - Indiv Files - IMAGE ONLY  (PROD)", "BB_CB_ONETIME_TRANSFER_BENE_IND": "N", "BB_CB_ONETIME_TRANSFER_SPOUSE_IND": "N"},
                {SYS_QueueCurrent: "BB_GO PScript - Duplex - Indiv Files - IMAGE ONLY (TEST)", "BB_CB_ONETIME_TRANSFER_BENE_IND": "Y", "BB_CB_ONETIME_TRANSFER_SPOUSE_IND": "Y"}
            ]
        }""")
        return system_instructions

    @staticmethod
    def create_user_input(rule_formula, variables_list):
        """
        Create the user input for the LLM
        :return: User input string formatted for xml data generation
        """
        # Create the user input string
        rule_formula = rule_formula.replace("\\n", "\n")
        user_input = (f"**Rule:**\n{rule_formula}\n\n"
                      + f"**Variables:**\n{json.dumps(variables_list)}\n\n"
                      + f"**Output JSON:**\n")

        return user_input
