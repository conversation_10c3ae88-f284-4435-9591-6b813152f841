# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-17      #
# --------------------------------- #

"""
Instructions, prompts, and utilities to generate a sample value for an XSD built-in primitive type dynamically.
"""
import json
from textwrap import dedent


class ElementValueGenerationConfig:

    def __init__(self, model="gpt-4.1", temperature=1.0, max_tokens=8000):
        self.model_config = {
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "store": False,
            "response_format": {"type": "json_schema"}
        }

        self.system_instructions = self.__get_system_instructions()

    @staticmethod
    def __get_system_instructions():
        system_instructions = dedent("""\
        Your goal is to generate a realistic example value for a single XML element based on provided metadata.
        Given the following input fields:
        - element_name: The name of the XML tag
        - element_path: The XPath indicating its location in the XML document
        - xsd_type: XSD data type (e.g. xsd:string, xsd:date, xsd:decimal, etc.)
        - type_restriction: An optional XSD constraint (facet), such as <xsd:maxLength value="185"/>. This may be null if there are no restrictions.
        - variable_type: An optional variable type that should influence value generation.

        Follow the below instructions to produce the example value:
        1. Infer the semantic meaning from the element's name and XPath (e.g., "FullName" under "MailItemInfo" implies a person's full name).
        2. Generate a plausible example value that:
            - Matches the inferred semantics and provided variable_type.
            - Adheres strictly to any given restrictions (type_restriction).
            - Conforms exactly to the specified xsd_type format.
            - Never generate negative values for amounts and currency.
        3. *Important Formatting Constraints*
            - For numeric or date-related values representing raw data (e.g., counts, numbers, percentages, amounts, 
            or dates), output ONLY digits WITHOUT decimals, commas, percentage symbols, hyphens, currency symbols, 
            or other formatting.
            - For PDF names, output only the name without .pdf extension.
        4. Present your output as a minimal JSON object, using only the element_name as the key, and the generated value as the associated value.
        
        EXAMPLE:
        **Input**
        {
            "element_name": "FullName",
            "element_path": "/DocumentRequest/MailItemInfo/FullName",
            "xsd_type": "xsd:string",
            "type_restriction": "<xsd:maxLength value='185'/>",
            "variable_type": "String"
        }
        
        **Output**
        {
            "FullName": "John D. Maxwell"
        }""")

        return system_instructions

    def create_user_input(self, name, path, el_type, el_restriction, variable_type):
        """
        Create the user input for the LLM
        """
        input_json = {
            "element_name": name,
            "element_path": path,
            "xsd_type": el_type,
            "type_restriction": el_restriction
        }
        if variable_type:
            input_json["variable_type"] = variable_type


        # Create the user input string
        user_input = (f"**Input:**\n{json.dumps(input_json)}\n\n"
                      + f"**Output:**\n")

        schema = self.build_json_schema(name)
        schema_details = {
            "name": "element_value",
            "strict": True,
            "schema": schema
        }

        return user_input, schema_details

    @staticmethod
    def build_json_schema(name):
        return {
            "type": "object",
            "properties": {
                name: {
                    "type": "string"
                }
            },
            "additionalProperties": False,
            "required": [name]
        }
