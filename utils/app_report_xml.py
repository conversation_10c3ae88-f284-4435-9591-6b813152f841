# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-23      #
# --------------------------------- #

"""
Class for HTML Application Report Parser
"""
import csv
import html
import itertools
import json
import os
import random
import string

import pandas as pd
from lxml import etree

from utils.misc_utils import remove_comments_from_rule
from utils.xml_utils import parse_xml, save_xml
from utils.error_codes import (ERR_FILE_004, ERR_APP_REPORT_001, ERR_APP_REPORT_002, ERR_RULE_001, ERR_RULE_001_01, ERR_RULE_001_02,
                               ERR_RULE_001_03, ERR_RULE_001_05)


class XMLApplicationReportParser:

    def __init__(self, app_report_path, log_dir, logger):

        self.logger = logger

        # App report path must be XML
        if not (app_report_path.endswith('.xml')):
            raise ValueError(f"[{ERR_FILE_004}] Application report must be XML.")
        self.app_report_path_xml = app_report_path

        # Create relevant directories to save the parsed information
        xml_parse_dir_name = "xml_parse"
        self.xml_parse_dir = os.path.join(os.path.dirname(self.app_report_path_xml), xml_parse_dir_name)
        if not os.path.exists(self.xml_parse_dir):
            os.mkdir(self.xml_parse_dir)

        sections_dir = "app_sections"
        self.sections_dir_path = os.path.join(self.xml_parse_dir, sections_dir)
        if not os.path.exists(self.sections_dir_path):
            os.mkdir(self.sections_dir_path)

        info_dir = "app_info"
        self.info_dir_path = os.path.join(self.xml_parse_dir, info_dir)
        if not os.path.exists(self.info_dir_path):
            os.mkdir(self.info_dir_path)

        self.error_file_path = os.path.join(log_dir, "rule_errors_from_parsing.tsv")
        self.paras_with_variableuse_path = os.path.join(log_dir, "ParagraphsWithVariableUse.xml")

        self.app_name = ""
        self.sections_of_interest = self.get_sections_of_interest()

        self.variables_df = pd.DataFrame()
        self.xml_tags_df = pd.DataFrame()
        self.mapping_df = pd.DataFrame()
        self.rules_df = pd.DataFrame()

        # Related to testcases generation
        self.testcases_generation_details = [] # will be used for testcases_generation_input_df
        self.testcases_generation_input_df = pd.DataFrame() # based on unique rule formula
        self.rule_errors_details = [] # will be used for rule_errors_df
        self.rule_errors_df = pd.DataFrame()

        self.json_report = {
            "app-name": "",
            "documents": []
        }

    def run(self):
        """Get required information from the application report (variables, XML tags, rules)"""
        self.extract_relevant_xml_sections()
        self.logger.new_line()

        self.logger.info("Extracting variables...")
        self._extract_variables()
        self.logger.info("Extracting XML tags and variable-xml mappings...")
        self._extract_xml_tags_and_mapping()
        self.logger.info("Extracting all the rules...")
        self._extract_all_rules()
        self.logger.new_line()

        self.logger.info("Preparing final JSON report and details for test cases generation...")
        # self.logger.info("\tExtracting documents and pages list with usage rules, targeting and variables...")
        self._prepare_json_report_and_testcase_details()
        self.logger.new_line()
        self.logger.new_line()

        self.logger.info("========================= APP REPORT STATS =========================")
        self._get_app_report_stats()

    @staticmethod
    def get_sections_of_interest():
        return {
            "documents": {"tag": "DocumentList", "filename": "DocumentList.xml"},
            "pages": {"tag": "PageList", "filename": "PageList.xml"},
            "sections": {"tag": "SectionList", "filename": "SectionList.xml"},
            "paragraphs": {"tag": "ParagraphList", "filename": "ParagraphList.xml"},
            "variables": {"tag": "VariableList", "filename": "VariableList.xml"},
            "rules": {"tag": "RuleList", "filename": "RuleList.xml"},
            "xml_layout": {"tag": "DataFiles", "filename": "XmlLayout.xml"}
        }

    def __get_sections_xml_root(self, section_name):
        filename = self.sections_of_interest[section_name]["filename"]
        file_path = os.path.join(self.sections_dir_path, filename)

        # If file does not exist, return None
        if not os.path.exists(file_path):
            return None

        _, root = parse_xml(file_path)
        return root

    def extract_relevant_xml_sections(self):
        _, root = parse_xml(self.app_report_path_xml)

        # Get the app name from the root element
        if root.tag == "Application":
            self.app_name = root.attrib.get("Name")
            self.json_report["app-name"] = self.app_name
        else:
            raise Exception(f"[{ERR_APP_REPORT_001}] Application not found at the root of XML report.")

        self.logger.info(f"========================= APPLICATION: {self.app_name} =========================")
        self.logger.new_line()

        self.logger.info(f"========================= APP REPORT PARSING =========================")
        self.logger.info("Extracts relevant sections and details from application report.")
        self.logger.new_line()
        self.logger.info("Extracting sections...")

        for section, details in self.sections_of_interest.items():
            tag = details["tag"]
            output_filename = details["filename"]

            element = root.find(f".//{tag}")
            if element is not None:
                # Special case for <DataFile type="Customer Driver File"> and <Layouts> under it
                if section == "xml_layout":
                    data_files = element.findall(".//DataFile[@Type='Customer Driver File']")
                    if len(data_files) != 1:
                        raise Exception(f"[{ERR_APP_REPORT_002}] Multiple or NO DataFiles found as customer driver file at the root "
                                        f"of XML report.")

                    name = data_files[0].attrib.get("Name", "DataFile")
                    layout_element = data_files[0].find(".//Layouts")
                    if layout_element is not None:
                        output_filepath = os.path.join(self.sections_dir_path, output_filename)
                        with open(output_filepath, "wb") as f:
                            f.write(etree.tostring(layout_element, pretty_print=True, xml_declaration=True, encoding="UTF-8"))
                        self.logger.info(f"\tXMLLayout from '{name}': {os.path.abspath(output_filepath)}")

                    continue

                output_filepath = os.path.join(self.sections_dir_path, output_filename)
                with open(output_filepath, "wb") as f:
                    f.write(etree.tostring(element, pretty_print=True, xml_declaration=True, encoding="UTF-8"))
                self.logger.info(f"\t{tag}: {os.path.abspath(output_filepath)}")

    def _extract_variables(self):
        vars_root = self.__get_sections_xml_root("variables")

        columns = ["VarOI", "Name", "Type", "Source", "ResetTime", "Access", "IsArray", "Sample", "Description"]
        data = []

        # Extract variable information
        for var in vars_root.findall("Variable"):
            var_data = {
                "VarOI": var.get("VarOI", ""),
                "Name": var.get("Name", ""),
                "Type": var.get("Type", ""),
                "Source": var.get("Source", ""),
                "ResetTime": var.get("ResetTime", ""),
                "Access": var.get("Access", ""),
                "IsArray": var.get("IsArray", ""),
                "Sample": "",
                "Description": ""
            }
            # Optional nested tags
            sample_elem = var.find("Sample")
            desc_elem = var.find("Description")

            if sample_elem is not None and sample_elem.text:
                var_data["Sample"] = sample_elem.text.strip()
            if desc_elem is not None and desc_elem.text:
                var_data["Description"] = desc_elem.text.strip()

            data.append(var_data)

        self.variables_df = pd.DataFrame(data, columns=columns)
        vars_file_path = os.path.join(self.info_dir_path, "variables.tsv")
        self.variables_df.to_csv(vars_file_path, sep="\t", header=True, index=False)
        self.logger.info(f"\tVariables: {os.path.abspath(vars_file_path)}")

    def _extract_xml_tags_and_mapping(self):
        xml_root = self.__get_sections_xml_root("xml_layout")

        # Get variable type and sample value into the mapping; for this variables dataframe must be extracted
        assert not self.variables_df.empty, "Variables are not extracted!"

        xml_columns = ["Tag", "Ignore", "NewCustomer", "NextTag", "NewSection", "Section", "FirstOnly"]
        xml_data = []

        mapping_columns = ["Tag", "VarOI", "Name", "VarType", "VarSampleValue", "Area", "Start", "Length", "ImpliedDigits", "Format",
                           "Action"]
        mapping_data = []

        # Iterate over each XMLLayout element
        for layout in xml_root.findall("XMLLayout"):
            # Get XML data
            entry = {col: layout.get(col, '') for col in xml_columns}
            xml_data.append(entry)

            # Get variable-XML mapping data
            tag_name = layout.get("Tag", "")
            data_areas = layout.find("DataAreas")
            if data_areas is not None:
                for area in data_areas.findall("DataArea"):
                    format_elem = area.find("Format")

                    # Continue if variable name is not present
                    variable_name = area.get("Name", "")
                    if not variable_name:
                        continue

                    # Get the Type and Sample from variables dataframe
                    variable_data = self.variables_df[self.variables_df["Name"] == variable_name]
                    variable_type, variable_sample_value = "", ""
                    if len(variable_data) != 0:
                        variable_type = variable_data.iloc[0]["Type"]
                        variable_sample_value = variable_data.iloc[0]["Sample"]

                    entry = {
                        "Tag": tag_name,
                        "VarOI": area.get("VarOI", ""),
                        "Name": variable_name,
                        "VarType": variable_type,
                        "VarSampleValue": variable_sample_value,
                        "Area": format_elem.get("Area", "") if format_elem is not None else "",
                        "Start": format_elem.get("Start", "") if format_elem is not None else "",
                        "Length": format_elem.get("Length", "") if format_elem is not None else "",
                        "ImpliedDigits": format_elem.get("ImpliedDigits", "") if format_elem is not None else "",
                        "Format": format_elem.text.strip() if format_elem is not None and format_elem.text else "",
                        "Action": area.get("Action", "")
                    }
                    mapping_data.append(entry)

        # Create DataFrame
        self.xml_tags_df = pd.DataFrame(xml_data, columns=xml_columns)
        self.mapping_df = pd.DataFrame(mapping_data, columns=mapping_columns)

        xml_tags_file_path = os.path.join(self.info_dir_path, "xml_tags.tsv")
        mapping_tags_file_path = os.path.join(self.info_dir_path, "xml_tags_to_variables.tsv")
        self.xml_tags_df.to_csv(xml_tags_file_path, sep="\t", header=True, index=False)
        self.mapping_df.to_csv(mapping_tags_file_path, sep="\t", header=True, index=False)

        self.logger.info(f"\tXML Tags: {os.path.abspath(xml_tags_file_path)}")
        self.logger.info(f"\tVariables-XML Tags Mapping: {os.path.abspath(mapping_tags_file_path)}")

    def _extract_all_rules(self):
        rules_root = self.__get_sections_xml_root("rules")

        columns = ["RuleOI", "Rule Name", "Formula"]
        data = []

        # Extract data from each Rule entry
        for rule in rules_root.findall("Rule"):
            rule_oi = rule.get("RuleOI", "")
            rule_name = rule.get("Name", "")
            formula_elem = rule.find("Formula")
            formula = formula_elem.text.strip() if formula_elem is not None and formula_elem.text else ""
            formula = html.unescape(formula)
            formula = formula.replace('\n', '\\n')

            data.append({
                "RuleOI": rule_oi,
                "Rule Name": rule_name,
                "Formula": formula
            })

        # Create DataFrame
        self.rules_df = pd.DataFrame(data, columns=columns)
        rules_file_path = os.path.join(self.info_dir_path, "rules.tsv")
        self.rules_df.to_csv(rules_file_path, sep="\t", header=True, index=False, quoting=csv.QUOTE_NONE, escapechar='\\')
        self.logger.info(f"\tRules: {os.path.abspath(rules_file_path)}")

    def _get_app_report_stats(self):
        assert len(self.json_report["documents"]) != 0, "JSON report file has not been extracted!"

        docs = self.json_report.get("documents", [])

        # Collect document and page names
        docs_list = [doc["name"] for doc in docs if doc.get("name")]
        pages_list = [page["name"] for doc in docs for page in doc.get("pages", []) if page.get("name")]

        # Collect section and paragraph names
        sections_list = [section["name"] for doc in docs for section in doc.get("sections", []) if section.get("name")]
        paragraphs_list = [paragraph["name"] for doc in docs for section in doc.get("sections", [])
                          for paragraph in section.get("paragraphs", []) if paragraph.get("name")]

        # Count total components and component-refs from pages
        comps_from_pages = sum(len(page.get("components", [])) for doc in docs for page in doc.get("pages", []))
        comp_refs_from_pages = sum(len(comp.get("ref-rules", [])) for doc in docs for page in doc.get("pages", [])
                                  for comp in page.get("components", []))

        # Count total components and component-refs from paragraphs
        comps_from_paragraphs = sum(len(paragraph.get("components", [])) for doc in docs for section in doc.get("sections", [])
                                   for paragraph in section.get("paragraphs", []))
        comp_refs_from_paragraphs = sum(len(comp.get("ref-rules", [])) for doc in docs for section in doc.get("sections", [])
                                       for paragraph in section.get("paragraphs", []) for comp in paragraph.get("components", []))

        # Helper to pull a formula-clean if present
        def __formula(item):
            return item.get("rule", {}).get("formula-clean")

        # Gather all formula-cleans from docs, pages, sections, paragraphs, components, and ref-rules
        rule_formulas = [
            f for f in itertools.chain((__formula(doc) for doc in docs),
                                       (__formula(page) for doc in docs for page in doc.get("pages", [])),
                                       (__formula(section) for doc in docs for section in doc.get("sections", [])),
                                       (__formula(paragraph) for doc in docs for section in doc.get("sections", [])
                                        for paragraph in section.get("paragraphs", [])),
                                       (__formula(comp) for doc in docs for page in doc.get("pages", [])
                                        for comp in page.get("components", [])),
                                       (__formula(comp) for doc in docs for section in doc.get("sections", [])
                                        for paragraph in section.get("paragraphs", []) for comp in paragraph.get("components", [])),
                                       (ref.get("formula-clean") for doc in docs for page in doc.get("pages", [])
                                        for comp in page.get("components", []) for ref in comp.get("ref-rules", [])),
                                       (ref.get("formula-clean") for doc in docs for section in doc.get("sections", [])
                                        for paragraph in section.get("paragraphs", []) for comp in paragraph.get("components", [])
                                        for ref in comp.get("ref-rules", []))
                                       ) if f
        ]

        self.logger.info(f"\t\t#Unique Documents: {len(set(docs_list))}")
        self.logger.info(f"\t\t\t#Unique Pages: {len(set(pages_list))}")
        self.logger.info(f"\t\t\t\t#Components for Pages: {comps_from_pages}")
        self.logger.info(f"\t\t\t\t#Component Refs for Pages: {comp_refs_from_pages}")
        self.logger.info(f"\t\t\t#Unique Sections: {len(set(sections_list))}")
        self.logger.info(f"\t\t\t\t#Unique Paragraphs: {len(set(paragraphs_list))}")
        self.logger.info(f"\t\t\t\t\t#Components for Paragraphs: {comps_from_paragraphs}")
        self.logger.info(f"\t\t\t\t\t#Component Refs for Paragraphs: {comp_refs_from_paragraphs}")
        self.logger.info(f"\t\t#Unique Rules (based on formula): {len(set(rule_formulas))}")

    @staticmethod
    def __chop_at_underscore(s, max_len=512):
        if len(s) <= max_len:
            return s
        idx = s.rfind('_', 0, max_len)
        return s[:idx] if idx != -1 else s[:max_len]

    def _prepare_json_report_and_testcase_details(self):
        pages = self.__extract_page_details()
        sections = self.__extract_section_details()
        documents = self.__extract_doc_details(pages, sections)
        self.json_report["documents"] = documents

        # Save app report JSON
        json_report_file = os.path.basename(os.path.splitext(self.app_report_path_xml)[0]) + '.json'
        json_report_path = os.path.join(self.xml_parse_dir, json_report_file)
        with open(os.path.join(json_report_path), 'w') as file:
            json.dump(self.json_report, file, indent=4)
        self.logger.info(f"\tJSON application report: {os.path.abspath(json_report_path)}..")

        # Save testcase generation details if present
        if len(self.testcases_generation_details) > 0:
            testcase_generation_file = "testcase_generation_details.tsv"
            testcase_generation_path = os.path.join(self.info_dir_path, testcase_generation_file)
            self.testcases_generation_input_df = pd.DataFrame(self.testcases_generation_details,
                                                              columns=["rule_id", "formula_clean", "variables", "var_to_tag"])
            # Drop duplicates based on rule_id
            self.testcases_generation_input_df.drop_duplicates(subset=["rule_id"], inplace=True, ignore_index=True)
            # Group by formula_clean and create a custom id column that includes rule_id joined by "_"
            self.testcases_generation_input_df['custom_id'] = self.testcases_generation_input_df.groupby('formula_clean')['rule_id'].transform(lambda ids: '_'.join(ids))
            self.testcases_generation_input_df = self.testcases_generation_input_df.drop_duplicates(subset='formula_clean')
            self.testcases_generation_input_df['formula_clean'] = self.testcases_generation_input_df['formula_clean'].str.replace('\n', '\\n')

            # The custom_id is for identification in OpenAI Batch jobs, but the length is capped at 512 characters.
            # So, we will chop the custom id if it exceeds the limit, but based on "_"
            self.testcases_generation_input_df.rename(columns={'custom_id': 'custom_id_original'}, inplace=True)
            self.testcases_generation_input_df['custom_id'] = self.testcases_generation_input_df[
                'custom_id_original'].apply(lambda x: self.__chop_at_underscore(x, max_len=512))

            self.testcases_generation_input_df.to_csv(testcase_generation_path, sep="\t", header=True, index=False,
                                                      quoting=csv.QUOTE_NONE, escapechar="\\",
                                                      columns=["custom_id", "custom_id_original", "formula_clean", "variables", "var_to_tag"])
            self.logger.info(f"\tTestcase Generation Details: {os.path.abspath(testcase_generation_path)}..")
        else:
            self.logger.info(f"\tNo rules require testcase generation!")

        # Save error details
        if len(self.rule_errors_details) > 0:
            # Drop duplicates based on rule id and save the errors to a file
            columns = ["rule_id", "rule_type", "unmapped_sections", "no_vars_in_rule", "undefined_rule", "total_unmapped_vars"]
            errors_df = pd.DataFrame(self.rule_errors_details, columns=columns)
            errors_df.drop_duplicates(subset=["rule_id", "rule_type"], inplace=True, ignore_index=True)
            # This number can be different from dataframe length itself because it is deduped on rule_ids and types both
            uniq_rules = errors_df["rule_id"].unique().tolist()

            self.logger.new_line()
            err_rule_001_msg = (f"[{ERR_RULE_001}] Some rules have missing details:"
                                f"\n\tUnique Rules affected due to errors: {len(uniq_rules)}"
                                f"\n\tRule IDs: {', '.join(uniq_rules)}")
            self.logger.error(err_rule_001_msg)
            self.logger.new_line()

            # Get total unmapped variables, unmapped sections, rules with no variables and undefined rules
            unique_unmapped_sections = list(set(item for sublist in errors_df["unmapped_sections"].tolist() for item in sublist))
            rules_with_no_vars = errors_df[errors_df["no_vars_in_rule"] == True]["rule_id"].unique().tolist()
            undefined_rules = errors_df[errors_df["undefined_rule"] == True]["rule_id"].unique().tolist()
            unique_unmapped_vars = list(set(item for sublist in errors_df["total_unmapped_vars"].tolist() for item in sublist))

            if len(rules_with_no_vars) > 0:
                err_rule_001_01_msg = (f"\t[{ERR_RULE_001_01}] Rules without variables count: {len(rules_with_no_vars)}"
                                       f"\n\tRules without variables list: {', '.join(rules_with_no_vars)}")
                self.logger.error(err_rule_001_01_msg)
                self.logger.new_line()

            if len(undefined_rules) > 0:
                err_rule_001_02_msg = (f"\t[{ERR_RULE_001_02}] Undefined rules count: {len(undefined_rules)}"
                                       f"\n\tUndefined rules list: {', '.join(undefined_rules)}")
                self.logger.error(err_rule_001_02_msg)
                self.logger.new_line()

            if len(unique_unmapped_sections) > 0:
                err_rule_001_03_msg = (
                    f"\t[{ERR_RULE_001_03}] Unique unmapped sections count: {len(unique_unmapped_sections)}"
                    f"\n\tUnique unmapped sections list: {', '.join(unique_unmapped_sections)}")
                self.logger.error(err_rule_001_03_msg)
                self.logger.new_line()

            if len(unique_unmapped_vars) > 0:
                err_rule_001_05_msg = (f"\t[{ERR_RULE_001_05}] Unique total unmapped variables count: {len(unique_unmapped_vars)}"
                                       f"\n\tUnique total unmapped variables list: {', '.join(unique_unmapped_vars)}"
                                       f"\n\tNOTE: Not all the unmapped variables may be used in rule conditions!")
                self.logger.error(err_rule_001_05_msg)
                self.logger.new_line()

            errors_df.to_csv(self.error_file_path, sep="\t", header=True, index=False)
            self.logger.info(f"Error details file based on parsing: {os.path.abspath(self.error_file_path)}")

    def __update_details_and_errors_for_testcase(self, rule_obj, rule_type, vars_for_testcases, var_to_tag):

        if rule_obj:
            # Testcases will still be generated if variables list is non-empty
            if vars_for_testcases and len(vars_for_testcases) != 0:
                    self.testcases_generation_details.append({
                        "rule_id": rule_obj["rule-oi"],
                        "formula_clean": rule_obj["formula-clean"],
                        "variables": vars_for_testcases,
                        "var_to_tag": var_to_tag
                    })

            # Record the error details is success is false or total-unmapped-variables has values (success is true in this case)
            if "rule-status" in rule_obj:
                if rule_obj["rule-status"]["error"]["total-unmapped-variables"] or not rule_obj["rule-status"]["success"]:

                    # If rule ID is not present, but section etst cases are present, record the rule_id as "noruleid"
                    if "rule-oi" not in rule_obj:
                        rule_id = "noruleid"
                    else:
                        rule_id = rule_obj["rule-oi"]

                    self.rule_errors_details.append({
                        "rule_id": rule_id,
                        "rule_type": rule_type,
                        "unmapped_sections": rule_obj["rule-status"]["error"]["unmapped-sections"],
                        "no_vars_in_rule": rule_obj["rule-status"]["error"]["no-variables-in-rule"],
                        "undefined_rule": rule_obj["rule-status"]["error"]["undefined-rule"],
                        "total_unmapped_vars": rule_obj["rule-status"]["error"]["total-unmapped-variables"]
                    })

    def __extract_doc_details(self, page_details, section_details):
        # Check that pages and sections are not None
        assert page_details is not None, "Page details must be extracted before documents!"
        # assert section_details is not None, "Section details must be extracted before documents!"

        docs_root = self.__get_sections_xml_root("documents")
        docs = []

        for doc in docs_root.findall("Document"):
            doc_name = doc.get("Name")

            # Extract DependsOnSection and RuleOIs
            targeting = doc.find("Targeting")
            section_name = targeting.get("DependsOnSection") if targeting is not None else None
            rule_tag = targeting.find("Rule") if targeting is not None else None
            rule_oi = rule_tag.get("RuleOI") if rule_tag is not None else None

            # Get details of the rule
            doc_rule, doc_vars_for_testcases, doc_var_to_tag = self.__get_rule_and_vars(rule_oi)

            # Get tag for section
            section = dict()
            if section_name:
                assert not self.xml_tags_df.empty, "XML tags are not extracted!"
                tags_for_section = self.xml_tags_df.loc[self.xml_tags_df["Section"] == section_name, "Tag"].drop_duplicates().tolist()
                tag_value = section_name if len(section_name) <= 2 else section_name.strip()[:2]
                section = {
                    "name": tag_value,
                    "xml-tags": tags_for_section
                }

                # If document does not have a rule, but only section details, add the rule-status
                if not doc_rule or len(doc_rule) == 0:
                    doc_rule["rule-status"] = {
                        "success": True,
                        "error": {
                            "total-unmapped-variables": [],
                            "unmapped-sections": [],
                            "no-variables-in-rule": False,
                            "undefined-rule": False
                        }
                    }

                # If a mapping does not exist for the section, record it in the error
                if len(tags_for_section) == 0:
                    doc_rule["rule-status"]["success"] = False
                    if section_name not in doc_rule["rule-status"]["error"]["unmapped-sections"]:
                        doc_rule["rule-status"]["error"]["unmapped-sections"].append(section_name)
                # Otherwise get prepare test case for section
                else:
                    section_tags_pos = {tag: tag_value for tag in section["xml-tags"]}
                    # Get section name value for negative case - generate random uppercase string of 2 characters
                    section_name_neg = tag_value
                    while section_name_neg == tag_value:
                        section_name_neg = "".join(random.choices(string.ascii_uppercase, k=2))
                    section_tags_neg = {tag: section_name_neg for tag in section["xml-tags"]}

                    # Add the pos-neg section tags to the rule object
                    section_data = {"positives": section_tags_pos, "negatives": section_tags_neg}
                    doc_rule["section-test-cases"] = section_data

            # Update testcase generation details and error details
            self.__update_details_and_errors_for_testcase(doc_rule, "document", doc_vars_for_testcases, doc_var_to_tag)

            # Extract PageOIs
            pages = []
            for page in doc.findall(".//Pages/Page"):
                page_oi = page.get("PageOI")
                if page_oi in page_details:
                    p = page_details[page_oi]
                else:
                    continue  # Skip if page details not found

                # update page and clean
                self.__update_details_and_errors_for_testcase(
                    p["rule"], "page", p["rule"].get("_vars_for_testcases"), p["rule"].get("_var_to_tag"))
                p["rule"].pop("_vars_for_testcases", None)
                p["rule"].pop("_var_to_tag", None)

                # update & clean each component + ref
                for comp in p["components"]:
                    self.__update_details_and_errors_for_testcase(
                        comp["rule"], "component", comp["rule"].get("_vars_for_testcases"), comp["rule"].get("_var_to_tag"))
                    comp["rule"].pop("_vars_for_testcases", None)
                    comp["rule"].pop("_var_to_tag", None)

                    for ref in comp["ref-rules"]:
                        self.__update_details_and_errors_for_testcase(
                            ref, "component_ref", ref.get("_vars_for_testcases"), ref.get("_var_to_tag"))
                        ref.pop("_vars_for_testcases", None)
                        ref.pop("_var_to_tag", None)

                pages.append(p)

            # Extract SectionOIs
            sections = []
            for sec in doc.findall(".//Sections/Section"):
                section_oi = sec.get("SectionOI")
                if section_oi in section_details:
                    s = section_details[section_oi]
                else:
                    continue  # Skip if section details not found

                # update section and clean
                self.__update_details_and_errors_for_testcase(
                    s["rule"], "section", s["rule"].get("_vars_for_testcases"), s["rule"].get("_var_to_tag"))
                s["rule"].pop("_vars_for_testcases", None)
                s["rule"].pop("_var_to_tag", None)

                # update & clean each paragraph + component + ref
                for paragraph in s["paragraphs"]:
                    self.__update_details_and_errors_for_testcase(
                        paragraph["rule"], "paragraph", paragraph["rule"].get("_vars_for_testcases"), paragraph["rule"].get("_var_to_tag"))
                    paragraph["rule"].pop("_vars_for_testcases", None)
                    paragraph["rule"].pop("_var_to_tag", None)

                    for comp in paragraph["components"]:
                        self.__update_details_and_errors_for_testcase(
                            comp["rule"], "component", comp["rule"].get("_vars_for_testcases"), comp["rule"].get("_var_to_tag"))
                        comp["rule"].pop("_vars_for_testcases", None)
                        comp["rule"].pop("_var_to_tag", None)

                        for ref in comp["ref-rules"]:
                            self.__update_details_and_errors_for_testcase(
                                ref, "component_ref", ref.get("_vars_for_testcases"), ref.get("_var_to_tag"))
                            ref.pop("_vars_for_testcases", None)
                            ref.pop("_var_to_tag", None)

                sections.append(s)

            docs.append({
                "name": doc_name,
                "section": section,
                "rule": doc_rule,
                "pages": pages,
                "sections": sections
            })

        return docs

    def __extract_page_details(self):
        pages_root = self.__get_sections_xml_root("pages")
        pages = dict()

        for page in pages_root.findall("Page"):
            page_oi = page.get("PageOI")
            page_name = page.get("Name")

            # Extract RuleOI
            rule_tag = page.find("Rule")
            rule_oi = rule_tag.get("RuleOI") if rule_tag is not None else ""

            # Get details of the rule
            page_rule, page_vars_for_testcases, page_var_to_tag = self.__get_rule_and_vars(rule_oi)
            page_rule["_vars_for_testcases"] = page_vars_for_testcases
            page_rule["_var_to_tag"] = page_var_to_tag

            # Extract component details
            component_list = page.findall("ComponentList/Component")
            components = self.__extract_component_details(component_list)

            pages[page_oi] = {
                "name": page_name,
                "rule": page_rule,
                "components": components
            }

        return pages

    def __extract_section_details(self):
        paragraph_details = self.__extract_paragraph_details()

        sections_root = self.__get_sections_xml_root("sections")
        sections = dict()
        if sections_root is None:
            return None

        for section in sections_root.findall("Section"):
            section_oi = section.get("SectionOI")
            section_name = section.get("Name")

            # Extract RuleOI from Targeting
            targeting = section.find("Targeting")
            rule_tag = targeting.find("Rule") if targeting is not None else None
            rule_oi = rule_tag.get("RuleOI") if rule_tag is not None else None

            # Get details of the rule
            section_rule, section_vars_for_testcases, section_var_to_tag = self.__get_rule_and_vars(rule_oi)
            section_rule["_vars_for_testcases"] = section_vars_for_testcases
            section_rule["_var_to_tag"] = section_var_to_tag

            # Extract paragraph details
            paragraph_list = section.findall("PolicyContent/ContentItem/Paragraph")
            paragraphs = []
            for para in paragraph_list:
                para_oi = para.get("ParagraphOI")
                paragraphs.append(paragraph_details[para_oi])

            sections[section_oi] = {
                "name": section_name,
                "rule": section_rule,
                "paragraphs": paragraphs
            }

        return sections

    def __extract_paragraph_details(self):
        paragraphs_root = self.__get_sections_xml_root("paragraphs")
        paragraphs = dict()
        if paragraphs_root is None:
            return None

        for paragraph in paragraphs_root.findall("Paragraph"):
            paragraph_oi = paragraph.get("ParagraphOI")
            paragraph_name = paragraph.get("Name")

            # Extract RuleOI
            rule_tag = paragraph.find("Rule")
            rule_oi = rule_tag.get("RuleOI") if rule_tag is not None else ""

            # Get details of the rule
            paragraph_rule, paragraph_vars_for_testcases, paragraph_var_to_tag = self.__get_rule_and_vars(rule_oi)
            paragraph_rule["_vars_for_testcases"] = paragraph_vars_for_testcases
            paragraph_rule["_var_to_tag"] = paragraph_var_to_tag

            # Extract component details
            component_list = paragraph.findall("ComponentList/Component")
            components = self.__extract_component_details(component_list)

            paragraphs[paragraph_oi] = {
                "name": paragraph_name,
                "rule": paragraph_rule,
                "components": components
            }

        # Report paragraphs that use variables separately
        filtered_paragraphs = [para for para in paragraphs_root.findall('Paragraph') if self.__has_variableuse(para)]
        self.logger.info(f"\t\t#Paragraphs with VariableUseList: {len(filtered_paragraphs)} (out of {len(paragraphs_root.findall('Paragraph'))})")
        # Create new root and append filtered paragraphs
        new_root = etree.Element('ParagraphList')
        for para in filtered_paragraphs:
            new_root.append(para)
        new_tree = etree.ElementTree(new_root)
        save_xml(new_tree, self.paras_with_variableuse_path)
        self.logger.info(f"\t\tParagraphs with VariableUseList: {os.path.abspath(self.paras_with_variableuse_path)}")

        return paragraphs

    def __extract_component_details(self, component_list):
        components = []
        for component in component_list:
            # Extract RuleOI for Component Rule, if present
            r = component.find("Rule") # Assumption that a component has only 1 base rule
            component_rule_oi = r.get("RuleOI") if r is not None else ""
            component_rule, component_vars_for_testcases, component_var_to_tag = self.__get_rule_and_vars(component_rule_oi)
            component_rule["_vars_for_testcases"] = component_vars_for_testcases
            component_rule["_var_to_tag"] = component_var_to_tag

            # Extract RuleOIs for Component RefRule, if present
            component_ref_rules = []
            for rr in component.findall("RefRule"):
                ref_rule_oi = rr.get("RuleOI")
                ref_rule, ref_vars_for_testcases, ref_var_to_tag = self.__get_rule_and_vars(ref_rule_oi)
                ref_rule["_vars_for_testcases"] = ref_vars_for_testcases
                ref_rule["_var_to_tag"] = ref_var_to_tag
                component_ref_rules.append(ref_rule)

            # Add rule details to the component if either is present
            if component_rule or component_ref_rules:
                components.append({
                    "rule": component_rule,
                    "ref-rules": component_ref_rules
                })

        return components

    def __get_rule_and_vars(self, rule_oi):
        if rule_oi is None or len(rule_oi) == 0:
            return dict(), None, None

        # Check if the relevant information is extracted first
        assert not self.rules_df.empty, "Rules are not extracted!"
        assert not self.variables_df.empty, "Variables are not extracted!"
        assert not self.mapping_df.empty, "Variables-XML tags mappings are not extracted!"

        # Maintain a testcase status to record the errors
        status = {
            "success": True,
            "error": {
                "total-unmapped-variables": [],    # May or may not be used in conditions of rules
                "unmapped-sections": [],
                "no-variables-in-rule": False,
                "undefined-rule": False
            }
        }

        # Get the rule information
        rule_details_df = self.rules_df.loc[self.rules_df["RuleOI"] == rule_oi]

        # If rule details are not found for a rule ID, the app report is missing the definition
        if rule_details_df.empty:
            status["success"] = False
            status["error"]["undefined-rule"] = True

            return {
                "rule-oi": rule_oi,
                "rule-status": status
            }, None, None

        rule_details = rule_details_df.iloc[0]
        rule_name = rule_details["Rule Name"]
        rule_formula = rule_details["Formula"]
        rule_formula_clean = remove_comments_from_rule(rule_formula)

        # Get variables associated with the rule;
        # and a separate variables list and var-to-tag mapping for testcases generation
        variables, var_names = [], []   # var names are used to check if the variable is already present in variables
        variables_for_testcases, var_to_tag = [], dict()
        vars_df = self.variables_df[self.variables_df["Name"].apply(lambda var: var in rule_formula_clean)]
        for _, row in vars_df.iterrows():
            variable_name = row["Name"]
            if variable_name in var_names:
                continue

            variable_info = {
                "name": variable_name,
                "type": row["Type"],
                "is-array": True if row["IsArray"] == "Yes" else False
            }
            var_names.append(variable_name)

            # Find the corresponding XML tag for the variable
            # TODO: Considers first occurrence of the variable mapping; check if there are more than 1
            xml_tag = self.mapping_df.loc[self.mapping_df["Name"] == variable_name, "Tag"]
            xml_tag = xml_tag.iloc[0] if not xml_tag.empty else None

            testcase_variables = variable_info.copy()
            if xml_tag is not None:
                testcase_variables["xml-schema"] = {"tag-name": xml_tag}
            variables_for_testcases.append(testcase_variables)

            variable_info["description"] = row["Description"] if row["Description"] and row["Description"] !=  variable_name else None
            variable_info["xml-schema"] = {"tag-name": xml_tag}
            variables.append(variable_info)

            # If xml_tag is not None, add it to the var_to_tag mapping
            if xml_tag is not None:
                var_to_tag.update({variable_name: xml_tag})
            else:
                # status is not changed to False for this, because the variables may not be used in the rule conditions
                # unmapped variables used in the rules will be discovered when generating test cases and status will be changed then
                # status["success"] = False
                if variable_name not in status["error"]["total-unmapped-variables"]:
                    status["error"]["total-unmapped-variables"].append(variable_name)

        # If no variables were found in the rule, record an error
        if len(variables_for_testcases) == 0:
            status["success"] = False
            status["error"]["no-variables-in-rule"] = True

        rule_info = {
            "rule-oi": rule_oi,
            "name": rule_name,
            "formula": rule_formula,
            "formula-clean": rule_formula_clean,
            "variables": variables,
            "rule-status": status
        }
        return rule_info, variables_for_testcases, var_to_tag

    def __has_variableuse(self, element):
        """Recursively check for any VariableUseList descendant."""
        for child in element:
            if child.tag == 'VariableUseList':
                return True
            if self.__has_variableuse(child):
                return True
        return False
