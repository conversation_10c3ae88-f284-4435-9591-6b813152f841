# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-19      #
# --------------------------------- #

"""
Class for complete payload generator
"""
import ast
import base64
import csv
import json
import os
import random
import re
from datetime import date, datetime

import pandas as pd
import rstr
from lxml import etree

from utils.error_codes import ERR_SYS_001, ERR_XSD_001, ERR_XSD_002, ERR_XSD_003
from utils.openai_utils import OpenAIUtils
from utils.prompts.sample_element_value_generation import ElementValueGenerationConfig
from utils.xml_utils import parse_xml, save_xml, load_schemas_with_definitions, build_type_context_map

# Built-in type groups
INT_TYPES = {"integer", "int", "long", "short", "byte", "nonPositiveInteger",
             "negativeInteger", "nonNegativeInteger", "unsignedLong", "unsignedInt",
             "unsignedShort", "unsignedByte", "positiveInteger"}
FLOAT_TYPES = {"decimal", "float", "double"}
STRING_TYPES = {"string", "normalizedString", "token", "language", "Name", "NCName",
                "ID", "IDREF", "IDREFS", "ENTITY", "ENTITIES"}

# All built-in XSD types that should be handled by _generate_builtin_type
BUILTIN_XSD_TYPES = INT_TYPES | FLOAT_TYPES | STRING_TYPES | {
    "boolean", "date", "dateTime", "time", "gYear", "gMonth", "gDay",
    "gYearMonth", "gMonthDay", "duration", "yearMonthDuration",
    "dayTimeDuration", "base64Binary"
}

# Unified map: values are either literals or zero-arity functions
_TYPE_GENERATORS = {
    # Booleans & zeros
    "boolean": "true",
    **{t: "0" for t in INT_TYPES},
    **{t: "0.0" for t in FLOAT_TYPES},

    # Date/time / g-X types
    "date":                   lambda: date.today().isoformat(),
    "dateTime":               lambda: datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
    "time":                   lambda: datetime.now().strftime("%H:%M:%S"),
    "gYear":                  lambda: str(date.today().year),
    "gMonth":                 lambda: f"--{date.today().month:02d}",
    "gDay":                   lambda: f"---{date.today().day:02d}",
    "gYearMonth":             lambda: date.today().strftime("%Y-%m"),
    "gMonthDay":              lambda: date.today().strftime("--%m-%d"),

    # Durations
    "duration":               "P1DT0H0M0S",
    "yearMonthDuration":      "P1Y",
    "dayTimeDuration":        "P1DT0H0M0S",

    # Binary data
    "base64Binary":           lambda: base64.b64encode(b"sample_binary_data").decode('ascii'),
}


class CompletePayloadGenerator:
    def __init__(self, main_schema_path, app_schema_path, sample_xml_path, var_tag_mapping_path, testcase_generation_details_tsv,
                 output_path, logger):
        self.main_schema_path = main_schema_path
        self.app_schema_path = app_schema_path
        self.sample_xml_path = sample_xml_path
        self.var_tag_mapping_path = var_tag_mapping_path
        self.testcase_generation_details_tsv = testcase_generation_details_tsv
        self.output_path = output_path

        self.logger = logger

        # The complete schema must be saved at the same location as other schemas
        self.complete_schema_path = os.path.join(os.path.dirname(self.app_schema_path), 'complete_schema.xsd')

        # Parse the sample XML file and schema files
        self.main_schema_tree, self.main_schema_root = parse_xml(self.main_schema_path)
        self.app_schema_tree, self.app_schema_root = parse_xml(self.app_schema_path)
        self.sample_xml_tree, self.sample_xml_root = parse_xml(self.sample_xml_path)

        # Get XML tags from testcase generation details
        self.xml_tags = self.get_xml_tags_from_testcase_generation_details()

        self.element_types_df = pd.DataFrame()
        self.values_map_app_report = {}
        self.records = []
        self.xs = ""

        self.schema_element_paths = None
        self.sample_elements = None
        self.skipped_elements = []
        self.skipped_elems_df = pd.DataFrame()

        # LLM & config
        self.config = ElementValueGenerationConfig()
        self.model = OpenAIUtils()

    def run(self):
        """Get complete schema, elements and it types and generate complete payload with values derived or generated"""
        self.logger.info("========================= COMPLETE PAYLOAD GENERATION =========================")
        self.logger.info(f"Sample payload XML used: {os.path.basename(self.sample_xml_path)}")
        self.logger.new_line()

        self.logger.info("Augmenting main schema with app schema...")
        self.augment_schema()
        self.logger.new_line()

        self.logger.info("Getting all the elements and their type details from complete schema...")
        self.extract_element_details()
        self.logger.new_line()

        self.logger.info("Preparing complete payload XML with valid values...")
        self.create_complete_payload()
        self.logger.new_line()

        self.logger.info("APP INPUT VALIDATION: Checking if XML elements in input XML provided are present in complete schema...")
        self.check_sample_elements_in_schema()
        self.logger.new_line()

        self.logger.info("APP INPUT VALIDATION: Checking if XML elements used in rules are present in complete schema...")
        self.validate_testcase_xml_tags_against_schema()

    def get_xml_tags_from_testcase_generation_details(self):
        """Get all the XML tags from the testcase generation details"""
        testcase_generation_df = pd.read_csv(self.testcase_generation_details_tsv, sep="\t", header=0, index_col=None,
                                             quoting=csv.QUOTE_NONE, escapechar="\\",
                                             usecols=["var_to_tag"], converters={"var_to_tag": ast.literal_eval})
        vars_tags = testcase_generation_df["var_to_tag"].tolist()
        xml_tags = [tag for vt in vars_tags for tag in vt.values()]
        xml_tags = list(set(xml_tags))
        return xml_tags

    def __detect_app_namespace(self, app_elem):
        """Identifies namespace of app request from sample XML; fallback is to get target namespace from app schema"""
        # 1. Directly from the element’s own tag
        ns = etree.QName(app_elem).namespace
        if ns:
            return ns

        # 2. Scan children for the first namespace you see
        for child in app_elem.iter():
            ns = etree.QName(child).namespace
            if ns:
                return ns

        # 3. Fallback: read the XSD’s targetNamespace
        # TODO: Handle when targetNamespace is not present in app schema
        return self.app_schema_root.get('targetNamespace')

    @staticmethod
    def __find_named_simpletype(type_name, schemas, ns):
        """
        Search across all loaded schemas for <xsd:simpleType name="..."> matching type_name.
        """
        local = type_name.split(":", 1)[-1]
        for root in schemas.values():
            st = root.find(f".//xsd:simpleType[@name='{local}']", namespaces=ns)
            if st is not None:
                return st
        return None

    def __get_leaf_simpletype(self, schemas, simple, ns):
        """
        If this simpleType restricts another named simpleType, recurse until base is built-in.
        """
        restr = simple.find("xsd:restriction", namespaces=ns)
        if restr is None:
            return simple
        base = restr.get("base")
        if not base:
            return simple
        # try to resolve base as a named simpleType in any schema
        other = self.__find_named_simpletype(base, schemas, ns)
        if other is not None:
            return self.__get_leaf_simpletype(schemas, other, ns)
        # otherwise base is a built-in XSD type → this is the leaf
        return simple

    @staticmethod
    def __find_named_element(elem_name, schemas, ns):
        """
        Search all loaded schemas for <xsd:element name="elem_name">.
        """
        for root in schemas.values():
            found = root.find(f".//xsd:element[@name='{elem_name}']", namespaces=ns)
            if found is not None:
                return found
        return None

    def __extract_all_elements_info(self, schemas, ns):
        rows = []
        # Prepare namespace-qualified tags
        xsd = ns.get('xsd', '')
        tag_fmt = lambda tag: f"{{{xsd}}}{tag}"
        elem_path = f".//{tag_fmt('element')}"
        simple_type_tag = tag_fmt('simpleType')
        restriction_tag = tag_fmt('restriction')

        def __resolve_leaf(elem):
            # Inline simpleType takes precedence
            inline = elem.find(simple_type_tag)
            if inline is not None:
                return self.__get_leaf_simpletype(schemas, inline, ns)
            # Otherwise lookup named simpleType
            el_type = elem.get('type') or ""
            if not el_type:
                return None
            named = self.__find_named_simpletype(el_type, schemas, ns)
            return self.__get_leaf_simpletype(schemas, named, ns) if named is not None else None

        for root in schemas.values():
            for elem in root.findall(elem_path):
                # handle ref vs name
                ref = elem.get("ref")
                if ref:
                    name = ref.split(":", 1)[-1]
                    declared = self.__find_named_element(name, schemas, ns)
                else:
                    name = elem.get("name")
                    declared = None

                if not name:
                    continue

                # helper to pick attributes from ref-element or declaration
                def pick_attr(attr, default=None):
                    val = elem.get(attr)
                    if val is not None:
                        return val
                    if declared is not None and declared.get(attr) is not None:
                        return declared.get(attr)
                    return default

                info = {
                    "default": pick_attr("default", None),
                    "fixed": pick_attr("fixed", None),
                    "minOccurs": pick_attr("minOccurs", "1"),
                    "maxOccurs": pick_attr("maxOccurs", "1"),
                    "nillable": pick_attr("nillable", "false")
                }

                elem_type = pick_attr("type", "")
                leaf = __resolve_leaf(elem)

                # serialize any <xsd:restriction> under the leaf
                type_base = ""
                restriction_xml = ""
                if leaf is not None:
                    r = leaf.find(restriction_tag)
                    if r is not None:
                        type_base = r.get('base', '')
                        raw = etree.tostring(r, encoding='unicode', method='xml')
                        start = raw.find(">") + 1
                        end = raw.rfind("</")
                        restriction_xml = raw[start:end]
                        # Remove any comments if present
                        restriction_xml = re.sub(r'<!--.*?-->', '', restriction_xml, flags=re.DOTALL).strip()
                        restriction_xml = re.sub(r"\s+", " ", restriction_xml).strip()

                rows.append({
                    'element_name': name,
                    'type': elem_type,
                    'type_base': type_base,
                    'type_restriction': restriction_xml,
                    'info': info,
                })

        return rows

    @staticmethod
    def _generate_from_pattern(pattern_str):
        pattern_str = rf"^{pattern_str}$"
        result = rstr.xeger(pattern_str)
        return result

    def _generate_value_using_llm(self, xml_el):
        el_name = xml_el.tag
        el_path = xml_el.getroottree().getpath(xml_el)

        # Get the type and restriction from element_types_df
        temp_row = self.element_types_df[self.element_types_df["element_name"] == el_name]
        el_type, el_restriction = "", ""
        if len(temp_row) != 0:
            temp_row = temp_row.iloc[0]
            if not pd.isna(temp_row["type_restriction"]) or temp_row["type_restriction"] is not None:
                el_restriction = temp_row["type_restriction"]
            if not pd.isna(temp_row["type_base"]) or temp_row["type_base"] is not None:
                el_type = temp_row["type_base"]
            elif not pd.isna(temp_row["type"]) or temp_row["type"] is not None:
                if temp_row["type"].startswith("xsd:"):
                    el_type = temp_row["type"]

        # Get the variable type for the element
        mapping_df = pd.read_csv(self.var_tag_mapping_path, sep="\t", usecols=[0, 3], header=0, index_col=None)
        temp_row = mapping_df[mapping_df["Tag"] == el_name]
        var_type = ""
        if len(temp_row) != 0:
            var_type = temp_row.iloc[0]["VarType"]

        # Create user input for the LLM and call the model
        user_input, json_schema_details = self.config.create_user_input(el_name, el_path, el_type, el_restriction, var_type)
        output, response = self.model.invoke_llm(self.config.model_config, self.config.system_instructions, user_input,
                                            json_schema_details)

        # Get the value from the output
        value = output[el_name] if isinstance(output, dict) and el_name in output else None

        return value

    @staticmethod
    def _validate_builtin_value(type_local, value):
        """
        Attempt to validate a primitive value against built-in XSD type.
        Returns True if the value can be parsed/converted appropriately.
        """
        try:
            if type_local == "boolean":
                return str(value).lower() in ("true", "false")
            if type_local in INT_TYPES:
                int(value)
                return True
            if type_local in FLOAT_TYPES:
                float(value)
                return True
            # Date/Time types
            if type_local == "date":
                date.fromisoformat(value)
                return True
            if type_local == "dateTime":
                datetime.fromisoformat(value)
                return True
            if type_local == "time":
                datetime.strptime(value, "%H:%M:%S")
                return True
            if type_local in ("gYear", "gMonth", "gDay", "gYearMonth", "gMonthDay"):
                patterns = {
                    "gYear": r"^-?\d{4}(Z|([+\-]\d{2}:\d{2}))?$",
                    "gMonth": r"^--\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                    "gDay": r"^---\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                    "gYearMonth": r"^-?\d{4}-\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                    "gMonthDay": r"^--\d{2}-\d{2}(Z|([+\-]\d{2}:\d{2}))?$",
                }
                pattern = patterns[type_local]
                return bool(re.fullmatch(pattern, value))
            # Duration types: basic check for leading 'P'
            if type_local.endswith("Duration") or type_local == "duration":
                return str(value).startswith("P")
            # Base64 binary validation
            if type_local == "base64Binary":
                try:
                    base64.b64decode(value, validate=True)
                    return True
                except Exception:
                    return False
            # String-like types: accept any string
            if type_local in STRING_TYPES or type_local.lower().endswith("string"):
                str(value)
                return True
        except Exception:
            return False
        # Fallback: accept the value
        return True

    def _validate_simple_type(self, xsd_simple, value):
        """
        Validate a string value against an <xs:simpleType> restriction.
        Returns True if it satisfies enumeration, pattern, length, or numeric facets.
        """
        restriction = xsd_simple.find(self.xs + "restriction")
        if restriction is None:
            return True

        # Enumerations
        enums = [e.get("value") for e in restriction.findall(self.xs + "enumeration")]
        if enums:
            return value in enums

        # Pattern facet
        pattern = restriction.find(self.xs + "pattern")
        if pattern is not None and pattern.get("value"):
            return re.fullmatch(pattern.get("value"), value) is not None

        # Numeric facets
        base = restriction.get("base")
        base_local = base.split(":")[-1] if base else None
        td = restriction.find(self.xs + "totalDigits")
        fd = restriction.find(self.xs + "fractionDigits")
        if (base_local in INT_TYPES | FLOAT_TYPES) and (td is not None or fd is not None):
            # Basic check: use built-in validation
            return self._validate_builtin_value(base_local, value)

        # Length facets
        length = restriction.find(self.xs + "length")
        if length is not None:
            try:
                return len(value) == int(length.get("value"))
            except:
                pass
        min_len = restriction.find(self.xs + "minLength")
        max_len = restriction.find(self.xs + "maxLength")
        if min_len is not None or max_len is not None:
            try:
                val_len = len(value)
                if min_len is not None and val_len < int(min_len.get("value")):
                    return False
                if max_len is not None and val_len > int(max_len.get("value")):
                    return False
                return True
            except:
                pass

        # Fallback to base type validation
        if base_local:
            return self._validate_builtin_value(base_local, value)

        return False

    def _record_value(self, xml_el, strategy):
        """
        Record the element's name, its full XPath, text value, and strategy.
        """
        # compute the full XPath from the tree root
        path = xml_el.getroottree().getpath(xml_el)
        self.records.append({
            "element_name": xml_el.tag,
            "path": path,
            "value": xml_el.text or "",
            "strategy": strategy
        })

    def _generate_builtin_type(self, type_local, parent_xml, el_name):
        """
        Generate or override a sample value for an xsD built-in primitive type dynamically,
        without relying on a hard-coded mapping. If no restrictions are present,
        choose a reasonable default based on the type name.
        """
        sample_value = self.__get_sample_xml_value(el_name)

        # Use sample override from app report if valid (first from sample payload, then app report
        if sample_value and self._validate_builtin_value(type_local, sample_value):
            value, strategy = str(sample_value), "sample_payload"

        else:
            generator = _TYPE_GENERATORS.get(type_local)
            if generator is not None:
                value = generator() if callable(generator) else generator
                if type_local == "boolean":
                    strategy = "randomly_generated_boolean"
                elif type_local in INT_TYPES | FLOAT_TYPES:
                    strategy = "randomly_generated_number"
                elif type_local == "base64Binary":
                    strategy = "randomly_generated_base64"
                else:
                    strategy = "randomly_generated_datetime"

            # string
            elif type_local in STRING_TYPES or type_local.lower().endswith("string"):
                value = self._generate_value_using_llm(parent_xml)
                if value is not None:
                    strategy = "randomly_generated_llm"
                else:
                    value = f"sample_{el_name}"
                    strategy = "randomly_generated"
            # fallback
            else:
                value = self._generate_value_using_llm(parent_xml)
                if value is not None:
                    strategy = "randomly_generated_llm"
                else:
                    value = f"sample_{el_name}"
                    strategy = "randomly_generated"

        # assign & record the strategy for the element
        parent_xml.text = value
        self._record_value(parent_xml, strategy)

    def _generate_simple_type(self, xsd_simple, parent_xml, el_name):
        """
        Given an <xs:simpleType> node, generate a sample text based on its <xs:restriction> or override:
          - If there are <xs:enumeration> facets, pick the first value.
          - If there's a pattern, generate a matching string via generate_from_pattern().
          - If there are totalDigits and/or fractionDigits, choose a value respecting them.
          - If there are length facets (minLength, maxLength, length), pick a string between those bounds.
          - Otherwise, if its base is a built-in primitive, defer to generate_builtin_type.
        Inserts text into parent_xml.
        """
        sample_value = self.__get_sample_xml_value(el_name)

        # Sample override from sample payload
        if sample_value and self._validate_simple_type(xsd_simple, sample_value):
            parent_xml.text = str(sample_value)
            self._record_value(parent_xml, "sample_payload")
            return

        restriction = xsd_simple.find(self.xs + "restriction")
        if restriction is None:
            value = self._generate_value_using_llm(parent_xml)
            if value is not None:
                strategy = "randomly_generated_llm"
            else:
                value = f"sample_{el_name}"
                strategy = "randomly_generated"

            parent_xml.text = value
            self._record_value(parent_xml, strategy)
            return

        # 1) Enumerations
        enums = restriction.findall(self.xs + "enumeration")
        if enums:
            value = enums[0].get("value", f"sample_{el_name}")
            parent_xml.text = value
            if value == f"sample_{el_name}":
                self._record_value(parent_xml, "randomly_generated")
            else:
                self._record_value(parent_xml, "randomly_generated_enum")
            return

        # 2) Pattern facet
        pattern = restriction.find(self.xs + "pattern")
        if pattern is not None:
            pat_val = pattern.get("value")
            if pat_val:
                parent_xml.text = self._generate_from_pattern(pat_val)
                self._record_value(parent_xml, "randomly_generated_pattern")
                return

        # Determine base
        base = restriction.get("base")
        base_local = base.split(":")[-1] if base else None

        # 3) Numeric-based restrictions: totalDigits / fractionDigits
        if base_local in INT_TYPES | FLOAT_TYPES:
            td = restriction.find(self.xs + "totalDigits")
            fd = restriction.find(self.xs + "fractionDigits")
            if td is not None:
                td_val = int(td.get("value"))
                fd_val = int(fd.get("value")) if fd is not None else 0
                if base_local in INT_TYPES:
                    parent_xml.text = "1" * td_val
                    self._record_value(parent_xml, "randomly_generated_number")
                    return
                int_digits = td_val - fd_val
                integer_part = "0" if int_digits <= 0 else "1" + "0" * (int_digits - 1)
                fraction_part = "0" * fd_val
                parent_xml.text = f"{integer_part}.{fraction_part}" if fd_val > 0 else integer_part
                self._record_value(parent_xml, "randomly_generated_number")
                return
            if fd is not None:
                fd_val = int(fd.get("value"))
                if base_local in FLOAT_TYPES:
                    fraction_part = "1" + "0" * (fd_val - 1) if fd_val > 0 else "0"
                    parent_xml.text = f"0.{fraction_part}"
                    self._record_value(parent_xml, "randomly_generated_number")
                    return

        # 4) Length constraints for indicator variables
        min_len = restriction.find(self.xs + "minLength")
        max_len = restriction.find(self.xs + "maxLength")
        try:
            min_val = int(min_len.get("value")) if min_len is not None else 1
            max_val = int(max_len.get("value")) if max_len is not None else min_val

            # Special case for variables ending in "Ind" (indicators) and of type string with max length = 1
            # The values for the indicators are mostly either Y or N
            if max_val == 1 and el_name.endswith("Ind"):
                value = random.choice(("Y", "N"))
                strategy = "randomly_generated_Ind"
            else:
                value = self._generate_value_using_llm(parent_xml)
                if value is not None:
                    strategy = "randomly_generated_llm"
                else:
                    value = f"sample_{el_name}"
                    strategy = "randomly_generated"

            parent_xml.text = value
            self._record_value(parent_xml, strategy)
            return

        except (ValueError, TypeError):
            pass

        # 5) Built-in fallback
        if base_local:
            self._generate_builtin_type(base_local, parent_xml, el_name)
            return

        # 6) Fallback generic
        value = self._generate_value_using_llm(parent_xml)
        if value is not None:
            strategy = "randomly_generated_llm"
        else:
            value = f"sample_{el_name}"
            strategy = "randomly_generated"
        parent_xml.text = value
        self._record_value(parent_xml, strategy)

    def _generate_element(self, xsd_element, elements_dict, complex_types_dict, simple_types_dict, parent_xml):
        """
        Given an <xs:element> node (either global or local) and a parent XML node,
        create the corresponding XML element (with dummy or constrained text, or nested children).
        Recurses into complexTypes, simpleTypes, built-in types, extensions, references, etc.
        """
        ref = xsd_element.get("ref")
        if ref is not None:
            local_name = ref.split(":")[-1]
            if local_name in elements_dict:
                xsd_element = elements_dict[local_name]
                el_name = local_name
            else:
                el_name = local_name
        else:
            el_name = xsd_element.get("name")

        # These elements were asked to be ignored
        if el_name == "InputDocuments" or el_name == "HarteHanksControlInfo":
            if parent_xml.tag == "DocumentRequest":
                return None

        xml_el = etree.SubElement(parent_xml, el_name)

        # Hard code certain elements as per the information from TIAA
        if el_name == "DeliveryType":
            xml_el.text = "S"
            self._record_value(xml_el, "hard-coded")
            return xml_el
        if el_name == "EpExtPdfName":
            xml_el.text = "generic_pdf_passthough"
            self._record_value(xml_el, "hard-coded")
            return xml_el

        type_attr = xsd_element.get("type")
        # Sample override at element level from sample payload
        sample_value = self.__get_sample_xml_value(el_name)
        if sample_value and type_attr:
            type_local = type_attr.split(":")[-1]
            if type_local in simple_types_dict and self._validate_simple_type(simple_types_dict[type_local], sample_value):
                xml_el.text = str(sample_value)
                self._record_value(xml_el, "sample_payload")
                return xml_el
            if type_local not in complex_types_dict and type_local not in simple_types_dict:
                if self._validate_builtin_value(type_local, sample_value):
                    xml_el.text = str(sample_value)
                    self._record_value(xml_el, "sample_payload")
                    return xml_el

        if type_attr is not None:
            type_local = type_attr.split(":")[-1]

            # Special handling for base64Binary to ensure it bypasses complex type resolution
            if type_local == "base64Binary":
                self._generate_builtin_type(type_local, xml_el, el_name)
            elif type_local in elements_dict:
                # grab that global element
                ref_el = elements_dict[type_local]
                # first, if it has a type="SomethingType", drill into that
                alias_type = ref_el.get("type")
                if alias_type:
                    alias_local = alias_type.split(":")[-1]
                    resolved_complex_type = self._resolve_type_with_context(alias_local, el_name, parent_xml, complex_types_dict)
                    if resolved_complex_type is not None:
                        self._generate_complex_type(
                            resolved_complex_type,
                            elements_dict, complex_types_dict, simple_types_dict,
                            xml_el
                        )
                        return xml_el
                    if alias_local in simple_types_dict:
                        self._generate_simple_type(
                            simple_types_dict[alias_local],
                            xml_el, el_name
                        )
                        return xml_el
                # otherwise, maybe inline <xs:complexType> or <xs:simpleType> on the referenced element:
                for child in ref_el:
                    if child.tag == self.xs + "complexType":
                        self._generate_complex_type(child, elements_dict, complex_types_dict,
                                                    simple_types_dict, xml_el)
                        return xml_el
                    elif child.tag == self.xs + "simpleType":
                        self._generate_simple_type(child, xml_el, el_name)
                        return xml_el

            resolved_complex_type = self._resolve_type_with_context(type_local, el_name, parent_xml, complex_types_dict)
            if resolved_complex_type is not None:
                self._generate_complex_type(resolved_complex_type, elements_dict, complex_types_dict,
                                      simple_types_dict, xml_el)
            elif type_local in simple_types_dict:
                self._generate_simple_type(simple_types_dict[type_local], xml_el, el_name)
            else:
                self._generate_builtin_type(type_local, xml_el, el_name)
        else:
            for child in xsd_element:
                if child.tag == self.xs + "complexType":
                    self._generate_complex_type(child, elements_dict, complex_types_dict, simple_types_dict, xml_el)
                    return None
                elif child.tag == self.xs + "simpleType":
                    self._generate_simple_type(child, xml_el, el_name)
                    return None

            value = self._generate_value_using_llm(parent_xml)
            if value is not None:
                strategy = "randomly_generated_llm"
            else:
                value = f"sample_{el_name}"
                strategy = "randomly_generated"
            xml_el.text = value
            self._record_value(xml_el, strategy)

        return xml_el

    def _record_all_descendants(self, node, parent_path, complex_types_dict):
        # follow named complexType for empty element definitions
        if node.tag == self.xs + "element" and not list(node) and node.get("type"):
            type_name = node.get("type").split(":")[-1]
            type_def = complex_types_dict.get(type_name)
            if type_def is not None:
                self._record_all_descendants(type_def, parent_path, complex_types_dict)
            return
        for child in node:
            # skip compositor tags in path; just recurse
            if child.tag in (self.xs + "sequence", self.xs + "all", self.xs + "choice"):
                self._record_all_descendants(child, parent_path, complex_types_dict)
            elif child.tag == self.xs + "element":
                name = self._get_node_name(child)
                path = f"{parent_path}/{name}"
                self.skipped_elements.append((name, path))
            else:
                tag_name = child.tag.split("}")[-1]
                path = f"{parent_path}/{tag_name}"
                self.skipped_elements.append((tag_name, path))
                self._record_all_descendants(child, path, complex_types_dict)

    def _get_node_name(self, n):
        name_attr = n.get("name")
        if name_attr:
            return name_attr
        ref_attr = n.get("ref")
        return ref_attr.split(":")[-1] if ref_attr else ""

    def filter_skipped_elems(self, pairs):
        # 1) Normalize each path to strip any trailing '/'
        normalized = [(name, path.rstrip('/')) for name, path in pairs]
        filtered = []
        for name, path in normalized:
            # 2) Skip rows with empty 'name'
            if not name.strip():
                continue
            # 3) If 'path' is a prefix of any other path, skip it
            is_prefix = any(
                other_path != path and other_path.startswith(path + '/')
                for _, other_path in normalized
            )
            if is_prefix:
                continue
            filtered.append((name, path))
        return filtered
    
    def _generate_complex_type(self, xsd_complex, elements_dict, complex_types_dict, simple_types_dict, parent_xml):
        """
        Walks an <xs:complexType> node. Handles:
          - <xs:sequence>, <xs:all>, <xs:choice> (including nested choices) by emitting all possible elements
          - <xs:complexContent> with <xs:extension> (walk base first, then extension children)
          - local <xs:element>s
        """
        def process_compositor(node):
            # If this is a choice, only take the first available branch
            if node.tag == self.xs + "choice":

                # Check maxOccurs and if it is unbounded, generate all elements
                max_occurs = node.get("maxOccurs")
                if max_occurs == "unbounded":
                    for child in node:
                        if child.tag == self.xs + "element":
                            self._generate_element(child, elements_dict, complex_types_dict, simple_types_dict,
                                                   parent_xml)
                        elif child.tag in (self.xs + "sequence", self.xs + "all", self.xs + "choice"):
                            process_compositor(child)
                    return

                # Get all the elements under choice
                all_choice_elems = [c.get("name") for c in node.getchildren() if c.tag == self.xs + "element"]

                # Try finding the element in the sample XML instance
                for child in node:
                    if child.tag != self.xs + "element":
                        continue
                    name = self._get_node_name(child)

                    # Determine the element name (name= or ref=) and if element exists in sample XML, generate this branch
                    if self.sample_xml_root.find(f".//{{*}}{name}") is not None:
                        temp_parent_path = parent_xml.getroottree().getpath(parent_xml)
                        temp_el_path = f"{temp_parent_path}/{name}"
                        self.logger.warning(f"\tChoosing <{name}> ({temp_el_path}) based on sample payload XML (Choices:"
                                            f" {', '.join(all_choice_elems)})...")

                        # record skipped siblings
                        for skip in node:
                            if skip is child:
                                continue
                            skip_name = self._get_node_name(skip)
                            skip_parent_path = parent_xml.getroottree().getpath(parent_xml)
                            skip_path = f"{skip_parent_path}/{skip_name}"
                            self.skipped_elements.append((skip_name, skip_path))
                            self._record_all_descendants(skip, skip_path, complex_types_dict)

                        return self._generate_element(child, elements_dict, complex_types_dict, simple_types_dict, parent_xml)

                # Fall back to the first branch (either an element or nested compositor)
                for child in node:
                    if child.tag == self.xs + "element":
                        name = self._get_node_name(child)
                        temp_parent_path = parent_xml.getroottree().getpath(parent_xml)
                        temp_el_path = f"{temp_parent_path}/{name}"
                        self.logger.warning(f"\tChoosing <{name}> ({temp_el_path}) - 1st choice element "
                                             f"(Choices: {', '.join(all_choice_elems)})...")

                        # record skipped siblings
                        for skip in node:
                            if skip is child:
                                continue
                            skip_name = self._get_node_name(skip)
                            skip_parent_path = parent_xml.getroottree().getpath(parent_xml)
                            skip_path = f"{skip_parent_path}/{skip_name}"
                            self.skipped_elements.append((skip_name, skip_path))
                            self._record_all_descendants(skip, skip_path, complex_types_dict)

                        return self._generate_element(child, elements_dict, complex_types_dict, simple_types_dict, parent_xml)

                    elif child.tag in (self.xs + "sequence", self.xs + "all", self.xs + "choice"):
                        # nested choice/sequence/all: recurse into *just this* branch
                        for skip in node:
                            if skip is child:
                                continue
                            skip_name = self._get_node_name(skip) if skip.tag == self.xs + "element" else (
                                skip.tag.split("}"))[-1]
                            skip_parent_path = parent_xml.getroottree().getpath(parent_xml)
                            skip_path = f"{skip_parent_path}/{skip_name}"
                            self.skipped_elements.append((skip_name, skip_path))
                            self._record_all_descendants(skip, skip_path, complex_types_dict)

                        return process_compositor(child)
            else:
                # sequence/all: walk all children
                for child in node:
                    if child.tag == self.xs + "element":
                        self._generate_element(child, elements_dict, complex_types_dict, simple_types_dict, parent_xml)
                    elif child.tag in (self.xs + "sequence", self.xs + "all", self.xs + "choice"):
                        process_compositor(child)

        # First handle <complexContent><extension>
        complex_content = xsd_complex.find(self.xs + "complexContent")
        if complex_content is not None:
            extension = complex_content.find(self.xs + "extension")
            if extension is not None:
                base_type = extension.get("base").split(":")[-1]
                if base_type in complex_types_dict:
                    self._generate_complex_type(complex_types_dict[base_type], elements_dict, complex_types_dict,
                                          simple_types_dict, parent_xml)

                for child in extension:
                    if child.tag in (self.xs + "sequence", self.xs + "all", self.xs + "choice"):
                        process_compositor(child)
            return

        # Otherwise, at top‐level of this complexType, walk every compositor
        for compositor in xsd_complex:
            if compositor.tag in (self.xs + "sequence", self.xs + "all", self.xs + "choice"):
                process_compositor(compositor)

        # And finally any directly nested <element>
        for e in xsd_complex.findall(self.xs + "element"):
            self._generate_element(e, elements_dict, complex_types_dict, simple_types_dict, parent_xml)

    def _build_sample_instance(self, xsd_path, root_element_name="DocumentRequests"):
        """
        Loads the xsD, finds the global <xs:element name="DocumentRequests">,
        creates a new XML tree with <DocumentRequests> as root, and populates
        all nested children (with dummy, simpleType, or dynamically generated built-in values).
        Returns the root of the created etree.
        """
        schemas, xs, elements_dict, complex_types_dict, simple_types_dict = load_schemas_with_definitions(xsd_path, extract_defs=True)
        self.xs = xs

        # Build type context map for generic conflict resolution
        self.type_context_map = build_type_context_map(schemas, xs, complex_types_dict)

        if root_element_name not in elements_dict:
            raise ValueError(f"No global <xs:element name=\"{root_element_name}\"> found in {xsd_path}.")

        xml_root = etree.Element(root_element_name)
        xsd_root_elem = elements_dict[root_element_name]

        root_type = xsd_root_elem.get("type")
        if root_type:
            type_local = root_type.split(":")[-1]
            if type_local in complex_types_dict:
                self._generate_complex_type(complex_types_dict[type_local], elements_dict, complex_types_dict,
                                            simple_types_dict, xml_root)
            elif type_local in simple_types_dict:
                self._generate_simple_type(simple_types_dict[type_local], xml_root, root_element_name)
            else:
                self._generate_builtin_type(type_local, xml_root, root_element_name)
        else:
            for child in xsd_root_elem:
                if child.tag == xs + "complexType":
                    self._generate_complex_type(child, elements_dict, complex_types_dict, simple_types_dict, xml_root)
                    break
                elif child.tag == xs + "simpleType":
                    self._generate_simple_type(child, xml_root, root_element_name)
                    break
            else:
                xml_root.text = f"sample_{root_element_name}"

        return xml_root

    def __get_sample_xml_value(self, name):
        """Get value of element from sample payload XML"""
        inst_elem = self.sample_xml_root.find(f".//{{*}}{name}")
        value = inst_elem.text.strip() if inst_elem is not None and inst_elem.text else ''
        return value

    def _load_mappings(self):
        # Load override values from app report
        mapping_df = pd.read_csv(self.var_tag_mapping_path, sep="\t", usecols=[0, 4], header=0, index_col=None)
        mapping_df = mapping_df.dropna(subset=["VarSampleValue"], ignore_index=True)
        self.values_map_app_report = dict(zip(mapping_df["Tag"], mapping_df["VarSampleValue"]))

    def augment_schema(self):
        """
        Builds a complete schema by adding relevant application request schema based on sample payload XML
            Picks from either DocumentInfo or DocumentInfoXML based on the sample XML
        """
        #  Determine which branch to augment from sample XML
        if self.sample_xml_root.find('.//DocumentInfoXml') is not None:
            branch = 'DocumentInfoXml'
        elif self.sample_xml_root.find('.//DocumentInfo') is not None:
            branch = 'DocumentInfo'
        else:
            raise ValueError("Neither DocumentInfoXml nor DocumentInfo found in the XML instance payload")
        self.logger.info(f"\tUsing {branch} from the choices (based on sample payload XML)...")

        # Get all the root elements from app schema
        app_elems = []
        app_ns_uri = self.app_schema_root.tag.split("}")[0].lstrip("{")
        for el in self.app_schema_root.findall(f"xsd:element", namespaces={"xsd": app_ns_uri}):
            if el.get("name"):
                app_elems.append(el.get("name"))

        # Identify the application-request element under the chosen branch
        for elem in self.sample_xml_root.iter():
            if etree.QName(elem).localname == branch:
                children = list(elem)
                if not children:
                    self.logger.critical(f"\t[{ERR_XSD_003}] Sample payload XML does not have children under"
                                         f" {branch}! Incorrect payload XML or app XSD!")
                    raise Exception(f"[{ERR_XSD_003}] No child elements found under {branch}")

                # Check if all the children of branch are present in app schema
                new_elems_in_payload = [etree.QName(e).localname for e in children if etree.QName(e).localname not in app_elems]
                if len(new_elems_in_payload) > 0:
                    self.logger.critical(f"[{ERR_XSD_003}] New elements ({",".join(new_elems_in_payload)}) found in "
                                         f"payload XML which are not root elements of app XSD! "
                                         f"Incorrect payload XML or app XSD!")
                    raise Exception(f"[{ERR_XSD_003}] New elements ({",".join(new_elems_in_payload)}) found in "
                                    f"payload XML which are not root elements of app XSD! "
                                    f"Incorrect payload XML or app XSD!")

                # Determine the namespace URI for the application element
                app_ns = self.__detect_app_namespace(children[0])
                break
        else:
            raise ValueError(f"Unable to locate the {branch} element in the XML instance payload")

        # Get namespace of the main schema
        main_ns_uri = self.main_schema_root.tag.split("}")[0].lstrip("{")

        # Rebuild root element with updated nsmap (including 'app')
        new_nsmap = self.main_schema_root.nsmap.copy()
        if 'app' in new_nsmap:
            raise ValueError("Namespace prefix 'app' is already in use in the main schema nsmap")
        if app_ns:
            new_nsmap['app'] = app_ns

        # Create new root with combined nsmap
        new_root = etree.Element(self.main_schema_root.tag, nsmap=new_nsmap)
        # Copy the attributes and append all children under the new root
        for attr_name, attr_val in self.main_schema_root.attrib.items():
            new_root.set(attr_name, attr_val)
        for child in self.main_schema_root:
            new_root.append(child)

        # Replace the main schema root
        self.main_schema_tree._setroot(new_root)
        self.main_schema_root = new_root

        # Create and insert an <xs:import> for the application schema
        import_elem = etree.Element(f'{{{main_ns_uri}}}import')
        if app_ns:
            import_elem.set('namespace', app_ns)
        import_elem.set('schemaLocation', os.path.basename(self.app_schema_path))

        # Insert after existing <xs:include> or <xs:import> elements
        insert_index = 0
        for idx, child in enumerate(self.main_schema_root):
            if child.tag in (f'{{{main_ns_uri}}}include', f'{{{main_ns_uri}}}import'):
                insert_index = idx + 1
        self.main_schema_root.insert(insert_index, import_elem)

        # Locate the xs:choice within the DocumentRequest complexType to replace the type
        choice_path = (
            "./{XSD}complexType[@name='DocumentRequest']/{XSD}complexContent/"
            "{XSD}extension/{XSD}sequence/{XSD}choice"
        ).replace('{XSD}', f'{{{main_ns_uri}}}')
        choice = self.main_schema_root.find(choice_path)
        if choice is None:
            raise ValueError("Could not locate the xs:choice for DocumentRequest in the main schema")

        # Modify the target element's type to reference the application type
        target_elem = choice.find(f"{{{main_ns_uri}}}element[@name='{branch}']")
        if target_elem is None:
            raise ValueError(f"Element '{branch}' not found in the xs:choice")
        target_elem.attrib.pop('type', None)

        # Create an anonymous complexType wrapping the application root element
        complex_type = etree.SubElement(target_elem, f'{{{main_ns_uri}}}complexType')
        seq = etree.SubElement(complex_type, f'{{{main_ns_uri}}}sequence')
        # Reference the application root elements by ref
        for app_elem_name in app_elems:
            ref_elem = etree.SubElement(seq, f'{{{main_ns_uri}}}element')
            ref_attr = f'app:{app_elem_name}' if app_ns else app_elem_name
            ref_elem.set('ref', ref_attr)
        self.logger.info(f"\tAdded {len(app_elems)} element/s under {branch} from app XSD: {", ".join(app_elems)}...")

        # Write the augmented schema to output
        save_xml(self.main_schema_tree, self.complete_schema_path)
        self.logger.info(f"\tComplete Schema: {os.path.abspath(self.complete_schema_path)}")

    def extract_element_details(self):
        """Get all the possible elements and their type details from the main schema and any imported/included schemas"""
        # Parse the XSD file along with all the referenced schemas
        schemas = load_schemas_with_definitions(self.complete_schema_path)[0]
        root = schemas[os.path.abspath(self.complete_schema_path)]
        ns = root.nsmap

        # Extract details of all the elements
        element_data = self.__extract_all_elements_info(schemas, ns)

        # write TSV
        self.element_types_df = pd.DataFrame(element_data, columns=["element_name", "type", "type_base", "type_restriction", "info"])
        self.element_types_df.drop_duplicates(subset=["element_name", "type", "type_base", "type_restriction"], inplace=True)
        element_types_tsv_path = os.path.join(self.output_path, 'element_types.tsv')
        self.element_types_df.to_csv(element_types_tsv_path, sep="\t", header=True, index=False, quoting=csv.QUOTE_NONE, escapechar="\\")
        self.logger.info(f"\tElement details: {len(self.element_types_df)} unique elements")
        self.logger.info(f"\t{os.path.abspath(element_types_tsv_path)}")

    def create_complete_payload(self):
        """
        Creates XML from complete schema
            The sample values are generated based on their provided data type and restrictions
        """
        # Get the output file path for sample payload
        output_file_path = os.path.join(self.output_path, "sample_payload.xml")
        tsv_path = os.path.join(self.output_path, "sample_payload_log.tsv")
        skipped_elems_path = os.path.join(self.output_path, "skipped_elems.tsv")

        # Load the mappings
        self._load_mappings()

        # Build the sample instance and save it to file
        sample_root = self._build_sample_instance(self.complete_schema_path, root_element_name="DocumentRequests")
        xml_bytes = etree.tostring(sample_root, pretty_print=True, xml_declaration=True, encoding="UTF-8")
        with open(output_file_path, "wb") as f:
            f.write(xml_bytes)

        # Record the details of sample payload generation to a TSV
        records_df = pd.DataFrame(self.records, columns=["element_name", "path", "value", "strategy"])
        records_df.to_csv(tsv_path, sep="\t", header=True, index=False)

        # Filter skipped elements
        self.skipped_elements = self.filter_skipped_elems(self.skipped_elements)
        if len(self.skipped_elements) > 0:
            self.skipped_elems_df = pd.DataFrame(self.skipped_elements, columns=["element_name", "path"])
            self.skipped_elems_df.to_csv(skipped_elems_path, sep="\t", header=True, index=False)

            self.logger.warning(f"\tSome elements were skipped because of 'choice' in XSD!")
            self.logger.info(f"\tDetails of skipped elements: {os.path.abspath(skipped_elems_path)}")

        self.logger.info(f"\tComplete XML payload: {os.path.abspath(output_file_path)}")
        self.logger.info(f"\tMetadata for complete XML: {os.path.abspath(tsv_path)}")

    def check_sample_elements_in_schema(self):
        """
        Check if all leaf elements from the input XML are present in the complete schema.
        Only compares leaf elements (those with text content or no children).
        This automatically ensures that all ancestor elements are also present in the schema.
        Uses the element paths from the complete payload generation for comparison.
        Reports detailed information about any missing elements.
        """
        try:
            # Extract leaf elements from input XML (with path adjustment for schema compatibility)
            self.sample_elements = self._extract_input_xml_elements()
            self.logger.info(f"\tFound {len(self.sample_elements)} leaf elements in input XML")

            # Extract possible element paths from complete payload generation records
            self.schema_element_paths = self._extract_schema_element_paths()
            self.logger.info(f"\tFound {len(self.schema_element_paths)} leaf elements from the schema")

            # Compare and find missing elements
            missing_elements = self._compare_elements()

            # Report results
            if missing_elements:
                # Update the missing elements list based on unique element dictionary
                missing_elements = list(map(json.loads, dict.fromkeys(json.dumps(d, sort_keys=True) for d in missing_elements)))

                err_xsd_001_msg = (f"[{ERR_XSD_001}] VALIDATION FAILED: Missing elements in sample XML compared to XSD"
                                                f"\n\tTotal missing elements: {len(missing_elements)}")
                for idx, missing in enumerate(missing_elements):
                    err_xsd_001_msg += f"\n\t\t{idx+1}. {missing['element_name']} - Path: {missing['absolute_path']}"
                err_xsd_001_msg += f"\n\tApp specific XSD or sample XML might be wrong! FIX THIS ISSUE BEFORE PROCEEDING!"

                self.logger.critical(err_xsd_001_msg)

            else:
                self.logger.info("\tVALIDATION PASSED: ALL elements from sample XML are present in the schema!")

        except Exception as e:
            self.logger.error(f"\t[{ERR_SYS_001}] Error during element checking: {str(e)}")
            raise

    def _extract_input_xml_elements(self):
        """
        Extract only leaf elements from the input XML with their absolute paths.
        Leaf elements are those that have text content or no children (terminal nodes).
        Adjusts paths to match the schema structure by prepending DocumentRequests.
        Returns a list of dictionaries with element_name and absolute_path.
        """
        elements = []

        def _traverse_element(element, path_parts):
            """Recursively traverse XML elements and build absolute paths for leaf elements only"""
            # Get the local name (without namespace)
            local_name = etree.QName(element).localname
            current_path_parts = path_parts + [local_name]

            # Build absolute path - adjust for schema structure
            if len(current_path_parts) == 1 and local_name == "DocumentRequest":
                # Replace DocumentRequest root with DocumentRequests to match schema
                absolute_path = "/DocumentRequests/DocumentRequest"
            else:
                # For all other elements, build normal path but ensure it starts with DocumentRequests
                if current_path_parts[0] == "DocumentRequest":
                    # Replace the root DocumentRequest with DocumentRequests
                    adjusted_path_parts = ["DocumentRequests"] + current_path_parts
                    absolute_path = "/" + "/".join(adjusted_path_parts)
                else:
                    absolute_path = "/" + "/".join(current_path_parts)

            # Check if this is a leaf element (has text content or no children)
            has_text = bool(element.text and element.text.strip())
            has_children = len(list(element)) > 0
            is_leaf = has_text or not has_children

            # Only add leaf elements to the list for comparison
            if is_leaf:
                elements.append({
                    'element_name': local_name,
                    'absolute_path': absolute_path,
                    'has_text': has_text,
                    'has_children': has_children
                })

            # Recursively process children
            for child in element:
                _traverse_element(child, current_path_parts)

        # Start traversal from root
        _traverse_element(self.sample_xml_root, [])

        return elements

    def _extract_schema_element_paths(self):
        """
        Extract all possible element paths from the complete schema using the information
        from the complete payload generation that was already performed.
        """
        try:
            # Use the records from complete payload generation which contains all element paths
            # that were generated from the complete schema
            if not hasattr(self, 'records') or not self.records:
                self.logger.warning("\t\tNo records available from complete payload generation. Cannot extract schema paths.")
                return set()

            element_paths = set()

            # Extract paths from the records generated during complete payload creation
            for record in self.records:
                element_path = record.get('path', '')
                if element_path:
                    element_paths.add(element_path)

            return element_paths

        except Exception as e:
            import traceback
            err_msg = f"[{ERR_SYS_001}] Error extracting schema element paths: {str(e)}\n{traceback.format_exc()}"
            self.logger.warning(err_msg)
            return set()

    def _compare_elements(self):
        """
        Compare input XML leaf elements against schema element paths and identify missing elements.
        Only compares leaf elements (those with text content or no children)
        This automatically ensures that all ancestor elements are also present in the schema.

        Special handling for empty parent elements: If a leaf element is missing from the complete
        payload but exists as an empty element in input XML, check if it's a valid parent element
        in the schema by looking for child elements with that path as prefix.

        Returns a list of missing elements with details.
        """
        missing_elements = []

        for sample_elem in self.sample_elements:
            sample_path = sample_elem['absolute_path']

            # Exception case as informed by TIAA: Ignoring /DocumentRequests/DocumentRequest/HarteHanksControlInfo
            if sample_path.startswith("/DocumentRequests/DocumentRequest/HarteHanksControlInfo"):
                continue

            # Exception case as informed by TIAA: Ignoring /DocumentRequests/DocumentRequest/TransactionRequestId
            if sample_path == "/DocumentRequests/DocumentRequest/TransactionRequestId":
                continue

            # Exception case as informed by TIAA:
            # Ignoring /DocumentRequests/DocumentRequest/DocumentInfoXml/PTCISRequest/EdlInformation/PlanShareHistoryInfo/PlanClassEndDate
            # For apps: CIS - IA & TPA & IPRO - AFP, CIS - MDO - AFP
            if sample_path == ("/DocumentRequests/DocumentRequest/DocumentInfoXml/PTCISRequest/EdlInformation"
                               "/PlanShareHistoryInfo/PlanClassEndDate"):
                continue

            # Check if this exact path exists in schema
            if sample_path not in self.schema_element_paths:
                # Special case: Check if this is an empty element that's valid in schema
                if not sample_elem['has_text'] and not sample_elem['has_children']:
                    # This is an empty element - check if it's a valid parent in the schema
                    is_valid_parent = self._is_valid_empty_parent_element(sample_path)
                    is_defined_in_schema = self._is_element_defined_in_schema(sample_path)

                    if is_valid_parent or is_defined_in_schema:
                        # validation_method = "parent element" if is_valid_parent else "schema definition"
                        # self.logger.info(f"\t\t✓ Empty element {sample_elem['element_name']} at {sample_path} is valid ({validation_method})")
                        continue  # Skip adding to missing elements

                missing_elements.append(sample_elem)

        return missing_elements

    def _is_valid_empty_parent_element(self, empty_element_path):
        """
        Check if an empty element from input XML is a valid parent element in the schema.
        This is done by checking if any schema element paths have this path as a prefix.
        """
        # Check if any schema paths start with this empty element path followed by "/"
        prefix_to_check = empty_element_path + "/"
        for schema_path in self.schema_element_paths:
            if schema_path.startswith(prefix_to_check):
                return True

        return False

    def _is_element_defined_in_schema(self, element_path):
        """
        Additional validation: Check if an element is actually defined in the schema
        by examining the schema definitions directly.
        """
        try:
            # Load schema definitions
            schemas, xsd_tag, elements_dict, complex_types_dict, simple_types_dict = load_schemas_with_definitions(
                self.complete_schema_path, extract_defs=True)
            element_name = element_path.split('/')[-1]
            if element_name in elements_dict:
                return True

            # Check if element is defined within complex types
            for complex_type_def in complex_types_dict.values():
                elements_in_type = complex_type_def.findall(f".//{xsd_tag}element")
                for elem in elements_in_type:
                    if elem.get('name') == element_name or elem.get('ref', '').split(':')[-1] == element_name:
                        return True

            return False

        except Exception as e:
            self.logger.warning(f"\t\tCould not validate element {element_path} in schema: {str(e)}")
            return False  # Conservative approach - if we can't validate, assume it's not valid

    def validate_testcase_xml_tags_against_schema(self):
        """
        Validate a list of XML tags used in rules against schema elements
        If the tag is not present in schema elements, check in the skipped elements
        """
        missing_tags = set()
        skipped_elem_names = []
        if not self.skipped_elems_df.empty:
            skipped_elem_names = self.skipped_elems_df["element_name"].values.tolist()

        # Extract all element names from schema paths
        schema_element_names = set()
        for path in self.schema_element_paths:
            schema_element_names.add(path.split("/")[-1])

        self.logger.info(f"\tValidating {len(self.xml_tags)} XML tags from rules...")

        # Check each XML tag against schema element names
        # If not present in schema elements, check the skipped elements
        for tag in self.xml_tags:
            if tag not in schema_element_names:
                if len(skipped_elem_names) > 0:
                    if tag not in skipped_elem_names:
                        missing_tags.add(tag)
                        continue
                else:
                    missing_tags.add(tag)
                    continue

        # Report results
        if len(missing_tags) > 0:
            err_xsd_002_msg = (f"[{ERR_XSD_002}] VALIDATION FAILED: XML tags used in rules not found in schema"
                                  f"\n\tMissing tags: {len(missing_tags)}")
            for idx, tag in enumerate(missing_tags):
                err_xsd_002_msg += f"\n\t\t{idx+1}. {tag}"
            err_xsd_002_msg += f"\n\tApp specific XSD might be wrong! FIX THIS ISSUE BEFORE PROCEEDING!"

            self.logger.critical(err_xsd_002_msg)
        else:
            self.logger.info(f"\tVALIDATION PASSED: All XML tags from rules are present in the schema!")

    def _resolve_type_with_context(self, type_local, el_name, parent_xml, complex_types_dict):
        """
        Deterministic type resolution with context awareness to handle any naming conflicts.

        This method uses actual schema structure analysis to resolve type conflicts
        without any heuristics or hardcoded patterns.

        Args:
            type_local: The local type name (e.g., "Document")
            el_name: The element name being processed
            parent_xml: The parent XML element for context
            complex_types_dict: Dictionary of complex type definitions

        Returns:
            The appropriate complex type definition or None
        """
        # First check if there's an unqualified type
        if type_local not in complex_types_dict:
            return None

        # Check if this type has conflicts (multiple definitions across schemas)
        if not hasattr(self, 'type_context_map') or type_local not in self.type_context_map:
            # No context map or no conflicts - use the unqualified version
            return complex_types_dict.get(type_local)

        type_info = self.type_context_map[type_local]

        # If only one schema defines this type, no conflict exists
        if len(type_info['schemas']) <= 1:
            return complex_types_dict.get(type_local)

        # Multiple schemas define this type - resolve using deterministic analysis
        best_schema = self._determine_schema_by_structural_analysis(
            type_local, el_name, parent_xml, type_info, complex_types_dict
        )

        if best_schema:
            qualified_name = f"{best_schema}:{type_local}"
            if qualified_name in complex_types_dict:
                return complex_types_dict[qualified_name]

        # Fallback to unqualified version if analysis fails
        return complex_types_dict.get(type_local)

    def _determine_schema_by_structural_analysis(self, type_local, el_name, parent_xml, type_info, complex_types_dict):
        """
        Determine the most appropriate schema for a type based on deterministic structural analysis.

        This method analyzes the actual schema definitions and usage patterns to make
        a deterministic choice without heuristics.

        Args:
            type_local: The type name
            el_name: Current element name
            parent_xml: Parent XML element
            type_info: Type context information from type_context_map
            complex_types_dict: Dictionary of complex type definitions

        Returns:
            Best matching schema identifier or None
        """
        # Build context hierarchy from current element up to root
        context_hierarchy = self._build_context_hierarchy(parent_xml)

        # Analyze each schema definition to find the best structural match
        schema_analysis = {}

        for schema_id in type_info['schemas']:
            qualified_name = f"{schema_id}:{type_local}"
            if qualified_name not in complex_types_dict:
                continue

            type_def = complex_types_dict[qualified_name]
            analysis = self._analyze_type_definition_structure(type_def, schema_id)
            schema_analysis[schema_id] = analysis

        # Use deterministic selection based on structural analysis
        return self._select_schema_deterministically(
            schema_analysis, context_hierarchy, el_name, type_info
        )

    def _analyze_type_definition_structure(self, type_def, schema_id):
        """
        Analyze the structural characteristics of a type definition.

        Args:
            type_def: The complex type definition XML element
            schema_id: Schema identifier

        Returns:
            Dictionary with structural analysis results
        """
        analysis = {
            'child_elements': [],
            'child_element_count': 0,
            'has_sequences': False,
            'has_choices': False,
            'has_extensions': False,
            'complexity_score': 0,
            'schema_id': schema_id
        }

        # Analyze child elements
        child_elements = type_def.findall(f".//{self.xs}element")
        for elem in child_elements:
            elem_name = elem.get('name')
            if elem_name:
                analysis['child_elements'].append(elem_name)

        analysis['child_element_count'] = len(analysis['child_elements'])

        # Analyze structural patterns
        analysis['has_sequences'] = len(type_def.findall(f".//{self.xs}sequence")) > 0
        analysis['has_choices'] = len(type_def.findall(f".//{self.xs}choice")) > 0
        analysis['has_extensions'] = len(type_def.findall(f".//{self.xs}extension")) > 0

        # Calculate complexity score (deterministic)
        analysis['complexity_score'] = (
            analysis['child_element_count'] * 10 +
            (5 if analysis['has_sequences'] else 0) +
            (3 if analysis['has_choices'] else 0) +
            (2 if analysis['has_extensions'] else 0)
        )

        return analysis

    def _select_schema_deterministically(self, schema_analysis, context_hierarchy, el_name, type_info):
        """
        Select the best schema using deterministic rules based on structural analysis.

        Args:
            schema_analysis: Analysis results for each schema
            context_hierarchy: Element hierarchy context
            el_name: Current element name
            type_info: Type context information

        Returns:
            Selected schema identifier or None
        """
        if not schema_analysis:
            return None

        # Rule 1: If current element name exactly matches a child element in one schema
        for schema_id, analysis in schema_analysis.items():
            if el_name in analysis['child_elements']:
                return schema_id

        # Rule 2: Find schema with the most matching parent context elements
        max_context_matches = 0
        best_context_schema = None

        for schema_id, analysis in schema_analysis.items():
            context_matches = 0
            for parent_elem in context_hierarchy:
                if parent_elem in analysis['child_elements']:
                    context_matches += 1

            if context_matches > max_context_matches:
                max_context_matches = context_matches
                best_context_schema = schema_id

        if best_context_schema and max_context_matches > 0:
            return best_context_schema

        # Rule 3: Use element context from type_info for exact matches
        if el_name in type_info['element_contexts']:
            element_schemas = type_info['element_contexts'][el_name]
            # Return the first schema that defines this element-type combination
            for schema_id in element_schemas:
                if schema_id in schema_analysis:
                    return schema_id

        # Rule 4: Use parent context from type_info
        for parent_elem in context_hierarchy:
            if parent_elem in type_info['parent_contexts']:
                parent_schemas = type_info['parent_contexts'][parent_elem]
                for schema_id in parent_schemas:
                    if schema_id in schema_analysis:
                        return schema_id

        # Rule 5: Select schema with highest complexity (most specific definition)
        if schema_analysis:
            best_schema = max(schema_analysis.items(), key=lambda x: x[1]['complexity_score'])
            return best_schema[0]

        return None

    def _build_context_hierarchy(self, xml_element):
        """
        Build a list of parent element from current element up to root.

        Args:
            xml_element: Current XML element

        Returns:
            List of parent element names
        """
        hierarchy = []
        current = xml_element

        while current is not None:
            if hasattr(current, 'tag') and current.tag:
                hierarchy.append(current.tag)
            current = current.getparent() if hasattr(current, 'getparent') else None

        return hierarchy
