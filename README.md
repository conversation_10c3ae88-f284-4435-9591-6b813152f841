# TIAA XML Generation

A Python-based tool for generating XML test files from application reports and XSD schemas. This project automates the process of parsing application reports, generating complete payloads, creating test cases, and producing XML files for testing purposes.

## Project Overview

This project consists of a 4-step pipeline that processes application reports and generates comprehensive XML test files:

1. **Application Report Analysis** - Parses XML application reports to extract business rules. Reports errors related to missing variables in rules, undefined rules, missing variable-XML tag mapping
2. **Complete Payload Generation** - Creates comprehensive XML payloads using XSD schemas and sample XML files. Validates input files by checking for XSD completion based on sample XML file and tags being used in rules
3. **Test Case Generation** - Uses LLM-based approach to generate positive and negative test cases
4. **XML File Generation** - Produces final XML test files based on generated test cases

## Features

- Support for XML application report format
- XSD-based XML payload generation with intelligent value assignment
- AI-powered test case generation for comprehensive coverage
- Configurable logging and error handling
- Batch processing capabilities

## Prerequisites

- Python 3.12 or higher
- OpenAI API access (for test case generation)

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd tiaa-poc-xml-generation
   ```

2. **Create a virtual environment (recommended):**
   ```bash
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up OpenAI API key:**
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```
   Or create a `.env` file in the project root with:
   ```
   OPENAI_API_KEY=your-api-key-here
   ```
## Input Files Required

1. **Application Report** - HTML or XML file containing business rules and validation logic
2. **XSD Schema Files** - Main and application-specific XSD files
3. **Sample XML** - Example XML payload for the application

## Usage

### Option 1: Run All Steps (Recommended)

Use the main script to execute all steps in sequence:

```bash
python main.py --application_path "../../applications/YourApp" \
  --app_report_path "AppReport.xml" \
  --main_xsd_path "xsd/main-schema.xsd" \
  --app_xsd_path "xsd/app-schema.xsd" \
  --sample_xml_path "sample.xml" \
  --mapping_path "xml_parse/app_info/xml_tags_to_variables.tsv" \
  --testcase_generation_details_tsv "xml_parse/app_info/testcase_generation_details.tsv" \
  --app_report_json_path "xml_parse/AppReport.json" \
  --complete_xml_log "complete_payload/sample_payload_log.tsv" \
  --element_types_tsv_path "complete_payload/element_types.tsv" \
  --rule_errors_tsv "logs/rule_errors_from_parsing.tsv" \
  --test_cases_json_path "xml_parse/test_cases/AppReport_test_cases.json" \
  --complete_xml_path "complete_payload/sample_payload.xml"
```

### Option 2: Run Individual Steps

#### Step 1: Parse Application Report
```bash
python 1_get_app_report_json.py \
  --application_path "../../applications/YourApp" \
  --app_report_path "AppReport.xml"
```

**Purpose:** Converts HTML or XML application reports into structured JSON format, extracting business rules, validation logic, and variable mappings.

**Outputs:**
- JSON application report file
- Variable-to-XML tag mapping TSV
- Test case generation details TSV
- Parsing logs

#### Step 2: Generate Complete Payload
```bash
python 2_get_complete_payload.py \
  --application_path "../../applications/YourApp" \
  --main_xsd_path "xsd/main-schema.xsd" \
  --app_xsd_path "xsd/app-schema.xsd" \
  --sample_xml_path "sample.xml" \
  --mapping_path "xml_parse/app_info/xml_tags_to_variables.tsv" \
  --testcase_generation_details_tsv "xml_parse/app_info/testcase_generation_details.tsv"
```

**Purpose:** Creates a comprehensive XML payload containing all possible elements from XSD schemas, with intelligent value generation based on:
- Sample XML values
- Variable values from application report
- Random generation based on data type and constraints
- LLM-generated values for semantic understanding

**Outputs:**
- Complete payload XML file
- Element types TSV
- Sample payload log TSV
- Generation logs

#### Step 3: Generate Test Cases
```bash
python 3_generate_test_cases.py \
  --application_path "../../applications/YourApp" \
  --app_report_json_path "xml_parse/AppReport.json" \
  --testcase_generation_details_tsv "xml_parse/app_info/testcase_generation_details.tsv" \
  --complete_xml_log "complete_payload/sample_payload_log.tsv" \
  --element_types_tsv_path "complete_payload/element_types.tsv" \
  --rule_errors_tsv "logs/rule_errors_from_parsing.tsv"
```

**Purpose:** Uses LLM-based approach to generate comprehensive positive and negative test cases based on business rules extracted from the application report.

**Outputs:**
- Test cases JSON file
- Test case generation logs

#### Step 4: Generate XML Files
```bash
python 4_generate_xml_files.py \
  --application_path "../../applications/YourApp" \
  --test_cases_json_path "xml_parse/test_cases/AppReport_test_cases.json" \
  --complete_xml_path "complete_payload/sample_payload.xml" \
  --element_types_tsv_path "complete_payload/element_types.tsv"
```

**Purpose:** Creates final XML test files for each generated test case, both positive and negative scenarios.

**Outputs:**
- Individual XML test files
- Batch XML files
- Generation logs
