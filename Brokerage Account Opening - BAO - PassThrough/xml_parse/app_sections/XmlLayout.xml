<?xml version='1.0' encoding='UTF-8'?>
<Layouts>
  <XMLLayout Tag="DocumentRequests" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="DocumentRequest" Ignore="No" NewCustomer="Yes" NextTag="No" NewSection="No"/>
  <XMLLayout Tag="BatchCounter" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="761" Name="GO_DR_BATCH_COUNTER" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestUserId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="757" Name="GO_DR_RQST_USER_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestUserName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="758" Name="GO_DR_RQST_USER_NAME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestDateTime" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="759" Name="GO_DR_RQST_DATE_TIME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CompositeOrchestrationId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3710" Name="GO_DR_COMPOSITE_ORCH_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OrchestrationId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="2410" Name="GO_DR_ORCHESTRATION_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BusinessUnitCode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4636" Name="GO_DR_BUSINESS_UNIT_CODE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DocCode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4637" Name="GO_DR_DOC_CODE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Version" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4638" Name="GO_DR_VERSION" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DocSequence" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4639" Name="GO_DR_DOC_SEQUENCE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BatchInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="760" Name="GO_DR_BATCH_IND" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailItemInfo" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="ApplicationId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="763" Name="GO_MI_PCKGE_CDE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="UniversalId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="753" Name="GO_MI_BC_PIN_NBR" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DocumentRequestId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="764" Name="GO_DDA_DOC_REQ_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PrinterId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="765" Name="GO_MI_PRNTR_ID_CDE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Prefix" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1147" Name="GO_MI_PREFIX" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FirstName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1149" Name="GO_MI_FIRST_NAME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MiddleName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1148" Name="GO_MI_MID_NAME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Suffix" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1146" Name="GO_MI_SUFFIX" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LastName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="766" Name="GO_MI_LAST_NAME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FullName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="756" Name="GO_MI_FULL_NAME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AddressTypeCode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="754" Name="GO_MI_ADD_TYP_CDE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AddressLines" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="AddressLine" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="768" Name="GO_MI_ADDRESS_LINE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmailAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="770" Name="GO_MI_ALT_DLVRY_ADDR" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LetterDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="771" Name="GO_MI_LETTER_DATE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DeliveryType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="775" Name="GO_MI_PCKGE_DLVRY_TYP" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EXPAGInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="776" Name="GO_MI_PCKGE_IMAGE_IND" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PortalDocDesc" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1449" Name="GO_MI_PORTAL_DOC_DESC" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ArchivalInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1429" Name="GO_MI_ARCHIVAL_IND" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PlanId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1430" Name="GO_MI_PLAN_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BusinessDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1431" Name="GO_MI_BUSINESS_DATE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CcpPackageId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="752" Name="GO_MI_PST_RQST_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DocumentInfoXml" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="BAORequest" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="AccountType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16067" Name="FR_BAO_AccountType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AccountCategory" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16068" Name="FR_BAO_AccountCategory" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ProposalNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16164" Name="FR_ProposalNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LetterRequest" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="LetterID" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3565" Name="FR_LetterType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AccountOwnerInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FullName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14964" Name="FR_AccountOwnerName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Prefix" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14965" Name="FR_AccountOwnerTitle" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FirstName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14966" Name="FR_AccountOwnerFirstName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MiddleName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14967" Name="FR_AccountOwnerMiddleName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LastName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14968" Name="FR_AccountOwnerLastName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Suffix" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15014" Name="FR_AccountOwnerSuffix" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14969" Name="FR_AccountOwnerSSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmailAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14970" Name="FR_AccountOwnerEmail" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Gender" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14971" Name="FR_AccountOwnerGender" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BirthDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14972" Name="FR_AccountOwnerBirthDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MaritalStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14974" Name="FR_AccountOwnerMaritalStatus" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BusinessPhone" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15478" Name="FR_AccountOwnerBusinessPhone" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15373" Name="FR_AccountOwnerResidentialStreetAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15374" Name="FR_AccountOwnerResidentialCity" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15375" Name="FR_AccountOwnerResidentialState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15376" Name="FR_AccountOwnerResidentialZipcode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialCountry" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15377" Name="FR_AccountOwnerResidentialCountry" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailingStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15378" Name="FR_AccountOwnerMailingStreetAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailingCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15379" Name="FR_AccountOwnerMailingCity" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailingState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15380" Name="FR_AccountOwnerMailingState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailingZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15381" Name="FR_AccountOwnerMailingZipcode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailingCountry" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15382" Name="FR_AccountOwnerMailingCountry" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="StateOfResidence" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14981" Name="FR_AccountOwnerStateofResidence" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Citzenship" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14980" Name="FR_AccountOwnerCitizenship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmploymentStatus" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="EmploymentStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14982" Name="FR_BAO_EmploymentStatus" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmploymentInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="EmployerName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15442" Name="FR_BAO_EmployerName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmploymentTitle" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15444" Name="FR_BAO_EmployerTitle" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AffiliationInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TradeCompany" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TradeCompanyCheckBox" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14985" Name="FR_AccountOwnerTradeCompanyCB" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TiaaCref" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TIAACREFCheckBox" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14986" Name="FR_AccountOwnerTIAACREFCB" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MemberFirm" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FirmCheckBox" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14987" Name="FR_AccountOwnerMemberFirmCB" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RelationshipToEmployee" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15469" Name="FR_BAO_Firm_Relationship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NameOfEmployee" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15468" Name="FR_BAO_Firm_PersonName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NameOfFirm" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15470" Name="FR_BAO_Firm_Name" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SeniorMilitaryCheckBox" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14988" Name="FR_AccountOwnerSeniorMilitaryCB" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="InvestmentProfile" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14989" Name="FR_AccountOwnerInvestmentProfile" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PhoneType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="17913" Name="FR_BAO_PhoneType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
      <DataArea VarOI="18531" Name="FR_AccountOwner_PhoneType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="GrantorInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18770" Name="FR_AccountOwner_GrantorType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BrokerageAccountNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15015" Name="FR_AccountOwnerBrokerageAccountNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PrimaryBeneficiary" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="BeneficiaryName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3283" Name="FR_PrimaryBeneficiaryName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SpousalWaiver" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16645" Name="FR_BAO_02_SpousalWaiver" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DeliveryMethod" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18450" Name="FR_BAO_DeliveryMethod" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ApplicationForm" Ignore="No" NewCustomer="No" NewSection="Yes" Section="ApplicationForm" FirstOnly="No"/>
  <XMLLayout Tag="FormNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18540" Name="FR_BAO_FormNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BrokerageServices" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="NonIraOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14990" Name="FR_BAO_NonIraOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
      <DataArea VarOI="20318" Name="BAO_NonIRAOption_2" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TrustAccount" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TrustAccountName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16134" Name="FR_BAO_TrustAccountName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TrustSSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16135" Name="FR_BAO_TrustSSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TrustEffectiveDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16136" Name="FR_BAO_TrustEffectiveDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AmendmentDateOfTrust" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="20321" Name="FR_BAO_AmendmentDateofTrust" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TrustType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="20322" Name="FR_BAO_TrustType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="InvestmentObjective" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14991" Name="FR_BAO_InvestmentObjective" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AnnualIncome" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14992" Name="FR_BAO_AnnualIncome" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NetWorth" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14993" Name="FR_BAO_NetWorth" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxBraket" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14994" Name="FR_BAO_TaxBraket" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SourceOfFunds" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14995" Name="FR_BAO_SourceOfFunds" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SweepSelection" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="SweepAccount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16176" Name="FR_BAO_SweepSelectionSweepAccount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Cash" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16177" Name="FR_BAO_SweepSelectionCash" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Liquid" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21790" Name="FR_BAO_Liquid" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Prime" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16178" Name="FR_BAO_SweepSelectionPrime" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DreyfusGovCashManageServiceShares" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21785" Name="FR_BAO_DreyfusGovCashManageServiceShares" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DreyfusGovSecCashManageInvestor" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21786" Name="FR_BAO_DreyfusGovSecCashManageInvestor" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DreyfusGovCashManageInvester" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21787" Name="FR_BAO_DreyfusGovCashManageInvester" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FederatedHermesGovObligationsCash" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21788" Name="FR_BAO_FederatedHermesGovObligationsCash" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FederatedHermesTrustUSTreasuryOblicationsCash" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21789" Name="FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Edelivery" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="21784" Name="FR_BAO_Edelivery" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Margin" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15471" Name="FR_BAO_Margin" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TrustCertification" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="DelegateToAppointedAgent" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18568" Name="FR_BAO_Trust_DelegateToAppointedAgent" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DelegateToOutsideProfessional" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18569" Name="FR_BAO_Trust_DelegateToOutsideProfessional" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FR_BAO_DeliveryMethod" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="OtherDescription" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16133" Name="FR_BAO_OtherDescription" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SourceOfFundsOtherDescription" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16142" Name="FR_BAO_SourceOfFundsOtherDescription" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentRelationship" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16160" Name="FR_BAO_DecendentRelationship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AccountNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16146" Name="FR_BAO_InitialTransactionsAccountNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FullTransfer" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16147" Name="FR_BAO_InitialTransactionsFullTransfer" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DeliveringAccountClosed" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16148" Name="FR_BAO_InitialTransactionsDeliveringAccountClosed" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PartialTransfer" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16149" Name="FR_BAO_InitialTransactionsPartialTransfer" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TransferCashAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16150" Name="FR_BAO_InitialTransactionsTransferCashAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TransferShares" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="SharesDescription" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16151" Name="FR_BAO_InitialTransactionsSharesDescription" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SharesQuantity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16152" Name="FR_BAO_InitialTransactionsSharesQuantity" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SignatoryName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="930" Name="GO_MI_EMPL_SGNTRY_NME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SignatoryUnit" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="773" Name="GO_MI_EMPL_UNIT_WORK_NME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CashWithdrawalRequest" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="PersonalInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="StateOfResidence" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3558" Name="FR_StateOfResidence" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Citizenship" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3559" Name="FR_Citizenship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PhoneNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="5953" Name="FR_PhoneNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PlanName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3562" Name="FR_PlanName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PlanId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3563" Name="FR_PlanId" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FormInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FormNumber" Ignore="No" NewCustomer="No" NewSection="Yes" Section="FORM" FirstOnly="No">
    <DataAreas>
      <DataArea VarOI="3620" Name="FR_FormNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SpousalWaiverInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="InstitutionName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3726" Name="FR_InstitutionName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3727" Name="FR_RequestDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MarriedIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4580" Name="FR_MarriedIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="WithdrawalMethodType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3751" Name="FR_WithdrawalMethodType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalWithdrawalAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3754" Name="FR_TotalWithdrawalAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwatFrequency" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4574" Name="FR_SwatFrequency" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwatNumberofPayments" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4575" Name="FR_SwatNumberofPayments" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwatPaymentStopDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4577" Name="FR_SwatPaymentsStopDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwatNoFundsOrStopIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4581" Name="FR_SwatNoFundsOrStopIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwTransactionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="11692" Name="FR_SwTransactionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwDistributionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="11693" Name="FR_SwDistributionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AnnualizedRequestAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="11694" Name="FR_AnnualizedRequestAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwRmd2in1OptionIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="11695" Name="FR_SwRmd2in1OptionIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwRmd2in1RequiredIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="11696" Name="FR_SwRmd2in1RequiredIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwRmd2in1EligibleIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="11697" Name="FR_SwRmd2in1EligibleIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalPercentageDollarValue" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4443" Name="FR_TotalPercentageDollarValue" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SwSubPlanDistributionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="12025" Name="FR_SwSubPlanDistributionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalWithdrawalPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3753" Name="FR_TotalWithdrawalPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalBalanceAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4579" Name="FR_TotalBalanceAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FromInvestmentFundList" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FundNameArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4466" Name="LT_FundNameArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundTickerSymbolArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6460" Name="LT_FundTickerSymbolArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalPercentageArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6401" Name="LT_FundWithdrawalPercentageArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalAmountArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6402" Name="LT_FundWithdrawalAmountArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ProfileInfo" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="MailingAddressIndicator" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="InputForms" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="PullForm_NSP" Ignore="No" NewCustomer="No" NewSection="Yes" Section="FORM_NSP" FirstOnly="No"/>
  <XMLLayout Tag="Name" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3756" Name="FR_PullForm_NonStaplingPortraitFileName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Salutation" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="772" Name="GO_MI_LTTR_SLTTN_TXT" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ArchivalAck" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1450" Name="GO_MI_ARCHACK" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EdelAck" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1427" Name="GO_MI_EDELACK" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PrintAck" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1451" Name="GO_MI_PRINTACK" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExpAgContracts" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="954" Name="GO_PI_CONTRACTS" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalAmountArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6402" Name="LT_FundWithdrawalAmountArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFrequency" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TaxFrequency" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="PmdOptions" Ignore="No" NewCustomer="No" NewSection="Yes" Section="RmdOptions" FirstOnly="No"/>
  <XMLLayout Tag="RmdTotalWithdrawalPayments" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="RmdYearEndBalanceAdjustment" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="9171" Name="LT_RmdYearEndBalanceAdjustment" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExcludeGrandfatheredAmountIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="9172" Name="LT_ExcludeGrandfatheredAmountIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PaymentDetailsFundTable" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FundRothHeaderIndArray" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FundNameArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4466" Name="LT_FundNameArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalPercentageArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6401" Name="LT_FundWithdrawalPercentageArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalAmountArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6402" Name="LT_FundWithdrawalAmountArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundRothTotalArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="9706" Name="LT_RothTotalAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="InServiceType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8627" Name="LT_InServiceType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RecurringPaymentIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4296" Name="FR_RecurringPaymentIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FaxNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="2380" Name="GO_MI_FAX_NUMBER" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExpAgWorkflowInfo" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="ExportInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="822" Name="GO_PI_EXPORT_IND" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaskId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="828" Name="GO_PI_TASK_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaskType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="829" Name="GO_PI_TASK_TYPE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaskGuid" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="823" Name="GO_PI_TASK_GUID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ActionStep" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="824" Name="GO_PI_ACTION_STEP" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DocContent" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="834" Name="GO_PI_DOC_CONTENT" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaskStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="827" Name="GO_PI_TASK_STATUS" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PlanId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="833" Name="GO_PI_PLAN_ID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TiaaDateTime" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="826" Name="GO_PI_TIAA_TIME" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="830" Name="GO_PI_SSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="UniversalId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="831" Name="GO_PI_PIN_NPIN_PPG" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="UniversalType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="832" Name="GO_PI_PIN_TYPE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DeclineReason" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="5343" Name="LT_DeclineReasonText" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestOptionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4456" Name="LT_RequestOptionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="WithdrawalType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4457" Name="LT_WithdrawalType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="F402fOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4458" Name="LT_F402fOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RelativeValueDisclosureOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4459" Name="LT_RelativeValueDisclosureOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NewEnrollmentIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6381" Name="LT_NewEnrollmentIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LetterTransactionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="10551" Name="LT_LetterTransactionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LetterDistributionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="10552" Name="LT_LetterDistributionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BinItems" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="BinItem" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="774" Name="GO_MI_BIN_ITEMS" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PlanName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4276" Name="LT_PlanName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Frequency" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4628" Name="LT_Frequency" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LastPaymentDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4501" Name="LT_LastPaymentDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NetAmountofLastPayment" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4502" Name="LT_NetAmountOfLastPayment" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PaymentStopDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="5955" Name="LT_PaymentStopDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OtpDeliveryMode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6467" Name="LT_OtpDeliveryMode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RecurringPhase" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8349" Name="LT_RecurringPhase" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FirstPaymentDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4630" Name="LT_FirstPaymentDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NumberofPayments" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6388" Name="LT_NumberOfPayments" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="UntilFundsDepletedInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8485" Name="LT_UntilFundsDepletedInd" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FinalPaymentDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8403" Name="LT_FinalPaymentDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SubsequentPaymentsDay" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="5387" Name="LT_SubsequentPaymentsDay" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ProductSubCode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6399" Name="LT_ProductSubCode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestedPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6469" Name="LT_RequestedPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TiaaIndexContractNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6206" Name="LT_TiaaIndexContractNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalPercentageArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6401" Name="LT_FundWithdrawalPercentageArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RmdPlanTotal" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6406" Name="LT_RmdPlanTotal" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OptionalWithholdingAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6412" Name="LT_OptionalWithholdingAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxMaritalStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6415" Name="LT_TaxMaritalStatus" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxExemptIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6416" Name="LT_TaxExemptIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NumberofExemptions" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6417" Name="LT_NumberofExemptions" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFlatDollarAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6418" Name="LT_TaxFlatDollarAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFixedPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6419" Name="LT_TaxFixedPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxWithholdingAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6428" Name="LT_TaxWithholdingAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFixedDollarAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6422" Name="LT_TaxFixedDollarAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OptionalWithholdingAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6412" Name="LT_OptionalWithholdingAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxMaritalStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6415" Name="LT_TaxMaritalStatus" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NumberofExemptions" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6417" Name="LT_NumberofExemptions" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFlatDollarAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6418" Name="LT_TaxFlatDollarAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFixedPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6419" Name="LT_TaxFixedPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxWithholdingAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6428" Name="LT_TaxWithholdingAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFixedDollarAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6422" Name="LT_TaxFixedDollarAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RmdOptions" Ignore="No" NewCustomer="No" NewSection="Yes" Section="RmdOptions" FirstOnly="Yes"/>
  <XMLLayout Tag="RmdCalculationMethod" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6434" Name="LT_RmdCalculationMethod" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SpouseDateofBirth" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6435" Name="LT_SpouseDateofBirth" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="UniformLifeExpectancyFactor" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6470" Name="LT_UniformLifeExpectancyFactor" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="JointLifeExpectancyFactor" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6471" Name="LT_JointLifeExpectancyFactor" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RmdTotalWithdrawalPayments" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6438" Name="LT_RmdTotalWithdrawalPayments" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RmdPriorYearGrandfatheredAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6439" Name="LT_RmdPriorYearGrandfatheredAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RmdPriorYearEndAccumulation" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6440" Name="LT_RmdPriorYearEndAccumulation" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="GfAccumulationEligibilityIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="10553" Name="LT_GfAccumulationEligibilityIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3175" Name="LT_BankName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankTransitNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3174" Name="LT_BankTransitNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankAccountNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3173" Name="LT_BankAccountNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankRoutingNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6442" Name="LT_BankRoutingNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankAccountType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4635" Name="LT_BankAccountType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExternalRolloverPlanType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6443" Name="LT_ExternalRolloverPlanType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExternalCompanyName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3181" Name="LT_ExternalCompanyName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExternalCompanyAddressLine1" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6444" Name="LT_ExternalCompanyAddressLine1" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExternalCompanyAddressLine2" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6445" Name="LT_ExternalCompanyAddressLine2" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExternalCompanyAccountNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4282" Name="LT_ExternalCompanyAccountNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="City" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6446" Name="LT_City" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="State" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6447" Name="LT_State" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ZipCode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6448" Name="LT_ZipCode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RolloverType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6455" Name="LT_RolloverType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NewEnrollmentInRollover" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6456" Name="LT_NewEnrollmentInRollover" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RolledOverPlanName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6457" Name="LT_RolledOverPlanName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RolledOverTiaaContractNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6458" Name="LT_RolledOverTiaaContractNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="InvestmentOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6459" Name="LT_InvestmentOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ToInvestmentFundList" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FundNameArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4466" Name="LT_FundNameArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundTickerSymbolArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6460" Name="LT_FundTickerSymbolArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalPercentageArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6401" Name="LT_FundWithdrawalPercentageArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CarrierName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6474" Name="LT_CarrierName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CarrierAddress" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="AddressLine" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6475" Name="LT_CarrierAddressLines" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OtherAccountName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6461" Name="LT_OtherAccountName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OtherAccountNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6462" Name="LT_OtherAccountNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OtherAccountType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6464" Name="LT_OtherAccountType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="IvcOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="7945" Name="LT_IvcOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6373" Name="LT_EmployerName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LetterInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="LetterType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3565" Name="FR_LetterType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LetterInfo" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="SpousalWaiverOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4460" Name="LT_SpousalWaiverOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SponsorIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4548" Name="LT_SponsorIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MarriedUnmarriedIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4546" Name="LT_MarriedUnmarriedIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RecurringPaymentIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="5047" Name="LT_RecurringPaymentIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MaritalStatusValidation" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8626" Name="LT_MaritalStatusValidation" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PrismSlaInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="10083" Name="LT_PrismSlaInd" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PrismSponsorApprovalDays" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="10084" Name="LT_PrismSponsorApprovalDays" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PrismSponsorApprovalExtDays" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="10085" Name="LT_PrismSponsorApprovalExtDays" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TransactionSummaryInfo" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TransactionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6371" Name="LT_TransactionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DistributionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6372" Name="LT_DistributionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="GrossAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6374" Name="LT_GrossAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Fees" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6376" Name="LT_Fees" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EstimatedTaxes" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6377" Name="LT_EstimatedTaxes" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NetWithdrawal" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6378" Name="LT_NetWithdrawal" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DataValidationRequiredIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6382" Name="LT_DataValidationRequiredIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PendingOtpIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6383" Name="LT_PendingOtpIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ProfileInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="MailingAddressIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6384" Name="LT_MailingAddressIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmailAddressIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6385" Name="LT_EmailAddressIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EdeliveryPreferenceIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="9170" Name="LT_EdeliveryPreferenceIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EffectiveDateofDistribution" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6397" Name="LT_EffectiveDateofDistribution" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PaymentDetailsInformation" Ignore="No" NewCustomer="No" NewSection="Yes" Section="PaymentDetail" FirstOnly="No"/>
  <XMLLayout Tag="AnnualizedRequestAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6424" Name="LT_AnnualizedRequestAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FrequencyPaymentAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6425" Name="LT_FrequencyPaymentAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ProductCode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6398" Name="LT_ProductCode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RequestedAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6468" Name="LT_RequestedAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TiaaNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4461" Name="LT_TiaaNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="IraIndexIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6207" Name="LT_IraIndexIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CrefNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4462" Name="LT_CrefNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SettlementSelectionType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6400" Name="LT_SettlementSelectionType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PaymentDetailsInvestmentInfo" Ignore="No" NewCustomer="No" NewSection="Yes" Section="PaymentDetailInvestment" FirstOnly="No"/>
  <XMLLayout Tag="PaymentDetailsFundTable" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FundNameArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4466" Name="LT_FundNameArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FundWithdrawalAmountArray" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6402" Name="LT_FundWithdrawalAmountArray" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AfterTaxAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6403" Name="LT_AfterTaxAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxableAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6405" Name="LT_TaxableAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalAmountRequested" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6407" Name="LT_TotalAmountRequested" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxWithholdingFederal" Ignore="No" NewCustomer="No" NewSection="Yes" Section="TaxWithhholdingFederal" FirstOnly="No"/>
  <XMLLayout Tag="TaxAnnualizedAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6409" Name="LT_TaxAnnualizedAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFrequencyAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="7969" Name="LT_TaxFrequencyAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OmniTaxAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6411" Name="LT_OmniTaxAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OptionalWithholdingPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6413" Name="LT_OptionalWithholdingPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFrequencyAmountFedAndState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8095" Name="LT_TaxFrequencyAmountFedAndState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxOptOutIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6414" Name="LT_TaxOptOutIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalTaxWithheldAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6423" Name="LT_TotalTaxWithheldAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalTaxFrequencyAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6433" Name="LT_TotalTaxFrequencyAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxWithholdingState" Ignore="No" NewCustomer="No" NewSection="Yes" Section="TaxWithholdingState" FirstOnly="No"/>
  <XMLLayout Tag="TaxAnnualizedAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6409" Name="LT_TaxAnnualizedAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFrequencyAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="7969" Name="LT_TaxFrequencyAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OmniTaxAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6411" Name="LT_OmniTaxAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="OptionalWithholdingPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6413" Name="LT_OptionalWithholdingPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxFrequencyAmountFedAndState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8095" Name="LT_TaxFrequencyAmountFedAndState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxOptOutIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6414" Name="LT_TaxOptOutIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TaxExemptIndicator" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6416" Name="LT_TaxExemptIndicator" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalTaxWithheldAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6423" Name="LT_TotalTaxWithheldAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TotalTaxFrequencyAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6433" Name="LT_TotalTaxFrequencyAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="PaymentDestination" Ignore="No" NewCustomer="No" NewSection="Yes" Section="PaymentDestination" FirstOnly="No"/>
  <XMLLayout Tag="PaymentMethod" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6441" Name="LT_PaymentMethod" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SendToAddress" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="AddressLine" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3191" Name="LT_SendAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="IncludeIvcAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="7518" Name="LT_IncludeIvcAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExcludedIvcAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8001" Name="LT_ExcludedIvcAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="AfterTaxPaymentDestination" Ignore="No" NewCustomer="No" NewSection="Yes" Section="AfterTaxPaymentDestination" FirstOnly="No"/>
  <XMLLayout Tag="PaymentMethod" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6441" Name="LT_PaymentMethod" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3175" Name="LT_BankName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankTransitNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3174" Name="LT_BankTransitNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankAccountNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3173" Name="LT_BankAccountNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankRoutingNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="6442" Name="LT_BankRoutingNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BankAccountType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="4635" Name="LT_BankAccountType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SendToAddress" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="AddressLine" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3191" Name="LT_SendAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="IvcOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="7945" Name="LT_IvcOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="IncludeIvcAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="7518" Name="LT_IncludeIvcAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ExcludedIvcAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8001" Name="LT_ExcludedIvcAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TiaaNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3560" Name="FR_TiaaNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CrefNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3561" Name="FR_CrefNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SubplanId" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3564" Name="FR_SubplanId" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RothHeaderInd" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="9705" Name="LT_RothHeaderInd" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RothTotalAmount" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="9706" Name="LT_RothTotalAmount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MailingAddress" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="AddressLine" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14975" Name="FR_AccountOwnerAddressLines" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerTaxID" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16153" Name="FR_BAO_EmployerTaxID" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15440" Name="FR_BAO_EmployerCity" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15445" Name="FR_BAO_EmployerZipcode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NameOfPerson" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15466" Name="FR_BAO_Trade_PersonName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="CompanyNameSymbol" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15467" Name="FR_BAO_Trade_CompanyNameSymbol" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="GrantorInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FullName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18699" Name="FR_GrantorName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Prefix" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18700" Name="FR_Grantor_Prefix" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FirstName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18701" Name="FR_Grantor_FirstName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MiddleName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18702" Name="FR_Grantor_Middlename" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LastName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18703" Name="FR_Grantor_LastName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18704" Name="FR_Grantor_SSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmailAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18705" Name="FR_Grantor_Email" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Gender" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18706" Name="FR_Grantor_Gender" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BirthDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18707" Name="FR_Grantor_BirthDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MaritalStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18708" Name="FR_Grantor_MaritalStatus" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BusinessPhone" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18709" Name="FR_Grantor_BusinessPhone" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18710" Name="FR_Grantor_ResAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18711" Name="FR_Grantor_ResCity" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18712" Name="FR_Grantor_ResState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18713" Name="FR_Grantor_ResZip" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialCountry" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18714" Name="FR_Grantor_ResCountry" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="JointGrantorInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="FullName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18715" Name="FR_JointGrantor_FullName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Prefix" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18716" Name="FR_JointGrantor_Prefix" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="FirstName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18717" Name="FR_JointGrantor_FirstName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MiddleName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18718" Name="FR_JointGrantor_MiddleName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="LastName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18719" Name="FR_JointGrantor_LastName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18720" Name="FR_JointGrantor_SSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmailAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18721" Name="FR_JointGrantor_Email" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Gender" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18722" Name="FR_JointGrantor_Gender" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BirthDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18723" Name="FR_JointGrantor_Birthdate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="MaritalStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18724" Name="FR_JointGrantor_MaritalStatus" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BusinessPhone" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18725" Name="FR_JointGrantor_BusinessPhone" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18726" Name="FR_JointGrantor_ResStreetAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18727" Name="FR_JointGrantor_ResCity" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18728" Name="FR_JointGrantor_ResState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18729" Name="FR_JointGrantor_ResZip" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ResidentialCountry" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18730" Name="FR_JointGrantor_ResCountry" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18696" Name="FR_PrimaryBeneficiary_BeneficiaryType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryLDPS" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16131" Name="FR_PrimaryBeneficiaryLDPS" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18697" Name="FR_ContingentBeneficiary_BeneficiaryType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TrustAmmendmentDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18771" Name="FR_BAO_Trust_AmmendmentDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="TypeOfTrust" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18570" Name="FR_BAO_TypeOfTrust" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="JointTenantAccountType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18883" Name="FR_JointTenantAccount" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15439" Name="FR_BAO_EmployerAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15443" Name="FR_BAO_EmployerState" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="EmployerCountry" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15441" Name="FR_BAO_EmployerCountry" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="RelationshipToEmployee" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15481" Name="FR_BAO_TIAACREF_Relationship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="NameOfEmployee" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15482" Name="FR_BAO_TIAACREF_PersonName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="JointTenantAccountType" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="BeneficiaryPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3284" Name="FR_PrimaryBeneficiaryPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiarySSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3285" Name="FR_PrimaryBeneficiarySSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryRelationship" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3287" Name="FR_PrimaryBeneficiaryRelationship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryDateOfBirth" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3286" Name="FR_PrimaryBeneficiaryBirthDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryGender" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3437" Name="FR_PrimaryBeneficiaryGender" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18466" Name="FR_PrimaryBeneficiary_StreetAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18467" Name="FR_PrimaryBeneficiary_City" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18468" Name="FR_PrimaryBeneficiary_State" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18469" Name="FR_PrimaryBeneficiary_zipcode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryContactPhoneNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18486" Name="FR_PrimaryBeneficiary_PhoneNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ContingentBeneficiary" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="BeneficiaryName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3288" Name="FR_ContingentBeneficiaryName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryPercentage" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3289" Name="FR_ContingentBeneficiaryPercentage" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiarySSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3290" Name="FR_ContingentBeneficiarySSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryRelationship" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3292" Name="FR_ContingentBeneficiaryRelationship" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryDateOfBirth" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3291" Name="FR_ContingentBeneficiaryBirthDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryGender" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="3442" Name="FR_ContingentBeneficiaryGender" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryStreetAddress" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18470" Name="FR_ContingentBeneficiary_StreetAddress" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryCity" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18471" Name="FR_ContingentBeneficiary_City" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryState" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18472" Name="FR_ContingentBeneficiary_State" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryZipcode" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18473" Name="FR_ContingentBeneficiary_zipcode" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryContactPhoneNumber" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="8380" Name="FR_ContingentBeneficiaryContactPhoneNumber" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Category" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18614" Name="FR_ContingentBeneficiary_Category" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BeneficiaryLDPS" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16132" Name="FR_ContingentBeneficiaryLDPS" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentMiddleName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16155" Name="FR_BAO_DecendentMiddleName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentSSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16157" Name="FR_BAO_DecendentSSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="IraOption" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="15016" Name="FR_BAO_IraOption" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
      <DataArea VarOI="20317" Name="BAO_IRAOption_2" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentPlanType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18563" Name="FR_BAO_Decendent_PlanType" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="InheritedIRAInformation" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="DecendentFirstName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16154" Name="FR_BAO_DecendentFirstName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentLastName" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16156" Name="FR_BAO_DecendentLastName" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentBirthDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16158" Name="FR_BAO_DecendentBirthDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="DecendentDeathDate" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16159" Name="FR_BAO_DecendentDeathDate" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">yyyy-mm-dd (2001-04-06)</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="InitialTransactions" Ignore="No" NewCustomer="No" NewSection="No"/>
  <XMLLayout Tag="TransferFunds" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16145" Name="FR_BAO_InitialTransactionsTransferFunds" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="VoteProxy" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16642" Name="FR_BAO_VoteProxy" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ConfirmSuppressionStatus" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="18449" Name="FR_BAO_ConfirmSuppressionStatus_Ind" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="BatchTotal" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="762" Name="GO_DR_BATCH_TOTAL" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">General Number</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SSN" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="767" Name="GO_MI_SSN" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ReturnDocumentType" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="1426" Name="GO_MI_RETURN_DOC_TYPE" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Trim Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="ListofForms" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14935" Name="BAO_ListofForms" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="Extension" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16137" Name="FR_AccountOwnerExtension" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="HomePhone" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14973" Name="FR_AccountOwnerHomePhone" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="HomeExtension" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="16165" Name="FR_AccountOwnerHomeExtension" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
  <XMLLayout Tag="SourceOfIncome" Ignore="No" NewCustomer="No" NewSection="No">
    <DataAreas>
      <DataArea VarOI="14983" Name="FR_BAO_SourceOfIncome" Action="Leave As Is">
        <Format Area="Tag Value" Start="0" Length="0" ImpliedDigits="0">Keep Blanks</Format>
      </DataArea>
    </DataAreas>
  </XMLLayout>
</Layouts>
