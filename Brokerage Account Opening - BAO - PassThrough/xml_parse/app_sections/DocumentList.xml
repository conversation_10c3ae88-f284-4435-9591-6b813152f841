<?xml version='1.0' encoding='UTF-8'?>
<DocumentList>
  <Document DocOI="6013" Name="FR_FORM_BAO - No Orchestration ID" Version="3" RestartPageNum="Yes" RestartDocCount="No">
    <Description>FR_FORM_BAO - No Orchestration ID</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Leave Blank" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="237087"/>
    </Targeting>
    <Pages>
      <Page PageOI="15024">FR_FORM_External PDF - Placeholder Page</Page>
    </Pages>
  </Document>
  <Document DocOI="6015" Name="BAO - Cover Letter" Version="9" RestartPageNum="No" RestartDocCount="No">
    <Description>BAO - Cover Letter</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting/>
    <Pages>
      <Page PageOI="15027">A11929 Letter - Page AFP</Page>
      <Page PageOI="22646">A11929 Back Side Letter - Page AFP</Page>
      <Page PageOI="19547">A14482 - NIGO Expiration Letter - Page AFP</Page>
      <Page PageOI="15026">A14483 Letter - Page AFP</Page>
      <Page PageOI="16648">C11981A Letter</Page>
      <Page PageOI="16813">A14795 Letter</Page>
    </Pages>
  </Document>
  <Document DocOI="6419" Name="F11035 - BAO IRA Application (PassThrough) - barcode rule" Version="11" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11035 - BAO IRA Application - PassThrough</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="376817"/>
    </Targeting>
    <Pages>
      <Page PageOI="16468">IRA Application - F11035 Page1 - PassThrough - barcode rule</Page>
      <Page PageOI="16469">IRA Application - F11035 Page2 - PassThrough  - barcode rule</Page>
      <Page PageOI="16470">IRA Application - F11035 Page3 - PassThrough  - barcode rule</Page>
      <Page PageOI="16471">IRA Application - F11035 Page4 - PassThrough  - barcode rule</Page>
      <Page PageOI="16472">IRA Application - F11035 Page5 - PassThrough  - barcode rule</Page>
      <Page PageOI="23652">IRA Application - F11035 Page6 - Sweep Option - PassThrough  - barcode rule</Page>
      <Page PageOI="16473">IRA Application - F11035 Page7 - PassThrough  - barcode rule</Page>
      <Page PageOI="16474">IRA Application - F11035 Page8 - PassThrough  - barcode rule</Page>
    </Pages>
  </Document>
  <Document DocOI="6421" Name="F11015 - BAO Designation of Beneficiary (PassThrough)" Version="10" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="342094"/>
    </Targeting>
    <Pages>
      <Page PageOI="16484">Designation of Beneficiary - F11015 Page1 - PassThrough</Page>
      <Page PageOI="16485">Designation of Beneficiary - F11015 Page2 - PassThrough</Page>
      <Page PageOI="16486">Designation of Beneficiary - F11015 Page3 - PassThrough</Page>
      <Page PageOI="23014">Designation of Beneficiary - F11015 Page3_B - PassThrough</Page>
      <Page PageOI="16487">Designation of Beneficiary - F11015 Page4 - PassThrough</Page>
      <Page PageOI="19225">Designation of Beneficiary - F11015 Page5 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="6431" Name="F11208 - BAO IRA Application (PassThrough)" Version="7" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11208 - BAO IRA Application</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="342527"/>
    </Targeting>
    <Pages>
      <Page PageOI="16569">IRA Application - F11208 Page1 - PassThrough</Page>
      <Page PageOI="16570">IRA Application - F11208 Page2 - PassThrough</Page>
      <Page PageOI="16571">IRA Application - F11208 Page3 - PassThrough</Page>
      <Page PageOI="16572">IRA Application - F11208 Page4 - PassThrough</Page>
      <Page PageOI="16573">IRA Application - F11208 Page5 - PassThrough</Page>
      <Page PageOI="16574">IRA Application - F11208 Page6 - PassThrough</Page>
      <Page PageOI="16575">IRA Application - F11208 Page7 - PassThrough</Page>
      <Page PageOI="16576">IRA Application - F11208 Page8 - PassThrough</Page>
      <Page PageOI="16577">IRA Application - F11208 Page9 - PassThrough</Page>
      <Page PageOI="16578">IRA Application - F11208 Page10 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="6434" Name="F11207 - BAO IRA Application (PassThrough)" Version="5" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11207 - BAO IRA Application</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="232830"/>
    </Targeting>
    <Pages>
      <Page PageOI="16582">IRA Application - F11207 Page1 - PassThrough</Page>
      <Page PageOI="16583">IRA Application - F11207 Page2 - PassThrough</Page>
      <Page PageOI="16584">IRA Application - F11207 Page3 - PassThrough</Page>
      <Page PageOI="16749">IRA Application - F11207 Page4 - PassThrough</Page>
      <Page PageOI="16585">IRA Application - F11207 Page5 - PassThrough</Page>
      <Page PageOI="16586">IRA Application - F11207 Page6 - PassThrough</Page>
      <Page PageOI="16587">IRA Application - F11207 Page7 - PassThrough</Page>
      <Page PageOI="16588">IRA Application - F11207 Page8 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="6443" Name="Proposal &amp; IPQ (PassThrough)" Version="2" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="237086"/>
    </Targeting>
    <Pages>
      <Page PageOI="16649">Proposal &amp; IPQ - Passthrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7000" Name="F11143 - BAO IRA Application (PassThrough)" Version="6" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11143 - BAO IRA Application - PassThrough</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="No" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="376656"/>
    </Targeting>
    <Pages>
      <Page PageOI="18536">IRA Application - F11143 Page1 - PassThrough</Page>
      <Page PageOI="18537">IRA Application - F11143 Page2 - PassThrough</Page>
      <Page PageOI="23651">IRA Application - F11143 Page2.1- Sweep Option - PassThrough</Page>
      <Page PageOI="18538">IRA Application - F11143 Page3 - PassThrough</Page>
      <Page PageOI="18552">IRA Application - F11143 Page 4 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7074" Name="FR_FORM_BAO - No Orchestration ID_Landscape" Version="4" RestartPageNum="Yes" RestartDocCount="No">
    <Description>FR_FORM_BAO - No Orchestration ID</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Leave Blank" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="345067"/>
    </Targeting>
    <Pages>
      <Page PageOI="18866">FR_FORM_External PDF - Placeholder Page_2</Page>
    </Pages>
  </Document>
  <Document DocOI="7084" Name="BAO - Cover Letter-2" Version="2" RestartPageNum="No" RestartDocCount="No">
    <Description>BAO - Cover Letter so the next doc can start on the back of this</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="Yes"/>
    </Composition>
    <Targeting/>
    <Pages>
      <Page PageOI="19357">IRA Cover Page_BOB</Page>
    </Pages>
  </Document>
  <Document DocOI="7177" Name="BAO - Cover Letter PDF" Version="1" RestartPageNum="No" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="218432"/>
    </Targeting>
    <Pages>
      <Page PageOI="19019">A14823 Letter</Page>
    </Pages>
  </Document>
  <Document DocOI="7230" Name="F11004 - BAO IRA Application (PassThrough)" Version="2" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11004 - TODI Form</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="248650"/>
    </Targeting>
    <Pages>
      <Page PageOI="19189">IRA Application - F11004 Page1 - PassThrough</Page>
      <Page PageOI="19191">IRA Application - F11004 Page2 - PassThrough</Page>
      <Page PageOI="19192">IRA Application - F11004 Page3 - PassThrough</Page>
      <Page PageOI="19193">IRA Application - F11004 Page4 - PassThrough</Page>
      <Page PageOI="19194">IRA Application - F11004 Page5 - PassThrough</Page>
      <Page PageOI="19503">IRA Application - F11004 Page6 - PassThrough</Page>
      <Page PageOI="19504">IRA Application - F11004 Page7 - PassThrough</Page>
      <Page PageOI="19195">IRA Application - F11004 Page8 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7247" Name="F11007 - BAO IRA Application (PassThrough)" Version="3" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11007 - TODJ Form</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="248998"/>
    </Targeting>
    <Pages>
      <Page PageOI="19233">IRA Application - F11007 Page1 - PassThrough</Page>
      <Page PageOI="19234">IRA Application - F11007 Page2 - PassThrough</Page>
      <Page PageOI="19235">IRA Application - F11007 Page3 - PassThrough</Page>
      <Page PageOI="19510">IRA Application - F11007 Page4 - PassThrough</Page>
      <Page PageOI="19511">IRA Application - F11007 Page5 - PassThrough</Page>
      <Page PageOI="19512">IRA Application - F11007 Page6 - PassThrough</Page>
      <Page PageOI="19513">IRA Application - F11007 Page7 - PassThrough</Page>
      <Page PageOI="19514">IRA Application - F11007 Page8 - PassThrough</Page>
      <Page PageOI="19515">IRA Application - F11007 Page9 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7248" Name="F11015 (BAO MANAGED V2)- Designation of Beneficiary (PassThrough)" Version="4" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="341061"/>
    </Targeting>
    <Pages>
      <Page PageOI="19328">Designation of Beneficiary - F11015 Page1(BAO MANAGED V2-) - PassThrough</Page>
      <Page PageOI="19239">Designation of Beneficiary - F11015 Page2(BAO MANAGED V2) - PassThrough</Page>
      <Page PageOI="19240">Designation of Beneficiary - F11015 Page3(BAO MANAGED V2) - PassThrough</Page>
      <Page PageOI="23011">Designation of Beneficiary - F11015 Page3_B(BAO MANAGED V2) - PassThrough</Page>
      <Page PageOI="19241">Designation of Beneficiary - F11015 Page4(BAO MANAGED V2) - PassThrough</Page>
      <Page PageOI="19242">Designation of Beneficiary - F11015 Page5(BAO MANAGED V2) - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7253" Name="F40334 (BAO MANAGED V2)- TIAA Protfolio Advisor Inherited Trust Application (PassThrough)" Version="5" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F40334</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="342530"/>
    </Targeting>
    <Pages>
      <Page PageOI="19285">Inherited Trust Application - F40334 Page1 - PassThrough</Page>
      <Page PageOI="19286">Inherited Trust Application - F40334 Page2 - PassThrough</Page>
      <Page PageOI="19287">Inherited Trust Application - F40334 Page3 - PassThrough</Page>
      <Page PageOI="19290">Inherited Trust Application - F40334 Page4 - PassThrough</Page>
      <Page PageOI="19291">Inherited Trust Application - F40334 Page5 - PassThrough</Page>
      <Page PageOI="19292">Inherited Trust Application - F40334 Page6 - PassThrough</Page>
      <Page PageOI="19293">Inherited Trust Application - F40334 Page7 - PassThrough</Page>
      <Page PageOI="19294">Inherited Trust Application - F40334 Page8 - PassThrough</Page>
      <Page PageOI="19295">Inherited Trust Application - F40334 Page9 - PassThrough</Page>
      <Page PageOI="19302">Inherited Trust Application - F40334 Page13 - PassThrough</Page>
      <Page PageOI="19300">Inherited Trust Application - F40334 Page15 - PassThrough</Page>
      <Page PageOI="19301">Inherited Trust Application - F40334 Page16 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7254" Name="F40285 (BAO MANAGED V2)- TIAA Protfolio Advisor Trust Account Application (PassThrough)" Version="5" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F40285</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="345265"/>
    </Targeting>
    <Pages>
      <Page PageOI="19303">Portfolio AdvisorTrust Account Application - F40285 Page1 - PassThrough</Page>
      <Page PageOI="19304">Portfolio AdvisorTrust Account Application - F40285 Page2 - PassThrough</Page>
      <Page PageOI="19305">Portfolio AdvisorTrust Account Application - F40285 Page3 - PassThrough</Page>
      <Page PageOI="19307">Portfolio AdvisorTrust Account Application - F40285 Page4 - PassThrough</Page>
      <Page PageOI="19308">Portfolio AdvisorTrust Account Application - F40285 Page5 - PassThrough</Page>
      <Page PageOI="19309">Portfolio AdvisorTrust Account Application - F40285 Page6 - PassThrough</Page>
      <Page PageOI="19310">Portfolio AdvisorTrust Account Application - F40285 Page7 - PassThrough</Page>
      <Page PageOI="19386">Portfolio AdvisorTrust Account Application - F40285 Page 8 - PassThrough</Page>
      <Page PageOI="19311">Portfolio AdvisorTrust Account Application - F40285 Page 9 - PassThrough</Page>
      <Page PageOI="19312">Portfolio AdvisorTrust Account Application - F40285 Page 10- PassThrough</Page>
      <Page PageOI="19313">Portfolio AdvisorTrust Account Application - F40285 Page 11 - PassThrough</Page>
      <Page PageOI="19314">Portfolio AdvisorTrust Account Application - F40285 Page 12 - PassThrough</Page>
      <Page PageOI="19315">Portfolio AdvisorTrust Account Application - F40285 Page 13 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7523" Name="F11208 (BAO MANAGED V3)- BAO IRA Application (PassThrough)" Version="3" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11208 - BAO IRA Application</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="342528"/>
    </Targeting>
    <Pages>
      <Page PageOI="20042">IRA Application - F11208 Page1(BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20052">IRA Application - F11208 Page2(BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20053">IRA Application - F11208 Page3(BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20043">IRA Application - F11208 Page4(BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20044">IRA Application - F11208 Page5(BAO MANAGED V3) -  PassThrough</Page>
      <Page PageOI="20045">IRA Application - F11208 Page6(BAO MANAGED V3) -  PassThrough</Page>
      <Page PageOI="20054">IRA Application - F11208 Page7(BAO MANAGED V3) -  PassThrough</Page>
      <Page PageOI="20055">IRA Application - F11208 Page8(BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20035">IRA Application - F11208 Page9(BAO MANAGED V3) - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7531" Name="BankSweep Terms and Conditions" Version="3" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377241"/>
    </Targeting>
    <Pages>
      <Page PageOI="20063">BankSweep TC Passthrough Page</Page>
    </Pages>
  </Document>
  <Document DocOI="7532" Name="Liquid Insured Deposit Terms and Conditions" Version="3" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377239"/>
    </Targeting>
    <Pages>
      <Page PageOI="20064">Liquid Insured TC Passthrough Page</Page>
    </Pages>
  </Document>
  <Document DocOI="7533" Name="F11207 (BAO MANAGED V3)- BAO IRA Application (PassThrough)" Version="2" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11207 - BAO IRA Application</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="345294"/>
    </Targeting>
    <Pages>
      <Page PageOI="20067">IRA Application - F11207 Page1 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20068">IRA Application - F11207 Page2 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20069">IRA Application - F11207 Page3 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20070">IRA Application - F11207 Page4 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20071">IRA Application - F11207 Page5 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20072">IRA Application - F11207 Page6 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20073">IRA Application - F11207 Page7 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20074">IRA Application - F11207 Page8 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20075">IRA Application - F11207 Page9 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20077">IRA Application - F11207 Page10 (BAO MANAGED V3) - PassThrough</Page>
      <Page PageOI="20078">IRA Application - F11207  Last Page (BAO MANAGED V3) - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7801" Name="F11543 - BAO IRA Application (PassThrough) - barcode rule" Version="3" RestartPageNum="Yes" RestartDocCount="No">
    <Description>F11543 - BAO IRA Application - PassThrough - NO Barcode</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="377270"/>
    </Targeting>
    <Pages>
      <Page PageOI="22831">IRA Application - F11543 Page1 - PassThrough - barcode rule</Page>
      <Page PageOI="22832">IRA Application - F11543 Page2 - PassThrough - barcode rule</Page>
      <Page PageOI="23654">IRA Application - F11543 Page3 - Sweeep Option - PassThrough - barcode rule</Page>
      <Page PageOI="22833">IRA Application - F11543 Page4 - PassThrough - barcode rule</Page>
      <Page PageOI="22834">IRA Application - F11543 Page5 - PassThrough - barcode rule</Page>
      <Page PageOI="22835">IRA Application - F11543 Page6 - PassThrough - barcode rule</Page>
      <Page PageOI="22836">IRA Application - F11543 Page7 -- PassThrough_PrevVersion - barcode rule</Page>
      <Page PageOI="22837">IRA Application - F11543 Page8 - PassThrough - barcode rule</Page>
      <Page PageOI="22838">IRA Application - F11543 Page9 - PassThrough - barcode rule</Page>
      <Page PageOI="23008">IRA Application - F11543 Page10 - PassThrough - barcode rule</Page>
    </Pages>
  </Document>
  <Document DocOI="7802" Name="F11071 - BAO IRA Application (PassThrough)" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description>Self-directed Brokerage Migration to Model A - Form F11071</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="No" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="331062"/>
    </Targeting>
    <Pages>
      <Page PageOI="22842">IRA Application - F11071 Page1 - PassThrough - PREFILL</Page>
      <Page PageOI="22843">IRA Application - F11071 Page2 - PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="7847" Name="F40031 - BAO Inherited IRA Application (PassThrough)_Trust Version" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description>TLENGDMP-5761</Description>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting DependsOnSection="ApplicationForm">
      <Rule RuleOI="338805"/>
    </Targeting>
    <Pages>
      <Page PageOI="22996">Inherited IRA Application - F40031 Page1_Trust - PassThrough</Page>
      <Page PageOI="22997">Inherited IRA Application - F40031 Page2_Trust- PassThrough</Page>
      <Page PageOI="23006">Inherited IRA Application - F40031 Page3 Trust- PassThrough</Page>
      <Page PageOI="22999">Inherited IRA Application - F40031 Page4 Trust - PassThrough</Page>
      <Page PageOI="23000">Inherited IRA Application - F40031 Page5 Trust - PassThrough</Page>
      <Page PageOI="23001">Inherited IRA Application - F40031 Page6 Trust - PassThrough</Page>
      <Page PageOI="23002">Inherited IRA Application - F40031 Page7_Trust- PassThrough</Page>
      <Page PageOI="23005">Inherited IRA Application - F40031 Page9 Trust- PassThrough</Page>
    </Pages>
  </Document>
  <Document DocOI="8077" Name="BrokerageSweep Terms and Conditions D_G_C_M_I-DGVXX" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377240"/>
    </Targeting>
    <Pages>
      <Page PageOI="23653">BrokerageSweep TC Passthrough Page</Page>
    </Pages>
  </Document>
  <Document DocOI="8078" Name="BrokerageSweep Terms and Conditions  D_G_C_M_S_S-DGUXX" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377242"/>
    </Targeting>
    <Pages>
      <Page PageOI="23653">BrokerageSweep TC Passthrough Page</Page>
    </Pages>
  </Document>
  <Document DocOI="8079" Name="BrokerageSweep Terms and Conditions  D_G_S_C_M_I-DVPXX" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377243"/>
    </Targeting>
    <Pages>
      <Page PageOI="23653">BrokerageSweep TC Passthrough Page</Page>
    </Pages>
  </Document>
  <Document DocOI="8080" Name="BrokerageSweep Terms and Conditions  F_H_G_O_C_II-GFYXX" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377244"/>
    </Targeting>
    <Pages>
      <Page PageOI="23653">BrokerageSweep TC Passthrough Page</Page>
    </Pages>
  </Document>
  <Document DocOI="8081" Name="BrokerageSweep Terms and Conditions  F_H_T_U_T_O_C_II_F-TTIXX" Version="1" RestartPageNum="Yes" RestartDocCount="No">
    <Description/>
    <Composition Float="No" FillStyle="Sequential" LimitMsgPages="9999">
      <TableOfContents DocFormat="None" PageFormat="1,2,3"/>
      <DuplexPrint EmptyBacks="Add Marketing" DocOnFront="Yes" NextDocOnBack="No"/>
    </Composition>
    <Targeting>
      <Rule RuleOI="377245"/>
    </Targeting>
    <Pages>
      <Page PageOI="23653">BrokerageSweep TC Passthrough Page</Page>
    </Pages>
  </Document>
</DocumentList>
