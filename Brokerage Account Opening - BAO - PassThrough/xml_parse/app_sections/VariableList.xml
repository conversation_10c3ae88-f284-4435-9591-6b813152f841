<?xml version='1.0' encoding='UTF-8'?>
<VariableList>
  <Variable VarOI="22" Name="SYS_LanguageCustomer" Type="String" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The name of the current customer's language. This is used to pick the correct language-version of pages and messages and to obtain names of days and months.</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="23" Name="SYS_LocaleCustomer" Type="String" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The name of the current customer's locale.  This is used to determine the type of formatting for numeric, currency, and dates.</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="17" Name="SYS_CustomerEffectiveDate" Type="Date" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The as-of date that documents should be created for this customer (used only for multi-version packages).</Description>
    <Sample>December 18, 2007</Sample>
  </Variable>
  <Variable VarOI="26" Name="SYS_CustInvalidDataLevel" Type="Integer" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The maximum invalid data level for the current customer, normally based on the action set in the variable's validation:  0= no invalid data, 1=continue (set default), 2=error, 10=skip customer.</Description>
    <Sample>11</Sample>
  </Variable>
  <Variable VarOI="21814" Name="BAO_Captiva_2D_Barcode_flag" Type="String" Source="User value" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="19463" Name="FR_00_EXT_PDFFiles_MasterFormula_LiquidInsure_TC_SD" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">
// Resize(FR_BAO_PassThrough_LiquidInsurance_TC_SD,1)
FR_BAO_PassThrough_LiquidInsurance_TC_SD(1) = Trim(ECS_01_OutputPathforResources) &amp; "LIDStc.pdf"
</Formula>
  </Variable>
  <Variable VarOI="19459" Name="FR_00_EXT_PDFFiles_MasterFormula_LiquidInsure_TC" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">
Resize(FR_BAO_PassThrough_LiquidInsurance_TC,1)
FR_BAO_PassThrough_LiquidInsurance_TC(1) = Trim(ECS_01_OutputPathforResources) &amp; "LiquidInsuredTC.pdf"
</Formula>
  </Variable>
  <Variable VarOI="21792" Name="FR_00_EXT_PDFFiles_MasterFormula_BrokerageSweep_TC" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">Resize(FR_BAO_PassThrough_BrokerageSweep_TC_DGUXX,1)
Resize(FR_BAO_PassThrough_BrokerageSweep_TC_DVPXX,1)
Resize(FR_BAO_PassThrough_BrokerageSweep_TC_DGVXX,1)
Resize(FR_BAO_PassThrough_BrokerageSweep_TC_GFYXX,1)
Resize(FR_BAO_PassThrough_BrokerageSweep_TC_TTIXX,1)


FR_BAO_PassThrough_BrokerageSweep_TC_DGVXX(1) = Trim(ECS_01_OutputPathforResources) &amp; "D_G_C_M_I-DGVXX.pdf"

FR_BAO_PassThrough_BrokerageSweep_TC_DGUXX(1) = Trim(ECS_01_OutputPathforResources) &amp; "D_G_C_M_S_S-DGUXX.pdf"

FR_BAO_PassThrough_BrokerageSweep_TC_DVPXX(1) = Trim(ECS_01_OutputPathforResources) &amp; "D_G_S_C_M_I-DVPXX.pdf"

FR_BAO_PassThrough_BrokerageSweep_TC_GFYXX(1) = Trim(ECS_01_OutputPathforResources) &amp; "F_H_G_O_C_II-GFYXX.pdf"

FR_BAO_PassThrough_BrokerageSweep_TC_TTIXX(1) = Trim(ECS_01_OutputPathforResources) &amp; "F_H_T_U_T_O_C_II_F-TTIXX.pdf"

</Formula>
  </Variable>
  <Variable VarOI="19458" Name="FR_00_EXT_PDFFiles_MasterFormula_BankSweep_TC" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">
Resize(FR_BAO_PassThrough_BankSweep_TC,1)
FR_BAO_PassThrough_BankSweep_TC(1) = Trim(ECS_01_OutputPathforResources) &amp; "Banksweeptc.pdf"
</Formula>
  </Variable>
  <Variable VarOI="16031" Name="FR_00_EXT_PDFFiles_MasterFormula" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_00_EXT_PDFFiles_MasterFormula_rjk</Description>
    <Sample/>
    <Formula Compute="Named section(Don't Compute)">IF Instr(1,"A14175,A11456,A14174,F11032,F11418,ADV123,A12440,TD12345,A40008,A10813",FR_BAO_FormNumber)&lt;&gt; 0 THEN
   FR_01_BAO_EXT_PDFFiles_PLACEHOLDER = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; ".pdf"
EndIf

IF Instr(1,"F11015,F11035,F11143,F11543,F11004,F11007,F11071,F40031,F40285,F40334",FR_BAO_FormNumber)&lt;&gt; 0 THEN
   FR_BAO_PassThrough_Test = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; ".pdf"
Endif

IF Instr(1,"F11208",FR_BAO_FormNumber)&lt;&gt; 0 THEN
	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN
		FR_BAO_PassThrough_Test = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; "_BAOMANAGEDV3.pdf"
	ELSE
		FR_BAO_PassThrough_Test = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; ".pdf"
	ENDIF	
ENDIF

IF Instr(1,"F11207",FR_BAO_FormNumber)&lt;&gt; 0 THEN
	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN
		FR_BAO_PassThrough_Test = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; "_BAOMANAGEDV3.pdf"
	ELSE
		FR_BAO_PassThrough_Test = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; ".pdf"
	ENDIF	
ENDIF	


IF Instr(1,"CITIODD",FR_BAO_FormNumber)&lt;&gt; 0 THEN
   FR_BAO_CitiPassThrough_Test = GO_01_OutputPathforEXTFILES &amp; COMMON.GO_MI_PST_RQST_ID &amp; ".pdf"
Endif 

IF Instr(1,"A10605,P0511225",FR_BAO_FormNumber)&lt;&gt; 0 THEN
   FR_01_BAO_EXT_PDFFiles_PLACEHOLDER_Landscape = ECS_01_OutputPathforResources &amp; FR_BAO_FormNumber &amp; ".pdf"
EndIf 

//  4/28/2017  Add Buckslip to the end of every BAO Doc per Maggie NWI ************

FR_01_BAO_EXT_BUCKSLIP_PDFFiles_PLACEHOLDER = ECS_01_OutputPathforResources &amp; "BAO_BUCKSLIP.pdf"
</Formula>
  </Variable>
  <Variable VarOI="1447" Name="GO_00_GLOBAL_AuditReport_FileMappings" Type="String" Source="Formula" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Engine Level GLOBAL  Formula for dynamically setting all  AUDITREPORT file mappings/ file names.</Description>
    <Sample/>
    <Formula Compute="Initialization">DIM iReturnCode AS Integer
DIM iFileNameLength AS Integer
DIM sSystemFileName AS String
DIM sFileMapName AS String
DIM sFileName AS String
DIM sQueueFlag AS String
DIM iA AS Integer
DIM iMsgNbr AS Integer
IF GO_01_DCS_ReqType &lt;&gt; "DWS" THEN
   iFileNameLength = Len(GO_AR_FILE_NAME)
   iMsgNbr = 9999
   FOR iA = 1 TO 4
      sQueueFlag = ""
      sSystemFileName = ""
      sFileMapName = ""
      sFileName = ""
      SELECT CASE iA
         CASE 1
            sQueueFlag = GO_01_PS_QUEUE_Flag
            sSystemFileName = ECS_01_OutputPathforPS &amp; GO_AR_FILE_NAME &amp; ".txt"
			If SYS_PostSort = TRUE Then
				sFileMapName = "GO_Audit_Report_File_TEXT - Post Sort"
			Else
				sFileMapName = "GO_Audit_Report_File_TEXT"
			EndIf
            sFileName = "General AUDITREPORT"
         CASE 2
            sQueueFlag = GO_01_EDELPDF_QUEUE_Flag
            sSystemFileName = ECS_01_OutputPathforPDF &amp; Left(GO_AR_FILE_NAME, iFileNameLength - 1) &amp; "A.txt"
            sFileMapName = "GO_Audit_Report_File_TEXT"
            sFileName = "General AUDITREPORT (E-Delivery)"
         CASE 3
            sQueueFlag = GO_01_TIFF_QUEUE_Flag
            sSystemFileName = ECS_01_OutputPathforTIFF &amp; Left(GO_AR_FILE_NAME, iFileNameLength - 1) &amp; "I.txt"
            sFileMapName = "GO_Audit_Report_File_TIFF"
            sFileName = "TIFF AUDITREPORT"
         CASE 4
            sQueueFlag = GO_01_ARCHPDF_QUEUE_Flag
            sSystemFileName = ECS_01_OutputPathforPDF &amp; Left(GO_AR_FILE_NAME, iFileNameLength - 1) &amp; "A.txt"
            sFileMapName = "GO_Audit_Report_File_ARCH"
            sFileName = "ARCH AUDITREPORT"
      END SELECT
	  If iA &lt;&gt; 1 and SYS_PostSort = True Then
		sQueueFlag = "0"
	  EndIf
      IF sQueueFlag &lt;&gt; "0" and IsNull(sQueueFlag) = False THEN
         iReturnCode = FileMap(sFileMapName, sSystemFileName)
         iMsgNbr = iMsgNbr - 1
         IF iReturnCode &lt;&gt; 0 THEN
            Message(iMsgNbr, "S", "Unable to Initialze " &amp; sFileName &amp; " file.  FileSystemFileName = " &amp; sSystemFileName &amp; ".  Return Code = " &amp; iReturnCode)
         ELSE
            Message(iMsgNbr, "I", "Successfully Initialized " &amp; sFileName &amp; " file.  FileSystemFileName = " &amp; sSystemFileName &amp; ".")
         ENDIF
      ENDIF
   NEXT iA
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="795" Name="GO_DDA_Master1" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>Customer Level DDA variable for tracking missing documents. Posts BEGIN tag.</Description>
    <Sample/>
    <Formula Compute="Customer(Post-customer)">DIM RC AS Integer
GO_DDA_Customer_Track_Variable =  'BEGIN:'  &amp; GO_DDA_DOC_REQ_ID
IF SYS_PostSort = True THEN
   RC = TRIGGER("GO_DDA_Customer_Track_PostSort")
ELSE
   RC = TRIGGER("GO_DDA_Customer_Track")
ENDIF
IF RC &lt;&gt; 0 THEN
   Message(  9999 ,'S', "Unable to write into Customer Tracking File/DDA. Document Request ID at the time of abend = " &amp; GO_DDA_DOC_REQ_ID &amp;  ".  Trigger RC =  " &amp; RC)
ENDIF
VALUE = ''

</Formula>
  </Variable>
  <Variable VarOI="796" Name="GO_DDA_Master2" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>Customer Level DDA variable for tracking missing documents. Posts  END  tag.</Description>
    <Sample/>
    <Formula Compute="Post-customer">DIM RC AS Integer
GO_DDA_Customer_Track_Variable =  'END  :'  &amp; GO_DDA_DOC_REQ_ID
IF SYS_PostSort = True THEN
   RC = TRIGGER("GO_DDA_Customer_Track_PostSort")
ELSE
   RC = TRIGGER("GO_DDA_Customer_Track")
ENDIF
IF RC &lt;&gt; 0 THEN
   Message(  9999 ,'S', "Unable to write into Customer Tracking File/DDA. Document Request ID at the time of abend = " &amp; GO_DDA_DOC_REQ_ID &amp; ". RC = " &amp; RC)
ENDIF
VALUE = ''

</Formula>
  </Variable>
  <Variable VarOI="1432" Name="GO_00_Application_MASTER_FORMULA" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>Customer level GLOBL formula for setting filenames &amp; queue selections.</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">
/********************************************************************/
/***** MASTER FORMULA for Queues and File Naming  *******************/
/***   Executed for each Customer in driver file  *******************/
/***   1. Sets One Filenames                      *******************/
/***   2. Sets Individual Filenames               *******************/
/***   3. Selects TIFF queue based on Gloabl EXPAG Flag *************/ 
/********************************************************************/

/*  Manju:  Onefilename now comes from GO_AR_FILE_NAME field from control file */


/*****  One-Filename assignments  TK: Internal Archive file will end with 'W' Archive files and MOB eDelivery files will end with 'M' *****/

ECS_01_OneFileNamePS      = ECS_01_OutputPathforPS   &amp; GO_AR_FILE_NAME &amp; ".ps"
ECS_01_OneFileNamePDF     = ECS_01_OutputPathforPDF  &amp; GO_AR_FILE_NAME &amp; ".pdf"

/*****  One-Filename assignments  TK: Internal Archive files for Mobius will end with 'W' *****/

IF GO_MI_ARCHIVAL_IND = "I" THEN
ECS_01_OneFileNameArchPDF = ECS_01_OutputPathforPDF  &amp; GO_AR_FILE_NAME_DELTYPE  &amp; "W" &amp; ".pdf"
ELSE
ECS_01_OneFileNameEdelPDF = ECS_01_OutputPathforPDF  &amp; GO_AR_FILE_NAME_DELTYPE  &amp; "M" &amp; ".pdf"
ECS_01_OneFileNameArchPDF = ECS_01_OutputPathforPDF  &amp; GO_AR_FILE_NAME_DELTYPE  &amp; "M" &amp; ".pdf"
ENDIF


ECS_01_OneFileNameTIFF    = ECS_01_OutputPathforTIFF &amp; GO_AR_FILE_NAME &amp; ".tif"

/*****  Individual-Filename assignments *****/

ECS_01_IndivFileNamePS   = ECS_01_OutputPathforPS   &amp; GO_MI_PST_RQST_ID  &amp; ".ps"
ECS_01_IndivFileNamePDF  = ECS_01_OutputPathforPDF  &amp; GO_MI_PST_RQST_ID  &amp; ".pdf"

IF GO_MI_ARCHIVAL_IND = "I" THEN
ECS_01_IndivFileNameArchPDF = ECS_01_OutputPathforPDF  &amp; GO_MI_PST_RQST_ID  &amp; ".pdf"
ELSE
ECS_01_IndivFileNameEdelPDF = ECS_01_OutputPathforPDF  &amp; GO_MI_PST_RQST_ID  &amp; ".pdf"
ECS_01_IndivFileNameArchPDF = ECS_01_OutputPathforPDF  &amp; GO_MI_PST_RQST_ID  &amp; ".pdf"
ENDIF


ECS_01_IndivFileNameTIFF = ECS_01_OutputPathforTIFF &amp; GO_MI_PST_RQST_ID  &amp; ".tif"


/*******************************************************************************/
/*****  Customer level TIFF Queue selection based upon EXP AG Indicator ********/
/*****  For all applications in DCS, tag &lt;EXPAGInd&gt; decides if the document ****/
/*****  goes into TIFF queue or not. This is non-Application specific and   ****/
/*****  hence kept in a common place here to make it global  for all Apps.  ****/
/*******************************************************************************/

IF  Ucase(GO_MI_PCKGE_IMAGE_IND)  &lt;&gt; "Y" THEN 
     GO_01_TIFF_Queue_Application_Flag = "N"
ELSE
     GO_01_TIFF_Queue_Application_Flag = "Y"
     GO_00_GLOBAL_TIFF_AuditReport_SeqNo = GO_00_GLOBAL_TIFF_AuditReport_SeqNo + 1
ENDIF



/*******************************************************************************/
/*****  Customer level ARCH Queue selection based upon Delivery Type ***********/
/*****  Archive Indicator. This is non-Application specific and ****************/
/*****  hence kept in a common place here to make it global  for all Apps. *****/
/*******************************************************************************/

IF GO_MI_ARCHIVAL_IND = "N" THEN
   GO_01_PDF_Arch_Queue_Application_Flag = "N"  
ELSE
   GO_01_PDF_Arch_Queue_Application_Flag = "Y"
   GO_00_GLOBAL_ARCH_AuditReport_SeqNo = GO_00_GLOBAL_ARCH_AuditReport_SeqNo + 1
ENDIF



/*******************************************************************************/
/*****  Customer level EDELIVERY Queue selection based upon Delivery Type ***********/
/*****  Archive Indicator. This is non-Application specific and ****************/
/*****  hence kept in a common place here to make it global  for all Apps. *****/
/*******************************************************************************/

IF (GO_MI_PCKGE_DLVRY_TYP = "E" OR GO_MI_PCKGE_DLVRY_TYP = "EMAIL") THEN 
   GO_01_PDF_Edel_Queue_Application_Flag = "Y"
ELSE 
   GO_01_PDF_Edel_Queue_Application_Flag = "N"
ENDIF
/*******************************************************************************/
</Formula>
  </Variable>
  <Variable VarOI="804" Name="GO_00_GLOBAL_Logo_Placeholder_NO_Address_TIFF" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>NO ADDRESS TIFF:  Customer Level Global MASTER Variable for calculating  TIFF Image placeholder variable value.  Multiple numbered vars are created for use in applications where more than one type of logo is used in same document. See for eg:  ACS</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">//GLOBAL FORMULA FOR SHARED CSET AND CSF "Stuff"
//
//Logo located on the shared drive in GO_EXSTREAM/RESOURCES
//
//NO Logo Address is in Position 1 of the Array
IF GO_00_GLOBAL_RB_COMPANY &lt;&gt;"TIAA" then 
GO_02_LogoPlaceholder_TIF = 
ECS_01_OutputPathforResources &amp; 
GO_01_Init_Image_Library_Array_TIFF(1)
else
GO_02_LogoPlaceholder_TIF = 
ECS_01_OutputPathforResources &amp; 
GO_01_Init_Image_Library_Array_TIFF(10)
endif
</Formula>
  </Variable>
  <Variable VarOI="17725" Name="GO_00_GLOBAL_Logo_Placeholder_NO_Address_Master_Formula_IRA" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_00_GLOBAL_Logo_Placeholder_NO_Address_Master_Formula</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">//GLOBAL FORMULA FOR SHARED CSET AND CSF "Stuff"
//
//Logo located on the shared drive in GO_EXSTREAM/RESOURCES
//
//NO Logo Address is in Position 5 of the Array
IF GO_00_GLOBAL_RB_COMPANY &lt;&gt;"TIAA" then 
GO_02_LogoPlaceholder_JPG = 
ECS_01_OutputPathforResources &amp; 
GO_01_Init_Image_Library_Array(5)
else
GO_02_LogoPlaceholder_JPG = 
ECS_01_OutputPathforResources &amp; 
"tiaa_logo_h_black_tint_RGB_resized_IRA.jpg"
endif


// Non rebranded logo for eApp Confimration :
FR_02_LogoPlaceholder_JPG_IRA_eApp = 
ECS_01_OutputPathforResources &amp; GO_01_Init_Image_Library_Array(5) 


IF ((FR_LetterType = "A14482") OR (FR_LetterType = "A14483")) THEN
	GO_02_LogoPlaceholder_JPG = ECS_01_OutputPathforResources &amp; GO_01_Init_Image_Library_Array(15)
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1434" Name="GO_00_GLOBAL_Queue_Selections" Type="String" Source="Formula" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Engine Level Global formula for selecting various queues based on script file settings</Description>
    <Sample/>
    <Formula Compute="Initialization">/******* Script File sets values for GO_01_**_QUEUE_Flag       *****/
/*******  DCS sets which queues to be used in the control file *****/
/*******  This Formula Variable sets queue selections at the   *****/
/*******  beginning of the engine run.                         *****/
/*******  Global QUEUE Selections based on         *****************/
/*******  following arrangement                    *****************/
/*******     0 = Queue Disabled                    *****************/
/*******     1 = One File will have ALL documents in the run   *****/
/*******     2 = Individual Files for each Customer ****************/
/*******     3 = Ind. File per Customer PLUS One file for all ******/


/************************************************************************/
/****  Following code applies to Engine level global selection      *****/
/****  TK: Added MOB PDF Archive Queue Flag                         *****/
/************************************************************************/


SELECT CASE  GO_01_PS_QUEUE_Flag
  CASE "0"
	GO_01_GroupPS_Flag = "N"
	GO_01_IndivPS_Flag = "N"
  CASE "1"
	GO_01_GroupPS_Flag = "Y"
	GO_01_IndivPS_Flag = "N"
  CASE "2"
	GO_01_GroupPS_Flag = "N"
	GO_01_IndivPS_Flag = "Y"
  CASE "3"
	GO_01_GroupPS_Flag = "Y"
	GO_01_IndivPS_Flag = "Y"
END SELECT


SELECT CASE  GO_01_PDF_QUEUE_Flag
  CASE "0"
	GO_01_GroupPDF_Flag = "N"
	GO_01_IndivPDF_Flag = "N"
  CASE "1"
	GO_01_GroupPDF_Flag = "Y"
	GO_01_IndivPDF_Flag = "N"
  CASE "2"
	GO_01_GroupPDF_Flag = "N"
	GO_01_IndivPDF_Flag = "Y"
  CASE "3"
	GO_01_GroupPDF_Flag = "Y"
	GO_01_IndivPDF_Flag = "Y"
END SELECT


SELECT CASE  GO_01_ARCHPDF_QUEUE_Flag
  CASE "0"
	GO_01_GroupArchPDF_Flag = "N"
	GO_01_IndivArchPDF_Flag = "N"
  CASE "1"
	GO_01_GroupArchPDF_Flag = "Y"
	GO_01_IndivArchPDF_Flag = "N"
  CASE "2"
	GO_01_GroupArchPDF_Flag = "N"
	GO_01_IndivArchPDF_Flag = "Y"
  CASE "3"
	GO_01_GroupArchPDF_Flag = "Y"
	GO_01_IndivArchPDF_Flag = "Y"
END SELECT

SELECT CASE  GO_01_EDELPDF_QUEUE_Flag
  CASE "0"
	GO_01_GroupEdelPDF_Flag = "N"
	GO_01_IndivEdelPDF_Flag = "N"
  CASE "1"
	GO_01_GroupEdelPDF_Flag = "Y"
	GO_01_IndivEdelPDF_Flag = "N"
  CASE "2"
	GO_01_GroupEdelPDF_Flag = "N"
	GO_01_IndivEdelPDF_Flag = "Y"
  CASE "3"
	GO_01_GroupEdelPDF_Flag = "Y"
	GO_01_IndivEdelPDF_Flag = "Y"
END SELECT


SELECT CASE  GO_01_TIFF_QUEUE_Flag
  CASE "0"
	GO_01_GroupTIFF_Flag = "N"
	GO_01_IndivTIFF_Flag = "N"
  CASE "1"
	GO_01_GroupTIFF_Flag = "Y"
	GO_01_IndivTIFF_Flag = "N"
  CASE "2"
	GO_01_GroupTIFF_Flag = "N"
	GO_01_IndivTIFF_Flag = "Y"
  CASE "3"
	GO_01_GroupTIFF_Flag = "Y"
	GO_01_IndivTIFF_Flag = "Y"
END SELECT


/************************************************************************/
/****  Following code applies to Customer level global selection    *****/
/************************************************************************/

/****  All Queues also have additional selection criteria at the     ****/
/****  Customer level. This formula executes at engine level and     ****/
/****  not for each customer. By default, all Customer level flags   ****/
/***** will be enabled for all queues to enable automatic inclusion. ****/
/****  Wherever required, each Applicaition will override the cusomer****/
/****  level selection flags based on application specific logic.    ****/
/****  TK: Added MOB Archive queue application flag                  ****/

   GO_01_PS_Queue_Application_Flag   = "Y"
   GO_01_PDF_Queue_Application_Flag  = "Y"
   GO_01_TIFF_Queue_Application_Flag = "Y"
   GO_01_PDF_Edel_Queue_Application_Flag  = "Y"
   GO_01_PDF_Arch_Queue_Application_Flag  = "Y"

/************************************************************************/
</Formula>
  </Variable>
  <Variable VarOI="8341" Name="GO_BNG_ENG_TLE" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="As-needed"> Value = "BNG ENG TLE"
</Formula>
  </Variable>
  <Variable VarOI="8239" Name="GO_DOC_SEND_ADDR_LINE1" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8240" Name="GO_DOC_SEND_ADDR_LINE2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8241" Name="GO_DOC_SEND_ADDR_LINE3" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8242" Name="GO_DOC_SEND_ADDR_LINE4" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8243" Name="GO_DOC_SEND_ADDR_LINE5" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8244" Name="GO_DOC_SEND_ADDR_LINE6" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8245" Name="GO_DOC_SEND_ADDR_ZIP_CODE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8246" Name="GO_DOC_ACCOUNT_NUMBER" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8247" Name="GO_DOC_TOTAL_AMOUNT_DUE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8248" Name="GO_DOC_TOTAL_AMOUNT_DUE_SIGN" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8249" Name="GO_DOC_PRODUCT_CODE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8250" Name="GO_DOC_SPECIAL_HANDLING_CODE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8251" Name="GO_DOC_COUNTRY_CODE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8252" Name="GO_DOC_INSERT_COMBO" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8253" Name="GO_DOC_PKG_TYPE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8254" Name="GO_DOC_RETURN_ADDR_LINE1" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8255" Name="GO_DOC_RETURN_ADDR_LINE2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8256" Name="GO_DOC_RETURN_ADDR_LINE3" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8257" Name="GO_DOC_RETURN_ADDR_LINE4" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8258" Name="GO_DOC_RETURN_ADDR_LINE5" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8259" Name="GO_DOC_REMIT_ADDR_LINE1" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8260" Name="GO_DOC_REMIT_ADDR_LINE2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8261" Name="GO_DOC_REMIT_ADDR_LINE3" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8262" Name="GO_DOC_REMIT_ADDR_LINE4" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8263" Name="GO_DOC_REMIT_ADDR_LINE5" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8264" Name="GO_DOC_REMIT_ADDR_ZIP_CODE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8297" Name="GO_FILE_TOTAL_AMOUNT_DUE" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8266" Name="GO_FILE_TOTAL_AMOUNT_DUE_SIGN" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8267" Name="GO_FILE_IMAGE_COUNT" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8268" Name="GO_FILE_SHEET_COUNT" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="8269" Name="GO_FILE_STATEMENT_COUNT" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="9174" Name="GO_DOC_QV_INDEX_SEARCH_2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="9180" Name="GO_DOC_QV_INDEX_SEARCH_3" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="810" Name="ECS_01_IndivFileNamePDF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production PDF Queues for generating a unique, meaningful named PDF file for each customer.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="752" Name="GO_MI_PST_RQST_ID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>CS_MI_PST_RQST_ID</Description>
    <Sample>PST01672734</Sample>
  </Variable>
  <Variable VarOI="771" Name="GO_MI_LETTER_DATE" Type="Date" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_LETTER_DATE</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="756" Name="GO_MI_FULL_NAME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_FULL_NAME</Description>
    <Sample>GO_MI_Mr. John Q. Participant</Sample>
  </Variable>
  <Variable VarOI="768" Name="GO_MI_ADDRESS_LINE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_MI_ADDRESS_LINE</Description>
    <Sample>GO_MI_ADDRESS_LINE</Sample>
  </Variable>
  <Variable VarOI="808" Name="GO_02_LogoPlaceholder_JPG" Type="JPEG Pass-Through Image" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>The Master Formula for an app will concatenate the variable file path with the filename for the proper Logo</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="803" Name="GO_02_LogoPlaceholder_TIF" Type="TIFF-G4 Image" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>Global Variable for holding  TIFF Images.  Multiple numbered vars are created for use in applications where more than one type of logo is used in same document. See for eg:  ACS</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="4836" Name="PageNumber" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>A001</Sample>
  </Variable>
  <Variable VarOI="2418" Name="GO_01_Mobius_02_Data_String" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>MOB02 barcode data string</Description>
    <Sample>#POST#-FollowedByEmailFields</Sample>
    <Formula Compute="As-needed">Value = "|MOB02|"
IF Trim(GO_01_MOB_Composite_OrchestrationID) &lt;&gt; "" THEN
   Value = Value &amp; "|COMPORCHID|" &amp; 
GO_01_MOB_Composite_OrchestrationID
ENDIF
IF Trim(GO_01_MOB_OrchestrationID) &lt;&gt; "" THEN
   Value = Value &amp; "|ORCHESTRATIONID|" &amp; GO_01_MOB_OrchestrationID
ENDIF
IF Trim(GO_01_MOB_Unique_Key) &lt;&gt; "" THEN
   Value = Value &amp; "|MVASUNIQUE|" &amp; GO_01_MOB_Unique_Key
ENDIF

//Added case statement for 2 confirm groups that need the MOB02 key. B Nielsen 9/22/14
//Added case statement for PTRKIACK and ANNCONST. Arnab 11/13/17

SELECT CASE  GO_MI_PCKGE_CDE

CASE "CONFXFRALLOC","CONFASTREBAL"
     IF CF_FromTiaaNumberClosed &lt;&gt; '' THEN
          Value = Value &amp; "|TIAANUMBER|" &amp;  CF_FromTiaaNumberClosed
     ELSE
          Value = Value &amp; "|TIAANUMBER|" &amp;  CF_FromTiaaNumber
     ENDIF
	 
	 IF CF_FromCrefNumber &lt;&gt; '' THEN
	      Value = Value &amp; "|CREFNUMBER|" &amp; CF_FromCrefNumber
	 ENDIF
	 
	 IF CF_Apin &lt;&gt; '' THEN
		Value = Value &amp; "|APIN|" &amp; CF_Apin
	 ELSE
	    Value = Value &amp; "|APIN| " 
	 ENDIF
	 
	 IF CF_BulkTradeBatchNum &lt;&gt; '' THEN
	      Value = Value &amp; "|BULKTRADEBATCHNUMBER|" &amp; CF_BulkTradeBatchNum
	 ELSE
	    Value = Value &amp; "|BULKTRADEBATCHNUMBER| " 
     	 ENDIF
	 
	 IF CF_SubPlanId &lt;&gt; '' THEN
		Value = Value &amp; "|SUBPLANID|" &amp; CF_SubPlanId
	 ELSE
	    Value = Value &amp; "|SUBPLANID| " 
	 ENDIF
     	 
	 IF CF_FromTiaaNumberOpen &lt;&gt; '' THEN
            Value = Value &amp; "|TIAANUMBER|" &amp;  CF_FromTiaaNumberOpen 
     	 ENDIF

     	 IF GO_MI_PST_RQST_ID &lt;&gt; '' THEN
	    Value = Value &amp; "|PACKAGEID|" &amp;  GO_MI_PST_RQST_ID
	 ELSE
	    Value = Value &amp; "|PACKAGEID| " 
	 ENDIF

     	 Value = Value &amp; "|END|"
CASE "BAO" 
     IF FR_BAO_AccountType &lt;&gt; '' THEN
        value = value &amp; "|ACCOUNTTYPE|" &amp; FR_BAO_AccountType
     ENDIF
     IF FR_BAO_AccountCategory &lt;&gt; '' THEN
        value = value &amp; "|ACCOUNTCATEGORY|" &amp; FR_BAO_AccountCategory
     ENDIF
     IF FR_BAO_ProposalNumber &lt;&gt; '' THEN
        value = value &amp; "|PROPOSALNUMBER|" &amp; FR_BAO_ProposalNumber
     ENDIF
     Value = Value &amp; 
     "|BUSINESSUNITCODE|" &amp; 
     GO_01_MOB_Bus_Code &amp;
     "|DOCCODE|" &amp; 
     GO_01_MOB_Doc_Code &amp;
     "|DOCVERSION|" &amp; 
     GO_01_MOB_Doc_Version &amp;
     "|CONFIRSTNAME|" &amp; 
     GO_01_MOB_ConFirstName &amp; 
     "|CONLASTNAME|" &amp; 
     GO_01_MOB_ConLastName &amp; 
     "|PACKAGEID|" &amp; 
     GO_MI_PST_RQST_ID &amp;  
     "|END|"
CASE "PTRKLONI" 
     Value = Value &amp; 
     "|BUSINESSUNITCODE|" &amp; 
     GO_01_MOB_Bus_Code &amp;
     "|DOCCODE|" &amp; 
     GO_01_MOB_Doc_Code &amp;
     "|DOCVERSION|" &amp; 
     GO_01_MOB_Doc_Version &amp;
     "|CONFIRSTNAME|" &amp; 
     GO_01_MOB_ConFirstName &amp; 
     "|CONLASTNAME|" &amp; 
     GO_01_MOB_ConLastName &amp;
     "|TIAA_ID|" &amp; 
     GO_01_MOB_TIAAID &amp;  
     "|PACKAGEID|" &amp; 
     GO_MI_PST_RQST_ID &amp;  
     "|TRANSACTION_ID|" &amp; 
     GO_DR_TRANSACTION_REQ_ID &amp;
     "|DELIVERY_METHOD|" &amp; 
     GO_DELIVERY_METHOD &amp;
     "|END|"
CASE "PTRKIACK" 
Value = Value &amp; 
     "|BUSINESSUNITCODE|" &amp; 
     "PENSION" &amp;
     "|DOCCODE|" &amp; 
     "ISSUANCE_ACK_LETTER" &amp;
     "|DOCVERSION|" &amp; 
     1 &amp;
     "|CUSTOMER_NUMBER|" &amp; 
     GO_01_MOB_CustNum &amp;
     "|CONFIRSTNAME|" &amp; 
     GO_01_MOB_ConFirstName &amp; 
     "|CONLASTNAME|" &amp; 
     GO_01_MOB_ConLastName &amp;
     "|TIAA_ID|" &amp; 
     GO_01_MOB_TIAAID &amp;  
     "|PACKAGEID|" &amp; 
     GO_MI_PST_RQST_ID &amp;  
     "|END|"
CASE "ANNCONST" 
Value = Value &amp; 
     "|BUSINESSUNITCODE|" &amp; 
     "PENSION" &amp;
     "|DOCCODE|" &amp; 
     "ANNUITY_CONTRIB_STMT" &amp;
     "|DOCVERSION|" &amp; 
     1 &amp;
     "|CUSTOMER_NUMBER|" &amp; 
     GO_01_MOB_CustNum &amp;
     "|CONFIRSTNAME|" &amp; 
     GO_01_MOB_ConFirstName &amp; 
     "|CONLASTNAME|" &amp; 
     GO_01_MOB_ConLastName &amp;
     "|TIAA_ID|" &amp; 
     GO_01_MOB_TIAAID &amp;  
     "|PACKAGEID|" &amp; 
     GO_MI_PST_RQST_ID &amp;  
     "|END|"
CASE ELSE 
     Value = Value &amp; 
     "|BUSINESSUNITCODE|" &amp; 
     GO_01_MOB_Bus_Code &amp;
     "|DOCCODE|" &amp; 
     GO_01_MOB_Doc_Code &amp;
     "|DOCVERSION|" &amp; 
     GO_01_MOB_Doc_Version &amp;
     "|CONFIRSTNAME|" &amp; 
     GO_01_MOB_ConFirstName &amp; 
     "|CONLASTNAME|" &amp; 
     GO_01_MOB_ConLastName &amp;
     "|TIAA_ID|" &amp; 
     GO_01_MOB_TIAAID &amp;  
     "|PACKAGEID|" &amp; 
     GO_MI_PST_RQST_ID &amp;  
     "|END|"
END SELECT
</Formula>
  </Variable>
  <Variable VarOI="1140" Name="GO_01_Mobius_Data_String" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_Mobius_Data_String_POC</Description>
    <Sample>#POST#-FollowedByEmailFields</Sample>
    <Formula Compute="As-needed">Value = 
"|MOB01|" &amp;
"|DOCTYPEID|" &amp; GO_01_MOB_DocTypeID &amp;
"|PIN|" &amp; GO_01_MOB_Pin_Number &amp;
"|EMAIL|" &amp; GO_01_MOB_Email &amp;
"|DESCRIPTION|" &amp; GO_01_MOB_Descr &amp;
"|PREFIX|" &amp; GO_01_MOB_Prefix &amp;
"|LASTNAME|" &amp; GO_01_MOB_Last_Name &amp;
"|FIRSTNAME|" &amp; GO_01_MOB_First_Name &amp;
"|MIDDLENAME|" &amp; GO_01_MOB_Middle_Name &amp;
"|SUFFIX|" &amp; GO_01_MOB_Suffix &amp;
"|EMAILTEMPLATE|" &amp;  GO_01_MOB_Email_TemplateID &amp;
"|DELIVERYTYPE|" &amp; GO_01_MOB_Delivery_Type &amp;
"|APPROVED|" &amp; GO_01_MOB_Approved_Flag &amp;
"|DRI|" &amp; GO_01_MOB_DocID &amp;
"|PLANID|" &amp; GO_01_MOB_PlanID &amp;
"|BUSDATE|" &amp; GO_01_MOB_Bus_Date &amp;
"|SEQUENCE|" &amp; GO_01_MOB_Seq_Number &amp;
"|END|"

</Formula>
  </Variable>
  <Variable VarOI="1431" Name="GO_MI_BUSINESS_DATE" Type="Date" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_BUSINESS_DATE</Description>
    <Sample>January 1, 2011</Sample>
  </Variable>
  <Variable VarOI="2410" Name="GO_DR_ORCHESTRATION_ID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>2352626</Sample>
  </Variable>
  <Variable VarOI="1" Name="SYS_DateCurrent" Type="Date" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The date that the engine is being run to produce documents (today).</Description>
    <Sample>September 25, 2007</Sample>
  </Variable>
  <Variable VarOI="16974" Name="GO_00_GLOBAL_RB_Tiaa" Type="String" Source="Formula" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Global Formula for dynamically setting Tiaa Text</Description>
    <Sample>TIAA</Sample>
    <Formula Compute="Initialization">IF GO_00_GLOBAL_RB_COMPANY = 'TIAA' THEN
   value = "TIAA"
ELSE
   value = "TIAA-CREF"            
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="14935" Name="BAO_ListofForms" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>TIAA-CREF Borkerage Services - Account Application</Sample>
  </Variable>
  <Variable VarOI="16133" Name="FR_BAO_OtherDescription" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>OtherDescription</Description>
    <Sample>test test test test</Sample>
  </Variable>
  <Variable VarOI="3398" Name="FR_02_Title" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Mr.</Sample>
  </Variable>
  <Variable VarOI="3399" Name="FR_02_FirstName" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>John</Sample>
  </Variable>
  <Variable VarOI="3400" Name="FR_02_MiddleName" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Q</Sample>
  </Variable>
  <Variable VarOI="3401" Name="FR_02_LastName" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Public</Sample>
  </Variable>
  <Variable VarOI="3683" Name="FR_02_Suffix" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="14970" Name="FR_AccountOwnerEmail" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14973" Name="FR_AccountOwnerHomePhone" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>(999)999-9999</Sample>
  </Variable>
  <Variable VarOI="14980" Name="FR_AccountOwnerCitizenship" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14996" Name="FR_BAO_02_AccountOwnerSSN" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="15001" Name="FR_BAO_02_TrustEffectiveDate" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="15002" Name="FR_BAO_02_TrustSSN" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="15003" Name="FR_BAO_02_TrustAccountName" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>FR_BAO_02_TrustAccount_Name</Description>
    <Sample>Full Name Array</Sample>
  </Variable>
  <Variable VarOI="15011" Name="FR_BAO_02_AccountOwner_BirthDate" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="15373" Name="FR_AccountOwnerResidentialStreetAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15374" Name="FR_AccountOwnerResidentialCity" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15376" Name="FR_AccountOwnerResidentialZipcode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15378" Name="FR_AccountOwnerMailingStreetAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15379" Name="FR_AccountOwnerMailingCity" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15381" Name="FR_AccountOwnerMailingZipcode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15383" Name="FR_BAO_02_State" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="15478" Name="FR_AccountOwnerBusinessPhone" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>(999)999-9999</Sample>
  </Variable>
  <Variable VarOI="16137" Name="FR_AccountOwnerExtension" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description>Extension</Description>
    <Sample>x2888</Sample>
  </Variable>
  <Variable VarOI="16165" Name="FR_AccountOwnerHomeExtension" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>ex. 2</Sample>
  </Variable>
  <Variable VarOI="16643" Name="FR_BAO_02_AccountOwnerSSN2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="16644" Name="FR_BAO_02_AccountOwner_BirthDate2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="14983" Name="FR_BAO_SourceOfIncome" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Active</Sample>
  </Variable>
  <Variable VarOI="15439" Name="FR_BAO_EmployerAddress" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15440" Name="FR_BAO_EmployerCity" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15442" Name="FR_BAO_EmployerName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15444" Name="FR_BAO_EmployerTitle" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15445" Name="FR_BAO_EmployerZipcode" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15466" Name="FR_BAO_Trade_PersonName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15467" Name="FR_BAO_Trade_CompanyNameSymbol" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15468" Name="FR_BAO_Firm_PersonName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15469" Name="FR_BAO_Firm_Relationship" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15470" Name="FR_BAO_Firm_Name" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15481" Name="FR_BAO_TIAACREF_Relationship" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15482" Name="FR_BAO_TIAACREF_PersonName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="16142" Name="FR_BAO_SourceOfFundsOtherDescription" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Test Test Test Test</Sample>
  </Variable>
  <Variable VarOI="14964" Name="FR_AccountOwnerName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="3283" Name="FR_PrimaryBeneficiaryName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="3284" Name="FR_PrimaryBeneficiaryPercentage" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>0</Sample>
  </Variable>
  <Variable VarOI="3287" Name="FR_PrimaryBeneficiaryRelationship" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="3669" Name="FR_ER_02_PrimaryBeneficiarySSN" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="15012" Name="FR_BAO_02_Beneficiary_BirthDate" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="16646" Name="FR_BAO_02_BeneficiaryFlag1" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16649" Name="FR_BAO_02_BeneficiaryFlag3" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16650" Name="FR_BAO_02_BeneficiaryFlag4" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16648" Name="FR_BAO_02_BeneficiaryFlag2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="3288" Name="FR_ContingentBeneficiaryName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="3289" Name="FR_ContingentBeneficiaryPercentage" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>0</Sample>
  </Variable>
  <Variable VarOI="3292" Name="FR_ContingentBeneficiaryRelationship" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="3681" Name="FR_ER_02_ContingentBeneficiarySSN" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="16651" Name="FR_BAO_02_ContingencyFlag2" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16647" Name="FR_BAO_02_ContingencyFlag1" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16645" Name="FR_BAO_02_SpousalWaiver" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="16652" Name="FR_BAO_02_ContingencyFlag3" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16653" Name="FR_BAO_02_ContingencyFlag4" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16164" Name="FR_ProposalNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>346654</Sample>
  </Variable>
  <Variable VarOI="18532" Name="FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18695" Name="FR_BAO_HighlightSSN_Field" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>YES</Sample>
  </Variable>
  <Variable VarOI="14966" Name="FR_AccountOwnerFirstName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14968" Name="FR_AccountOwnerLastName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="16146" Name="FR_BAO_InitialTransactionsAccountNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>234567</Sample>
  </Variable>
  <Variable VarOI="16150" Name="FR_BAO_InitialTransactionsTransferCashAmount" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>20.0</Sample>
  </Variable>
  <Variable VarOI="16151" Name="FR_BAO_InitialTransactionsSharesDescription" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="16152" Name="FR_BAO_InitialTransactionsSharesQuantity" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>**********</Sample>
  </Variable>
  <Variable VarOI="4251" Name="FR_01_FullName" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Full Name</Sample>
    <Formula Compute="As-needed">IF Trim(COMMON.GO_MI_FULL_NAME) = "" THEN
   Value = Trim(COMMON.GO_MI_PREFIX) &amp; " " &amp; Trim(COMMON.GO_MI_FIRST_NAME)
   IF Trim(COMMON.GO_MI_MID_NAME) &lt;&gt; "" THEN
      Value = Trim(Value) &amp; " " &amp; Trim(COMMON.GO_MI_MID_NAME)
   ENDIF
   Value = Trim(Value) &amp; " " &amp; Trim(COMMON.GO_MI_LAST_NAME) &amp; " " &amp; Trim(COMMON.GO_MI_SUFFIX)
ELSE
   Value = Trim(COMMON.GO_MI_FULL_NAME)
ENDIF

</Formula>
  </Variable>
  <Variable VarOI="31" Name="SYS_PageTotalPrinted" Type="Integer" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The last printed page number in the current document being written.  See SYS_PagePrintedValue for the current printed page number of a specific page.  See SYS_PagePrintedValue for a specific page number.</Description>
    <Sample>345</Sample>
  </Variable>
  <Variable VarOI="51" Name="SYS_PagePrintedValue" Type="Integer" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The page number that should be printed on the current page.  The page number may differ from page count because of page count restart or limitation on counting blank pages.</Description>
    <Sample>21</Sample>
  </Variable>
  <Variable VarOI="18476" Name="FR_PrimaryBeneficiaryBirthDate_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1967-08-13</Sample>
  </Variable>
  <Variable VarOI="18478" Name="FR_PrimaryBeneficiaryName_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="18481" Name="FR_PrimaryBeneficiarySSN_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18482" Name="FR_PrimaryBeneficiary_City_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>charlotte</Sample>
  </Variable>
  <Variable VarOI="18483" Name="FR_PrimaryBeneficiary_State_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>MA</Sample>
  </Variable>
  <Variable VarOI="18484" Name="FR_PrimaryBeneficiary_StreetAddress_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1733 sweet street</Sample>
  </Variable>
  <Variable VarOI="18485" Name="FR_PrimaryBeneficiary_zipcode_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>28162</Sample>
  </Variable>
  <Variable VarOI="18487" Name="FR_PrimaryBeneficiary_PhoneNumber_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6897645432</Sample>
  </Variable>
  <Variable VarOI="18489" Name="FR_ContingentBeneficiaryBirthDate_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1967-08-13</Sample>
  </Variable>
  <Variable VarOI="18491" Name="FR_ContingentBeneficiaryName_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="18494" Name="FR_ContingentBeneficiarySSN_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18496" Name="FR_ContingentBeneficiary_City_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>atlanta</Sample>
  </Variable>
  <Variable VarOI="18497" Name="FR_ContingentBeneficiary_State_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>CO</Sample>
  </Variable>
  <Variable VarOI="18498" Name="FR_ContingentBeneficiary_StreetAddress_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>173 candler street</Sample>
  </Variable>
  <Variable VarOI="18499" Name="FR_ContingentBeneficiary_zipcode_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>55562</Sample>
  </Variable>
  <Variable VarOI="18500" Name="FR_ContingentBeneficiaryContactPhoneNumber_Display" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>(999)999-9999</Sample>
  </Variable>
  <Variable VarOI="16154" Name="FR_BAO_DecendentFirstName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Firstname</Sample>
  </Variable>
  <Variable VarOI="16156" Name="FR_BAO_DecendentLastName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Lastname</Sample>
  </Variable>
  <Variable VarOI="18543" Name="FR_BAO_02_Decendent_BirthDate_display" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="18544" Name="FR_BAO_02_Decendent_DeathDate_display" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="18772" Name="FR_BAO_02_Trust_AmmendmentDate" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="18701" Name="FR_Grantor_FirstName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6TEST</Sample>
  </Variable>
  <Variable VarOI="18703" Name="FR_Grantor_LastName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>ACCOUNT</Sample>
  </Variable>
  <Variable VarOI="18710" Name="FR_Grantor_ResAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>********* KNOXWOOD DR </Sample>
  </Variable>
  <Variable VarOI="18711" Name="FR_Grantor_ResCity" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6666HUNTERSVILLE</Sample>
  </Variable>
  <Variable VarOI="18713" Name="FR_Grantor_ResZip" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18717" Name="FR_JointGrantor_FirstName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6TEST</Sample>
  </Variable>
  <Variable VarOI="18719" Name="FR_JointGrantor_LastName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>ACCOUNT</Sample>
  </Variable>
  <Variable VarOI="18726" Name="FR_JointGrantor_ResStreetAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>********* KNOXWOOD DR </Sample>
  </Variable>
  <Variable VarOI="18727" Name="FR_JointGrantor_ResCity" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6666HUNTERSVILLE</Sample>
  </Variable>
  <Variable VarOI="18729" Name="FR_JointGrantor_ResZip" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="16179" Name="FR_BAO_02_SEPIRA_TIN_SSN" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="16134" Name="FR_BAO_TrustAccountName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>TrustAccountName</Description>
    <Sample>Herbert</Sample>
  </Variable>
  <Variable VarOI="16160" Name="FR_BAO_DecendentRelationship" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="16708" Name="FR_BAO_02_SpousalWaiverFlag1" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="20320" Name="FR_BAO_02_AccountOwnerBrokerageAccountNumber" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>TLENGONG-12426</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="16136" Name="FR_BAO_TrustEffectiveDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>TrustEffectiveDate</Description>
    <Sample>2015-01-30-05:00</Sample>
  </Variable>
  <Variable VarOI="20321" Name="FR_BAO_AmendmentDateofTrust" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>TLENGONG-12426</Description>
    <Sample>2015-01-30-05:00</Sample>
  </Variable>
  <Variable VarOI="16062" Name="BAO_BarcodeInformation" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Information Field - 18 byte field, left justified</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">value=Format(GO_DR_ORCHESTRATION_ID, "!@@@@@@@@@@@@@@@@@@@")
</Formula>
  </Variable>
  <Variable VarOI="16060" Name="BAO_BarcodeFormNumber" Type="String" Source="Formula" ResetTime="Automatically ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="As-needed">value=Format(FR_02_CurrentForm(SYS_SubDocInDocument), "@@@@@@@@@@@@@@@@@@@")
</Formula>
  </Variable>
  <Variable VarOI="16061" Name="BAO_BarcodePrefix" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Prefix Field - 2 bytes field</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">value=Format("T", "@")
</Formula>
  </Variable>
  <Variable VarOI="16065" Name="BAO_BarcodePage" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Page Field - 2 byte field</Description>
    <Sample/>
    <Formula Compute="As-needed">Value=Format(SYS_PagePrintedValue, "00")

//IF ((SYS_PagePrintedValue mod 2) = 0) THEN
    //back page
//    VALUE="02"
//ELSE
    //front page
//    VALUE="01"
//ENDIF
</Formula>
  </Variable>
  <Variable VarOI="18542" Name="BAO_BarcodeFormVersion" Type="String" Source="Formula" ResetTime="Automatically ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="As-needed">value=Format( FR_02_CurrentVersionDate(SYS_SubDocInDocument), "@@@@")
</Formula>
  </Variable>
  <Variable VarOI="16210" Name="BAO_BarcodeDataStringINSTRUCTIONS" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="As-needed">VALUE= "INSTRUCTIONS"
</Formula>
  </Variable>
  <Variable VarOI="18884" Name="BAO_BarcodePage_Minus3" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Page Field - 2 byte field</Description>
    <Sample/>
    <Formula Compute="As-needed">Value=Format(SYS_PagePrintedValue-3, "00")


</Formula>
  </Variable>
  <Variable VarOI="764" Name="GO_DDA_DOC_REQ_ID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>VA</Description>
    <Sample>1234</Sample>
  </Variable>
  <Variable VarOI="14805" Name="FR_01_BAO_EXT_PDFFiles_PLACEHOLDER" Type="PDF Pass-Through" Source="User value" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description>FR_01_BAO_EXT_PDFFiles_PLACEHOLDER</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="16028" Name="FR_BAO_PassThrough_Test" Type="PDF Pass-Through" Source="File" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="16123" Name="FR_BAO_CitiPassThrough_Test" Type="PDF Pass-Through" Source="User value" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="17971" Name="FR_01_BAO_EXT_PDFFiles_PLACEHOLDER_Landscape" Type="PDF Pass-Through" Source="User value" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description>FR_01_BAO_EXT_PDFFiles_PLACEHOLDER</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="18278" Name="FR_BAO_CoverLetter_PassThrough" Type="PDF Pass-Through" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="As-needed">FR_BAO_CoverLetter_PassThrough = ECS_01_OutputPathforResources &amp; "A14823.pdf" 
//VALUE="S:\Exstream Shared Folder\GO_EXSTREAM\RESOURCES\A14823.pdf"
</Formula>
  </Variable>
  <Variable VarOI="19456" Name="FR_BAO_PassThrough_BankSweep_TC" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="19457" Name="FR_BAO_PassThrough_LiquidInsurance_TC" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="21794" Name="FR_BAO_PassThrough_BrokerageSweep_TC_DGVXX" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="21791" Name="FR_BAO_PassThrough_BrokerageSweep_TC_DGUXX" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="21793" Name="FR_BAO_PassThrough_BrokerageSweep_TC_DVPXX" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="21795" Name="FR_BAO_PassThrough_BrokerageSweep_TC_GFYXX" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="21796" Name="FR_BAO_PassThrough_BrokerageSweep_TC_TTIXX" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="3687" Name="FR_02_CurrentForm" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description>FR_02_CurrentForm</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="142" Name="SYS_SubDocInDocument" Type="Integer" Source="System" ResetTime="Automatically" Access="Rule" IsArray="No">
    <Description>The current document being written to the current customer.</Description>
    <Sample>11</Sample>
  </Variable>
  <Variable VarOI="3693" Name="FR_02_CurrentVersionDate" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description>FR_02_CurrentVersionDate</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="18540" Name="FR_BAO_FormNumber" Type="String" Source="File" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample>F11207</Sample>
  </Variable>
  <Variable VarOI="4623" Name="FR_02_WPID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>TAKION</Sample>
  </Variable>
  <Variable VarOI="3690" Name="FR_02_FormId" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_02_FormID</Description>
    <Sample>F111111 (06/12)</Sample>
  </Variable>
  <Variable VarOI="20318" Name="BAO_NonIRAOption_2" Type="String" Source="File" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample>Trust</Sample>
  </Variable>
  <Variable VarOI="763" Name="GO_MI_PCKGE_CDE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PCKGE_CDE</Description>
    <Sample>PTBENCNF</Sample>
  </Variable>
  <Variable VarOI="3565" Name="FR_LetterType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_LetterType</Description>
    <Sample>CoverLetter</Sample>
  </Variable>
  <Variable VarOI="4637" Name="GO_DR_DOC_CODE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_DOC_CODE</Description>
    <Sample>CW_SpousalWaiver</Sample>
  </Variable>
  <Variable VarOI="21787" Name="FR_BAO_DreyfusGovCashManageInvester" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="21785" Name="FR_BAO_DreyfusGovCashManageServiceShares" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="21786" Name="FR_BAO_DreyfusGovSecCashManageInvestor" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="21788" Name="FR_BAO_FederatedHermesGovObligationsCash" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="21789" Name="FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="21790" Name="FR_BAO_Liquid" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="20317" Name="BAO_IRAOption_2" Type="String" Source="File" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description/>
    <Sample>Inherited Roth IRA</Sample>
  </Variable>
  <Variable VarOI="852" Name="GO_01_TestOrProd_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample>T</Sample>
  </Variable>
  <Variable VarOI="775" Name="GO_MI_PCKGE_DLVRY_TYP" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PCKGE_DLVRY_TYP</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="1354" Name="PRO_CheckEFTInd" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Check</Sample>
  </Variable>
  <Variable VarOI="16975" Name="GO_00_GLOBAL_RB_COMPANY" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Global Formula for dynamically setting Tiaa Text, set in Control Script</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="1355" Name="PRO_HoldCode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>PS01</Sample>
  </Variable>
  <Variable VarOI="13928" Name="FIF_CheckEFTInd" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Check</Sample>
  </Variable>
  <Variable VarOI="19412" Name="FIF_CheckEFTInd_N" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Check</Sample>
  </Variable>
  <Variable VarOI="19353" Name="CIS_EOP_REPRINT_IND" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>R</Sample>
  </Variable>
  <Variable VarOI="20507" Name="CIS_EOP_WATERMARK_IND" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="14990" Name="FR_BAO_NonIraOption" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="3351" Name="FR_02_TempArray" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>Temporary array</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="16135" Name="FR_BAO_TrustSSN" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>TrustSSN</Description>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="14965" Name="FR_AccountOwnerTitle" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14967" Name="FR_AccountOwnerMiddleName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15014" Name="FR_AccountOwnerSuffix" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14969" Name="FR_AccountOwnerSSN" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="14972" Name="FR_AccountOwnerBirthDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1967-08-13</Sample>
  </Variable>
  <Variable VarOI="15375" Name="FR_AccountOwnerResidentialState" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15380" Name="FR_AccountOwnerMailingState" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14971" Name="FR_AccountOwnerGender" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Male</Sample>
  </Variable>
  <Variable VarOI="14974" Name="FR_AccountOwnerMaritalStatus" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15443" Name="FR_BAO_EmployerState" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14982" Name="FR_BAO_EmploymentStatus" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Active</Sample>
  </Variable>
  <Variable VarOI="14985" Name="FR_AccountOwnerTradeCompanyCB" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14986" Name="FR_AccountOwnerTIAACREFCB" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14987" Name="FR_AccountOwnerMemberFirmCB" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14988" Name="FR_AccountOwnerSeniorMilitaryCB" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14991" Name="FR_BAO_InvestmentObjective" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="14989" Name="FR_AccountOwnerInvestmentProfile" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14992" Name="FR_BAO_AnnualIncome" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="14993" Name="FR_BAO_NetWorth" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="14994" Name="FR_BAO_TaxBraket" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="14995" Name="FR_BAO_SourceOfFunds" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="16067" Name="FR_BAO_AccountType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="3285" Name="FR_PrimaryBeneficiarySSN" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="3286" Name="FR_PrimaryBeneficiaryBirthDate" Type="Date" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1967-08-13</Sample>
  </Variable>
  <Variable VarOI="3437" Name="FR_PrimaryBeneficiaryGender" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Male</Sample>
  </Variable>
  <Variable VarOI="16131" Name="FR_PrimaryBeneficiaryLDPS" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description>PrimaryBeneficiaryLDPS</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="3290" Name="FR_ContingentBeneficiarySSN" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="3291" Name="FR_ContingentBeneficiaryBirthDate" Type="Date" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1967-08-13</Sample>
  </Variable>
  <Variable VarOI="3442" Name="FR_ContingentBeneficiaryGender" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Male</Sample>
  </Variable>
  <Variable VarOI="16132" Name="FR_ContingentBeneficiaryLDPS" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description>ContingentBeneficiaryLDPS</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="15016" Name="FR_BAO_IraOption" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="16153" Name="FR_BAO_EmployerTaxID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>2131415</Sample>
  </Variable>
  <Variable VarOI="18450" Name="FR_BAO_DeliveryMethod" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description/>
    <Sample>E-Signature-Agent</Sample>
  </Variable>
  <Variable VarOI="18531" Name="FR_AccountOwner_PhoneType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Business</Sample>
  </Variable>
  <Variable VarOI="16642" Name="FR_BAO_VoteProxy" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>VoteProxy</Description>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18449" Name="FR_BAO_ConfirmSuppressionStatus_Ind" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Confirm Suppression Indicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16176" Name="FR_BAO_SweepSelectionSweepAccount" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="18696" Name="FR_PrimaryBeneficiary_BeneficiaryType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Individual</Sample>
  </Variable>
  <Variable VarOI="18697" Name="FR_ContingentBeneficiary_BeneficiaryType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Trust</Sample>
  </Variable>
  <Variable VarOI="16145" Name="FR_BAO_InitialTransactionsTransferFunds" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16147" Name="FR_BAO_InitialTransactionsFullTransfer" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="16178" Name="FR_BAO_SweepSelectionPrime" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16148" Name="FR_BAO_InitialTransactionsDeliveringAccountClosed" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16149" Name="FR_BAO_InitialTransactionsPartialTransfer" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="16177" Name="FR_BAO_SweepSelectionCash" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="17913" Name="FR_BAO_PhoneType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Home</Sample>
  </Variable>
  <Variable VarOI="50" Name="SYS_PageInDocument" Type="Integer" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The current number of counted pages within the document being written.  See SYS_PageTotalInDocument for the total counted pages in the document.  You can set which pages are counted in the application.</Description>
    <Sample>21</Sample>
  </Variable>
  <Variable VarOI="30" Name="SYS_PageTotalInDocument" Type="Integer" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The total number of counted pages in the current document. The number of counted pages can differ from the number of physical pages if you set a page counting method in the application or use duplex. See SYS_PageInDocument for the current page number.</Description>
    <Sample>345</Sample>
  </Variable>
  <Variable VarOI="18486" Name="FR_PrimaryBeneficiary_PhoneNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6897645432</Sample>
  </Variable>
  <Variable VarOI="18466" Name="FR_PrimaryBeneficiary_StreetAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>1733 sweet street</Sample>
  </Variable>
  <Variable VarOI="18467" Name="FR_PrimaryBeneficiary_City" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>charlotte</Sample>
  </Variable>
  <Variable VarOI="18468" Name="FR_PrimaryBeneficiary_State" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>MA</Sample>
  </Variable>
  <Variable VarOI="18469" Name="FR_PrimaryBeneficiary_zipcode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>28162</Sample>
  </Variable>
  <Variable VarOI="18614" Name="FR_ContingentBeneficiary_Category" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>A</Sample>
  </Variable>
  <Variable VarOI="18495" Name="FR_ContingentBeneficiaryGender_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Male</Sample>
  </Variable>
  <Variable VarOI="18492" Name="FR_ContingentBeneficiaryPercentage_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>0</Sample>
  </Variable>
  <Variable VarOI="8380" Name="FR_ContingentBeneficiaryContactPhoneNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>(999)999-9999</Sample>
  </Variable>
  <Variable VarOI="18470" Name="FR_ContingentBeneficiary_StreetAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>173 candler street</Sample>
  </Variable>
  <Variable VarOI="18471" Name="FR_ContingentBeneficiary_City" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>atlanta</Sample>
  </Variable>
  <Variable VarOI="18472" Name="FR_ContingentBeneficiary_State" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>CO</Sample>
  </Variable>
  <Variable VarOI="18473" Name="FR_ContingentBeneficiary_zipcode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>55562</Sample>
  </Variable>
  <Variable VarOI="18480" Name="FR_PrimaryBeneficiaryRelationship_Display" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="18883" Name="FR_JointTenantAccount" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>RightsOfSurvivorship</Sample>
  </Variable>
  <Variable VarOI="18563" Name="FR_BAO_Decendent_PlanType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="18570" Name="FR_BAO_TypeOfTrust" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Irrevocable</Sample>
  </Variable>
  <Variable VarOI="16155" Name="FR_BAO_DecendentMiddleName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Middlename</Sample>
  </Variable>
  <Variable VarOI="16158" Name="FR_BAO_DecendentBirthDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>1960-11-11</Sample>
  </Variable>
  <Variable VarOI="16159" Name="FR_BAO_DecendentDeathDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>1990-11-11</Sample>
  </Variable>
  <Variable VarOI="18568" Name="FR_BAO_Trust_DelegateToAppointedAgent" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="18569" Name="FR_BAO_Trust_DelegateToOutsideProfessional" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="18771" Name="FR_BAO_Trust_AmmendmentDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>2016-01-01</Sample>
  </Variable>
  <Variable VarOI="18770" Name="FR_AccountOwner_GrantorType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="18702" Name="FR_Grantor_Middlename" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>B</Sample>
  </Variable>
  <Variable VarOI="18704" Name="FR_Grantor_SSN" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18707" Name="FR_Grantor_BirthDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6666-06-21</Sample>
  </Variable>
  <Variable VarOI="18712" Name="FR_Grantor_ResState" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>NC</Sample>
  </Variable>
  <Variable VarOI="18718" Name="FR_JointGrantor_MiddleName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>B</Sample>
  </Variable>
  <Variable VarOI="18720" Name="FR_JointGrantor_SSN" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18723" Name="FR_JointGrantor_Birthdate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6666-06-21</Sample>
  </Variable>
  <Variable VarOI="18728" Name="FR_JointGrantor_ResState" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>NC</Sample>
  </Variable>
  <Variable VarOI="16157" Name="FR_BAO_DecendentSSN" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="20322" Name="FR_BAO_TrustType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>TLENGONG-12426</Description>
    <Sample>Revocable</Sample>
  </Variable>
  <Variable VarOI="15015" Name="FR_AccountOwnerBrokerageAccountNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="70" Name="SYS_QueueCurrent" Type="String" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The name for the output queue for the current document.</Description>
    <Sample>Under 1 OZ</Sample>
  </Variable>
  <Variable VarOI="4837" Name="PageNumberPrefix" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="995" Name="GO_01_TIFF_Queue_Application_Flag" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>APP Level Queue Rule variable. App will enable/disable a particular "TYPE" of Queue by setting this flag.</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="1435" Name="GO_01_PDF_Arch_Queue_Application_Flag" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>APP Level Queue Rule variable. App will enable/disable a particular "TYPE" of Queue by setting this flag.</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="737" Name="GO_01_Init_Image_Library_Array" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description>GO_01_Init_Image_Library_Array - This array variable is mapped to the column of all possible image file names listed in the Image Initialization File</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="738" Name="GO_DDA_Customer_Track_Variable" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="736" Name="GO_01_Init_Image_Library_Array_TIFF" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description>GO_01_Init_Image_Library_Array - This array variable is mapped to the column of all possible image file names listed in the Image Initialization File</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="1463" Name="GO_AR_AUDIT_RPT_ID" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report ID</Description>
    <Sample/>
    <Formula Compute="Initialization">IF GO_01_EDELPDF_QUEUE_Flag &lt;&gt; '0'  AND IsNull(GO_01_EDELPDF_QUEUE_Flag) = False THEN
Value = Left(GO_AR_RPT_ID,  (Len(GO_AR_RPT_ID) - 1) ) &amp; "A"
ELSE
VALUE = GO_AR_RPT_ID
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="20" Name="SYS_CustomerInRun" Type="Integer" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>The current customer number being processed.</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="741" Name="GO_AR_FILE_NAME" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="742" Name="GO_AR_SYSTEM" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="743" Name="GO_AR_APP" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="751" Name="GO_AR_TOTAL_NOTICE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="745" Name="GO_AR_START_RQST_ID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="746" Name="GO_AR_END_RQST_ID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="747" Name="GO_AR_START_NAME" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="748" Name="GO_AR_END_NAME" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="749" Name="GO_AR_FOREIGN_NOTICE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="750" Name="GO_AR_BAD_ADDR_NOTICE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="744" Name="GO_AR_CYC_DATE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample>20100817144019</Sample>
  </Variable>
  <Variable VarOI="740" Name="GO_AR_RDT" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="753" Name="GO_MI_BC_PIN_NBR" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_BC_PIN_NBR</Description>
    <Sample>1234567</Sample>
  </Variable>
  <Variable VarOI="1092" Name="GO_00_AuditReports_Address_Typ_Code" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Copy of GO_MI_ADD_TYP_CDE  from I/P file for suppressing "U" in audit reports</Description>
    <Sample/>
    <Formula Compute="As-needed">IF  Ucase(GO_MI_ADD_TYP_CDE)  = 'U'   THEN
     Value = ''
ELSE
      Value = GO_MI_ADD_TYP_CDE
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1001" Name="GO_AR_RPT_ID_TIFF" Type="String" Source="Formula" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Audit Report TIFF</Description>
    <Sample>DCSD3019SI</Sample>
    <Formula Compute="As-needed">Value = Left(GO_AR_RPT_ID,  (Len(GO_AR_RPT_ID) - 1) ) &amp; "I"
</Formula>
  </Variable>
  <Variable VarOI="1003" Name="GO_00_GLOBAL_TIFF_AuditReport_SeqNo" Type="Integer" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample>001</Sample>
  </Variable>
  <Variable VarOI="1462" Name="GO_AR_RPT_ID_ARCH" Type="String" Source="Formula" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Audit Report ARCH</Description>
    <Sample>DCSD3019SI</Sample>
    <Formula Compute="As-needed">Value = Left(GO_AR_RPT_ID,  (Len(GO_AR_RPT_ID) - 1) ) &amp; "A"
</Formula>
  </Variable>
  <Variable VarOI="1448" Name="GO_00_GLOBAL_ARCH_AuditReport_SeqNo" Type="Integer" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample>001</Sample>
  </Variable>
  <Variable VarOI="761" Name="GO_DR_BATCH_COUNTER" Type="Integer" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_BATCH_COUNTER</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="757" Name="GO_DR_RQST_USER_ID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_RQST_USER_ID</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="758" Name="GO_DR_RQST_USER_NAME" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_RQST_USER_NAME</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="759" Name="GO_DR_RQST_DATE_TIME" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_RQST_DATE_TIME</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="3710" Name="GO_DR_COMPOSITE_ORCH_ID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_COMPOSITE_ORCH_ID</Description>
    <Sample>CompOrchId_11</Sample>
  </Variable>
  <Variable VarOI="4636" Name="GO_DR_BUSINESS_UNIT_CODE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_BUSINESS_UNIT_CODE</Description>
    <Sample>PENSION</Sample>
  </Variable>
  <Variable VarOI="4638" Name="GO_DR_VERSION" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_VERSION</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="4639" Name="GO_DR_DOC_SEQUENCE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_DOC_SEQUENCE</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="760" Name="GO_DR_BATCH_IND" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_BATCH_IND</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="765" Name="GO_MI_PRNTR_ID_CDE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PRNTR_ID_CDE</Description>
    <Sample>TZUG    </Sample>
  </Variable>
  <Variable VarOI="1147" Name="GO_MI_PREFIX" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PREFIX</Description>
    <Sample>                                   </Sample>
  </Variable>
  <Variable VarOI="1149" Name="GO_MI_FIRST_NAME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_FIRST_NAME</Description>
    <Sample>John                        </Sample>
  </Variable>
  <Variable VarOI="1148" Name="GO_MI_MID_NAME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_MIDDLE_NAME</Description>
    <Sample>                                   </Sample>
  </Variable>
  <Variable VarOI="1146" Name="GO_MI_SUFFIX" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_MI_SUFFIX</Description>
    <Sample> </Sample>
  </Variable>
  <Variable VarOI="766" Name="GO_MI_LAST_NAME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_LAST_NAME</Description>
    <Sample>                                   </Sample>
  </Variable>
  <Variable VarOI="754" Name="GO_MI_ADD_TYP_CDE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>CS_MI_ADD_TYP_CDE</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="770" Name="GO_MI_ALT_DLVRY_ADDR" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_ALT_DLVRY_ADDR</Description>
    <Sample>Fax Number or Email Address                                                                                              </Sample>
  </Variable>
  <Variable VarOI="776" Name="GO_MI_PCKGE_IMAGE_IND" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PCKGE_IMAGE_IND</Description>
    <Sample> </Sample>
  </Variable>
  <Variable VarOI="1449" Name="GO_MI_PORTAL_DOC_DESC" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PORTAL_DOC_DESC</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="1429" Name="GO_MI_ARCHIVAL_IND" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_ARCHIVAL_IND</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="1430" Name="GO_MI_PLAN_ID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PLAN_ID</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="16068" Name="FR_BAO_AccountCategory" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="15377" Name="FR_AccountOwnerResidentialCountry" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15382" Name="FR_AccountOwnerMailingCountry" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="14981" Name="FR_AccountOwnerStateofResidence" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="21784" Name="FR_BAO_Edelivery" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="15471" Name="FR_BAO_Margin" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="930" Name="GO_MI_EMPL_SGNTRY_NME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_EMPL_SGNTRY_NME</Description>
    <Sample>GO_MI_Empl_Sgntry_Name</Sample>
  </Variable>
  <Variable VarOI="773" Name="GO_MI_EMPL_UNIT_WORK_NME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_EMPL_UNIT_WORK_NME</Description>
    <Sample>Mail Item Work Unit Name                         </Sample>
  </Variable>
  <Variable VarOI="3558" Name="FR_StateOfResidence" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_StateOfResidence</Description>
    <Sample>NY</Sample>
  </Variable>
  <Variable VarOI="3559" Name="FR_Citizenship" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_Citizenship</Description>
    <Sample>United States</Sample>
  </Variable>
  <Variable VarOI="5953" Name="FR_PhoneNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_PhoneNumber</Description>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="3562" Name="FR_PlanName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_PlanName</Description>
    <Sample>XYZ Plan</Sample>
  </Variable>
  <Variable VarOI="3563" Name="FR_PlanId" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_PlanId</Description>
    <Sample>P12345</Sample>
  </Variable>
  <Variable VarOI="3620" Name="FR_FormNumber" Type="String" Source="File" ResetTime="Before section FORM" Access="Any" IsArray="No">
    <Description>FR_FormNumber</Description>
    <Sample>FR_FormNumber</Sample>
  </Variable>
  <Variable VarOI="3726" Name="FR_InstitutionName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_InstitutionName</Description>
    <Sample>FR_XYZ Institution</Sample>
  </Variable>
  <Variable VarOI="3727" Name="FR_RequestDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_RequestDate</Description>
    <Sample>2012-08-06</Sample>
  </Variable>
  <Variable VarOI="4580" Name="FR_MarriedIndicator" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_MarriedIndicator</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="3751" Name="FR_WithdrawalMethodType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_WithdrawalMethodType</Description>
    <Sample>CD</Sample>
  </Variable>
  <Variable VarOI="3754" Name="FR_TotalWithdrawalAmount" Type="Currency" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_TotalWithdrawalAmount</Description>
    <Sample>99,999,999.99</Sample>
  </Variable>
  <Variable VarOI="4574" Name="FR_SwatFrequency" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwatFrequency</Description>
    <Sample>Quarterly</Sample>
  </Variable>
  <Variable VarOI="4575" Name="FR_SwatNumberofPayments" Type="Integer" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwatNumberofPayments</Description>
    <Sample>5</Sample>
  </Variable>
  <Variable VarOI="4577" Name="FR_SwatPaymentsStopDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwatPaymentsStopDate</Description>
    <Sample>2012-07-01</Sample>
  </Variable>
  <Variable VarOI="4581" Name="FR_SwatNoFundsOrStopIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwatNoFundsOrStopIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="11692" Name="FR_SwTransactionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwTransactionType</Description>
    <Sample>RMD</Sample>
  </Variable>
  <Variable VarOI="11693" Name="FR_SwDistributionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwDistributionType</Description>
    <Sample>SWAT</Sample>
  </Variable>
  <Variable VarOI="11694" Name="FR_AnnualizedRequestAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_AnnualizedRequestAmount</Description>
    <Sample>11111.11</Sample>
  </Variable>
  <Variable VarOI="11695" Name="FR_SwRmd2in1OptionIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwRmd2in1OptionIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="11696" Name="FR_SwRmd2in1RequiredIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwRmd2in1RequiredIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="11697" Name="FR_SwRmd2in1EligibleIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwRmd2in1EligibleIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="4443" Name="FR_TotalPercentageDollarValue" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_TotalPercentageDollarValue</Description>
    <Sample>$22,222.22</Sample>
  </Variable>
  <Variable VarOI="12025" Name="FR_SwSubPlanDistributionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SwSubPlanDistributionType</Description>
    <Sample>SWAT</Sample>
  </Variable>
  <Variable VarOI="3753" Name="FR_TotalWithdrawalPercentage" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_TotalWithdrawalPercentage</Description>
    <Sample>50.00</Sample>
  </Variable>
  <Variable VarOI="4579" Name="FR_TotalBalanceAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_TotalBalanceAmount</Description>
    <Sample>$22,222.22</Sample>
  </Variable>
  <Variable VarOI="4466" Name="LT_FundNameArray" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>LT_FundNameArray</Description>
    <Sample>Payment Duration Fund Name</Sample>
  </Variable>
  <Variable VarOI="6460" Name="LT_FundTickerSymbolArray" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>LT_FundTickerSymbolArray</Description>
    <Sample>#1111</Sample>
  </Variable>
  <Variable VarOI="6401" Name="LT_FundWithdrawalPercentageArray" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>LT_FundWithdrawalPercentageArray</Description>
    <Sample>25</Sample>
  </Variable>
  <Variable VarOI="6402" Name="LT_FundWithdrawalAmountArray" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>LT_FundWithdrawalPercentageArray</Description>
    <Sample>********11111.11</Sample>
  </Variable>
  <Variable VarOI="3756" Name="FR_PullForm_NonStaplingPortraitFileName" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_PullForm_NonStaplingPortraitFileName</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="772" Name="GO_MI_LTTR_SLTTN_TXT" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_LTTR_SLTTN_TXT</Description>
    <Sample>GO MI Salutation Text                       </Sample>
  </Variable>
  <Variable VarOI="1450" Name="GO_MI_ARCHACK" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_ARCHACK</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="1427" Name="GO_MI_EDELACK" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_EDELACK</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="1451" Name="GO_MI_PRINTACK" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_PRINTACK</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="954" Name="GO_PI_CONTRACTS" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_CONTRACTS</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="9171" Name="LT_RmdYearEndBalanceAdjustment" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RmdYearEndBalanceAdjustment</Description>
    <Sample>666.66</Sample>
  </Variable>
  <Variable VarOI="9172" Name="LT_ExcludeGrandfatheredAmountIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExcludeGrandfatheredAmountIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="9706" Name="LT_RothTotalAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RothTotalAmount</Description>
    <Sample>111,111,111.11</Sample>
  </Variable>
  <Variable VarOI="8627" Name="LT_InServiceType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_InServiceType</Description>
    <Sample>InServiceType</Sample>
  </Variable>
  <Variable VarOI="4296" Name="FR_RecurringPaymentIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_RecurringPaymentIndicator</Description>
    <Sample>SWAT</Sample>
  </Variable>
  <Variable VarOI="2380" Name="GO_MI_FAX_NUMBER" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_FAX_NUMBER</Description>
    <Sample><EMAIL></Sample>
  </Variable>
  <Variable VarOI="822" Name="GO_PI_EXPORT_IND" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_EXPORT_IND</Description>
    <Sample>A</Sample>
  </Variable>
  <Variable VarOI="828" Name="GO_PI_TASK_ID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_TASK_ID</Description>
    <Sample>PI Task ID</Sample>
  </Variable>
  <Variable VarOI="829" Name="GO_PI_TASK_TYPE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_TASK_TYPE</Description>
    <Sample>PI Task Type</Sample>
  </Variable>
  <Variable VarOI="823" Name="GO_PI_TASK_GUID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_TASK_GUID</Description>
    <Sample>PI Task Guid</Sample>
  </Variable>
  <Variable VarOI="824" Name="GO_PI_ACTION_STEP" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_ACTION_STEP</Description>
    <Sample>PI Action Step</Sample>
  </Variable>
  <Variable VarOI="834" Name="GO_PI_DOC_CONTENT" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_DOC_CONTE</Description>
    <Sample>PI Doc Content</Sample>
  </Variable>
  <Variable VarOI="827" Name="GO_PI_TASK_STATUS" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_TASK_STATUS</Description>
    <Sample>A</Sample>
  </Variable>
  <Variable VarOI="833" Name="GO_PI_PLAN_ID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_PLAN_ID</Description>
    <Sample>PI Plan ID</Sample>
  </Variable>
  <Variable VarOI="826" Name="GO_PI_TIAA_TIME" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_TIAA_TIME</Description>
    <Sample>11:22:33</Sample>
  </Variable>
  <Variable VarOI="830" Name="GO_PI_SSN" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_SSN</Description>
    <Sample>ABCDEFGHI</Sample>
  </Variable>
  <Variable VarOI="831" Name="GO_PI_PIN_NPIN_PPG" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_PIN_NPIN_PPG</Description>
    <Sample>PI Pin Npin PPG</Sample>
  </Variable>
  <Variable VarOI="832" Name="GO_PI_PIN_TYPE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_PI_PIN_TYPE</Description>
    <Sample>A</Sample>
  </Variable>
  <Variable VarOI="5343" Name="LT_DeclineReasonText" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_DeclineReasonText</Description>
    <Sample>Decline Reason Text</Sample>
  </Variable>
  <Variable VarOI="4456" Name="LT_RequestOptionType" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>LT_RequestOptionType</Description>
    <Sample>S</Sample>
  </Variable>
  <Variable VarOI="4457" Name="LT_WithdrawalType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_WithdrawalType</Description>
    <Sample>C</Sample>
  </Variable>
  <Variable VarOI="4458" Name="LT_F402fOption" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>LT_F402fOption</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="4459" Name="LT_RelativeValueDisclosureOption" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>LT_RelativeValueDisclosureOption</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="6381" Name="LT_NewEnrollmentIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_NewEnrollmentIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="10551" Name="LT_LetterTransactionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_LetterTransactionType</Description>
    <Sample>Cash</Sample>
  </Variable>
  <Variable VarOI="10552" Name="LT_LetterDistributionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_LetterDistributionType</Description>
    <Sample>OneTime</Sample>
  </Variable>
  <Variable VarOI="774" Name="GO_MI_BIN_ITEMS" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>GO_MI_BIN_ITEMS</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="4276" Name="LT_PlanName" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PlanName</Description>
    <Sample>XYZ University</Sample>
  </Variable>
  <Variable VarOI="4628" Name="LT_Frequency" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_Frequency</Description>
    <Sample>Annual</Sample>
  </Variable>
  <Variable VarOI="4501" Name="LT_LastPaymentDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_LastPaymentDate</Description>
    <Sample>2012-11-01</Sample>
  </Variable>
  <Variable VarOI="4502" Name="LT_NetAmountOfLastPayment" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_NetAmountOfLastPayment</Description>
    <Sample>3333.33</Sample>
  </Variable>
  <Variable VarOI="5955" Name="LT_PaymentStopDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PaymentStopDate</Description>
    <Sample>2012-09-13</Sample>
  </Variable>
  <Variable VarOI="6467" Name="LT_OtpDeliveryMode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OtpDeliveryMode</Description>
    <Sample>M</Sample>
  </Variable>
  <Variable VarOI="8349" Name="LT_RecurringPhase" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RecurringPhase</Description>
    <Sample>Maintenance</Sample>
  </Variable>
  <Variable VarOI="4630" Name="LT_FirstPaymentDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_FirstPaymentDate</Description>
    <Sample>2012-09-01</Sample>
  </Variable>
  <Variable VarOI="6388" Name="LT_NumberOfPayments" Type="Integer" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>1234</Sample>
  </Variable>
  <Variable VarOI="8485" Name="LT_UntilFundsDepletedInd" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_UntilFundsDepletedInd</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="8403" Name="LT_FinalPaymentDate" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_FinalPaymentDate</Description>
    <Sample>2014-03-15</Sample>
  </Variable>
  <Variable VarOI="5387" Name="LT_SubsequentPaymentsDay" Type="Integer" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_SubsequentPaymentsDay</Description>
    <Sample>15</Sample>
  </Variable>
  <Variable VarOI="6399" Name="LT_ProductSubCode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ProductSubCode</Description>
    <Sample>PSub11</Sample>
  </Variable>
  <Variable VarOI="6469" Name="LT_RequestedPercentage" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RequestedPercentage</Description>
    <Sample>11.11</Sample>
  </Variable>
  <Variable VarOI="6206" Name="LT_TiaaIndexContractNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TiaaIndexContractNumber</Description>
    <Sample>T_INDEX-2</Sample>
  </Variable>
  <Variable VarOI="6406" Name="LT_RmdPlanTotal" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RmdPlanTotal</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6412" Name="LT_OptionalWithholdingAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OptionalWithholdingAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6415" Name="LT_TaxMaritalStatus" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxMaritalStatus</Description>
    <Sample>HH</Sample>
  </Variable>
  <Variable VarOI="6416" Name="LT_TaxExemptIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxExemptIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="6417" Name="LT_NumberofExemptions" Type="Integer" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_NumberofExemptions</Description>
    <Sample>1234</Sample>
  </Variable>
  <Variable VarOI="6418" Name="LT_TaxFlatDollarAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxFlatDollarAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6419" Name="LT_TaxFixedPercentage" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxFixedPercentage</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6428" Name="LT_TaxWithholdingAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxWithholdingAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6422" Name="LT_TaxFixedDollarAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxFixedDollarAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6434" Name="LT_RmdCalculationMethod" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RmdCalculationMethod</Description>
    <Sample>Uniform</Sample>
  </Variable>
  <Variable VarOI="6435" Name="LT_SpouseDateofBirth" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_SpouseDateofBirth</Description>
    <Sample>1980-08-13</Sample>
  </Variable>
  <Variable VarOI="6470" Name="LT_UniformLifeExpectancyFactor" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_UniformLifeExpectancyFactor</Description>
    <Sample>11.11</Sample>
  </Variable>
  <Variable VarOI="6471" Name="LT_JointLifeExpectancyFactor" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_JointLifeExpectancyFactor</Description>
    <Sample>22.22</Sample>
  </Variable>
  <Variable VarOI="6438" Name="LT_RmdTotalWithdrawalPayments" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RmdTotalWithdrawalPayments</Description>
    <Sample>121212.12</Sample>
  </Variable>
  <Variable VarOI="6439" Name="LT_RmdPriorYearGrandfatheredAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RmdPriorYearGrandfatheredAmount</Description>
    <Sample>343434343434.34</Sample>
  </Variable>
  <Variable VarOI="6440" Name="LT_RmdPriorYearEndAccumulation" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RmdPriorYearEndAccumulation</Description>
    <Sample>**********.56</Sample>
  </Variable>
  <Variable VarOI="10553" Name="LT_GfAccumulationEligibilityIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_GfAccumulationEligibilityIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="3175" Name="LT_BankName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_BankName</Description>
    <Sample>Bank Name</Sample>
  </Variable>
  <Variable VarOI="3174" Name="LT_BankTransitNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_BankTransitNumber</Description>
    <Sample>Transit Number</Sample>
  </Variable>
  <Variable VarOI="3173" Name="LT_BankAccountNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_BankAccountNumber</Description>
    <Sample>Bank Account Number</Sample>
  </Variable>
  <Variable VarOI="6442" Name="LT_BankRoutingNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_BankRoutingNumber</Description>
    <Sample>BRN-********</Sample>
  </Variable>
  <Variable VarOI="4635" Name="LT_BankAccountType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_BankAccountType</Description>
    <Sample>C</Sample>
  </Variable>
  <Variable VarOI="6443" Name="LT_ExternalRolloverPlanType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExternalRolloverPlanType</Description>
    <Sample>ExtRollType</Sample>
  </Variable>
  <Variable VarOI="3181" Name="LT_ExternalCompanyName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExternalCompanyName</Description>
    <Sample>External Company Name</Sample>
  </Variable>
  <Variable VarOI="6444" Name="LT_ExternalCompanyAddressLine1" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExternalCompanyAddressLine1</Description>
    <Sample>External Company Address Line 1</Sample>
  </Variable>
  <Variable VarOI="6445" Name="LT_ExternalCompanyAddressLine2" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExternalCompanyAddressLine2</Description>
    <Sample>External Company Address Line 2</Sample>
  </Variable>
  <Variable VarOI="4282" Name="LT_ExternalCompanyAccountNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExternalCompanyAccountNumber</Description>
    <Sample>Ext. Co. Acct. 123</Sample>
  </Variable>
  <Variable VarOI="6446" Name="LT_City" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_City</Description>
    <Sample>New York City</Sample>
  </Variable>
  <Variable VarOI="6447" Name="LT_State" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_State</Description>
    <Sample>New York</Sample>
  </Variable>
  <Variable VarOI="6448" Name="LT_ZipCode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ZipCode</Description>
    <Sample>10017-1234</Sample>
  </Variable>
  <Variable VarOI="6455" Name="LT_RolloverType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RolloverType</Description>
    <Sample>Internal</Sample>
  </Variable>
  <Variable VarOI="6456" Name="LT_NewEnrollmentInRollover" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_NewEnrollmentInRollover</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="6457" Name="LT_RolledOverPlanName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RolledOverPlanName</Description>
    <Sample>Rolled Over Plan Name</Sample>
  </Variable>
  <Variable VarOI="6458" Name="LT_RolledOverTiaaContractNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RolledOverTiaaContractNumber</Description>
    <Sample>RO111111-1</Sample>
  </Variable>
  <Variable VarOI="6459" Name="LT_InvestmentOption" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_InvestmentOption</Description>
    <Sample>C</Sample>
  </Variable>
  <Variable VarOI="6474" Name="LT_CarrierName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_CarrierName</Description>
    <Sample>Mr. Carrier Name</Sample>
  </Variable>
  <Variable VarOI="6475" Name="LT_CarrierAddressLines" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>LT_CarrierAddressLines</Description>
    <Sample>Carrier Address Line 1</Sample>
  </Variable>
  <Variable VarOI="6461" Name="LT_OtherAccountName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OtherAccountName</Description>
    <Sample>Other Account Name</Sample>
  </Variable>
  <Variable VarOI="6462" Name="LT_OtherAccountNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OtherAccountNumber</Description>
    <Sample>OANbr-********</Sample>
  </Variable>
  <Variable VarOI="6464" Name="LT_OtherAccountType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OtherAccountType</Description>
    <Sample>OAType</Sample>
  </Variable>
  <Variable VarOI="7945" Name="LT_IvcOption" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_IvcOption</Description>
    <Sample>I</Sample>
  </Variable>
  <Variable VarOI="6373" Name="LT_EmployerName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_EmployerName</Description>
    <Sample>Employer Name in Personal Info Section</Sample>
  </Variable>
  <Variable VarOI="4460" Name="LT_SpousalWaiverOption" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>LT_SpousalWaiverOption</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="4548" Name="LT_SponsorIndicator" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>LT_SponsorIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="4546" Name="LT_MarriedUnmarriedIndicator" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_MarriedUnmarriedIndicator</Description>
    <Sample>M</Sample>
  </Variable>
  <Variable VarOI="5047" Name="LT_RecurringPaymentIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RecurringPaymentIndicator</Description>
    <Sample>SWAT</Sample>
  </Variable>
  <Variable VarOI="8626" Name="LT_MaritalStatusValidation" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_MaritalStatusValidation</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="10083" Name="LT_PrismSlaInd" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PrismSlaInd</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="10084" Name="LT_PrismSponsorApprovalDays" Type="Integer" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PrismSponsorApprovalDays</Description>
    <Sample>5</Sample>
  </Variable>
  <Variable VarOI="10085" Name="LT_PrismSponsorApprovalExtDays" Type="Integer" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PrismSponsorApprovalExtDays</Description>
    <Sample>10</Sample>
  </Variable>
  <Variable VarOI="6371" Name="LT_TransactionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TransactionType</Description>
    <Sample>Cash</Sample>
  </Variable>
  <Variable VarOI="6372" Name="LT_DistributionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_DistributionType</Description>
    <Sample>OneTime</Sample>
  </Variable>
  <Variable VarOI="6374" Name="LT_GrossAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_GrossAmount</Description>
    <Sample>********11111.11</Sample>
  </Variable>
  <Variable VarOI="6376" Name="LT_Fees" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_Fees</Description>
    <Sample>2222222222222.22</Sample>
  </Variable>
  <Variable VarOI="6377" Name="LT_EstimatedTaxes" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_EstimatedTaxes</Description>
    <Sample>********33333.33</Sample>
  </Variable>
  <Variable VarOI="6378" Name="LT_NetWithdrawal" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_NetWithdrawal</Description>
    <Sample>4444444444444.44</Sample>
  </Variable>
  <Variable VarOI="6382" Name="LT_DataValidationRequiredIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_DataValidationRequiredIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="6383" Name="LT_PendingOtpIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PendingOtpIndicator</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="6384" Name="LT_MailingAddressIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_MailingAddressIndicator</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="6385" Name="LT_EmailAddressIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_EmailAddressIndicator</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="9170" Name="LT_EdeliveryPreferenceIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_EdeliveryPreferenceIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="6397" Name="LT_EffectiveDateofDistribution" Type="Date" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_EffectiveDateofDistribution</Description>
    <Sample>2013-10-01</Sample>
  </Variable>
  <Variable VarOI="6424" Name="LT_AnnualizedRequestAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_AnnualizedRequestAmount</Description>
    <Sample>********.11</Sample>
  </Variable>
  <Variable VarOI="6425" Name="LT_FrequencyPaymentAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_FrequencyPaymentAmount</Description>
    <Sample>2222.222</Sample>
  </Variable>
  <Variable VarOI="6398" Name="LT_ProductCode" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ProductCode</Description>
    <Sample>PC111</Sample>
  </Variable>
  <Variable VarOI="6468" Name="LT_RequestedAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RequestedAmount</Description>
    <Sample>$11111.11</Sample>
  </Variable>
  <Variable VarOI="4461" Name="LT_TiaaNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TiaaNumber</Description>
    <Sample>T11111-1</Sample>
  </Variable>
  <Variable VarOI="6207" Name="LT_IraIndexIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_IraIndexIndicator</Description>
    <Sample>B</Sample>
  </Variable>
  <Variable VarOI="4462" Name="LT_CrefNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_CrefNumber</Description>
    <Sample>C11111-1</Sample>
  </Variable>
  <Variable VarOI="6400" Name="LT_SettlementSelectionType" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_SettlementSelectionType</Description>
    <Sample>Maximum</Sample>
  </Variable>
  <Variable VarOI="6403" Name="LT_AfterTaxAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_AfterTaxAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6405" Name="LT_TaxableAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxableAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6407" Name="LT_TotalAmountRequested" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TotalAmountRequested</Description>
    <Sample>*********.99</Sample>
  </Variable>
  <Variable VarOI="6409" Name="LT_TaxAnnualizedAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxAnnualizedAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="7969" Name="LT_TaxFrequencyAmount" Type="Currency" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxFrequencyAmount</Description>
    <Sample>$123,456.78</Sample>
  </Variable>
  <Variable VarOI="6411" Name="LT_OmniTaxAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OmniTaxAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6413" Name="LT_OptionalWithholdingPercentage" Type="Float" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_OptionalWithholdingPercentage</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="8095" Name="LT_TaxFrequencyAmountFedAndState" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxFrequencyAmountFedAndState</Description>
    <Sample>12345678.90</Sample>
  </Variable>
  <Variable VarOI="6414" Name="LT_TaxOptOutIndicator" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TaxOptOutIndicator</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="6423" Name="LT_TotalTaxWithheldAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TotalTaxWithheldAmount</Description>
    <Sample>*********1234.12</Sample>
  </Variable>
  <Variable VarOI="6433" Name="LT_TotalTaxFrequencyAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_TotalTaxFrequencyAmount</Description>
    <Sample>$*********1.12</Sample>
  </Variable>
  <Variable VarOI="6441" Name="LT_PaymentMethod" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_PaymentMethod</Description>
    <Sample>EFT</Sample>
  </Variable>
  <Variable VarOI="3191" Name="LT_SendAddress" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description>LT_SendAddress</Description>
    <Sample>Send Address Line</Sample>
  </Variable>
  <Variable VarOI="7518" Name="LT_IncludeIvcAmount" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_IncludeIvcAmount</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="8001" Name="LT_ExcludedIvcAmount" Type="Currency" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_ExcludedIvcAmount</Description>
    <Sample>111111.11</Sample>
  </Variable>
  <Variable VarOI="3560" Name="FR_TiaaNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_TiaaNumber</Description>
    <Sample>T11111-1</Sample>
  </Variable>
  <Variable VarOI="3561" Name="FR_CrefNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_CrefNumber</Description>
    <Sample>C22222-2</Sample>
  </Variable>
  <Variable VarOI="3564" Name="FR_SubplanId" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>FR_SubplanId</Description>
    <Sample>S12345</Sample>
  </Variable>
  <Variable VarOI="9705" Name="LT_RothHeaderInd" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>LT_RothHeaderInd</Description>
    <Sample> - SUMMARY</Sample>
  </Variable>
  <Variable VarOI="14975" Name="FR_AccountOwnerAddressLines" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Address</Sample>
  </Variable>
  <Variable VarOI="18699" Name="FR_GrantorName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6666MR TEST B ACCOUNT</Sample>
  </Variable>
  <Variable VarOI="18700" Name="FR_Grantor_Prefix" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>MR</Sample>
  </Variable>
  <Variable VarOI="18705" Name="FR_Grantor_Email" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample><EMAIL></Sample>
  </Variable>
  <Variable VarOI="18706" Name="FR_Grantor_Gender" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Female</Sample>
  </Variable>
  <Variable VarOI="18708" Name="FR_Grantor_MaritalStatus" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Mard</Sample>
  </Variable>
  <Variable VarOI="18709" Name="FR_Grantor_BusinessPhone" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>**********</Sample>
  </Variable>
  <Variable VarOI="18714" Name="FR_Grantor_ResCountry" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>US</Sample>
  </Variable>
  <Variable VarOI="18715" Name="FR_JointGrantor_FullName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>6666MR TEST B ACCOUNT</Sample>
  </Variable>
  <Variable VarOI="18716" Name="FR_JointGrantor_Prefix" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>MR</Sample>
  </Variable>
  <Variable VarOI="18721" Name="FR_JointGrantor_Email" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample><EMAIL></Sample>
  </Variable>
  <Variable VarOI="18722" Name="FR_JointGrantor_Gender" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Female</Sample>
  </Variable>
  <Variable VarOI="18724" Name="FR_JointGrantor_MaritalStatus" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>Mard</Sample>
  </Variable>
  <Variable VarOI="18725" Name="FR_JointGrantor_BusinessPhone" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>**********</Sample>
  </Variable>
  <Variable VarOI="18730" Name="FR_JointGrantor_ResCountry" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>US</Sample>
  </Variable>
  <Variable VarOI="15441" Name="FR_BAO_EmployerCountry" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="Yes">
    <Description/>
    <Sample>String</Sample>
  </Variable>
  <Variable VarOI="762" Name="GO_DR_BATCH_TOTAL" Type="Integer" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_BATCH_TOTAL</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="767" Name="GO_MI_SSN" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_SSN</Description>
    <Sample>111223333</Sample>
  </Variable>
  <Variable VarOI="1426" Name="GO_MI_RETURN_DOC_TYPE" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_RETURN_DOC_TYPE</Description>
    <Sample>D</Sample>
  </Variable>
  <Variable VarOI="799" Name="ECS_01_OutputPathforResources" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>This string variable is hardcoded or set at runtime with the path where the required images reside</Description>
    <Sample>/app/exstream/ecs/images</Sample>
  </Variable>
  <Variable VarOI="19461" Name="FR_BAO_PassThrough_LiquidInsurance_TC_SD" Type="PDF Pass-Through" Source="User value" ResetTime="No reset" Access="Any" IsArray="Yes">
    <Description/>
    <Sample/>
  </Variable>
  <Variable VarOI="1573" Name="GO_01_OutputPathforEXTFILES" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>GO_01_OutputPathforEXTFILES.  Value is a VARSET</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="17977" Name="FR_01_BAO_EXT_BUCKSLIP_PDFFiles_PLACEHOLDER" Type="PDF Pass-Through" Source="User value" ResetTime="Before section ApplicationForm" Access="Any" IsArray="No">
    <Description>FR_01_BAO_EXT_BUCKSLIP_PDFFiles_PLACEHOLDER</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="111" Name="SYS_PostSort" Type="Boolean" Source="System" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>True if running post-sort, False if running pre-sort.</Description>
    <Sample>T</Sample>
  </Variable>
  <Variable VarOI="806" Name="ECS_01_OutputPathforTIFF" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>This variable should be set by the VARSET command in each Control file.  It specificies the path where generated TIFF files shall go</Description>
    <Sample>/app/exstream/ecs/output</Sample>
  </Variable>
  <Variable VarOI="811" Name="ECS_01_OutputPathforPDF" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>This variable should be set by the VARSET command in each Control file.  It specificies the path where generated PDFs shall go</Description>
    <Sample>/app/exstream/ecs/output</Sample>
  </Variable>
  <Variable VarOI="813" Name="ECS_01_OutputPathforPS" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>This variable should be set by the VARSET command in each Control file.  It specificies the path where generated Postscript files shall go</Description>
    <Sample>/app/exstream/ecs/output</Sample>
  </Variable>
  <Variable VarOI="985" Name="GO_01_PS_QUEUE_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="987" Name="GO_01_TIFF_QUEUE_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="1436" Name="GO_01_ARCHPDF_QUEUE_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="1440" Name="GO_01_EDELPDF_QUEUE_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="1454" Name="GO_01_DCS_ReqType" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>VARSET GO_01_DCS_ReqType. Used for DWS Server</Description>
    <Sample>DWS</Sample>
  </Variable>
  <Variable VarOI="805" Name="ECS_01_IndivFileNameTIFF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production TIFF Queues for generating a unique, meaningful named .tiff file for each customer.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="812" Name="ECS_01_IndivFileNamePS" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production Postscript Queues for generating a unique, meaningful named PS file for each customer.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="816" Name="ECS_01_OneFileNamePDF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production PDF Queues for generating one file for each PDF run.  It is customzied within each application.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="817" Name="ECS_01_OneFileNamePS" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production PS Queues for generating one file for each PS print run.  It is customized  within each application.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="982" Name="ECS_01_OneFileNameTIFF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production TIFF Queues for generating one file for each run.  It is customzied within each application.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="1433" Name="ECS_01_OneFileNameArchPDF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production Arch PDF Queues for generating one file for each PDF run.  It is customzied within each application.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="1437" Name="GO_AR_FILE_NAME_DELTYPE" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
    <Formula Compute="As-needed">VALUE = LEFT(GO_AR_FILE_NAME,35)
</Formula>
  </Variable>
  <Variable VarOI="1438" Name="ECS_01_OneFileNameEdelPDF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production Edel PDF Queues for generating one file for each PDF run.  It is customzied within each application.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="1439" Name="GO_01_PDF_Edel_Queue_Application_Flag" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>APP Level Queue Rule variable. App will enable/disable a particular "TYPE" of Queue by setting this flag.</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="1445" Name="ECS_01_IndivFileNameArchPDF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production PDF Queues for generating a unique, meaningful named PDF file for each customer.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="1446" Name="ECS_01_IndivFileNameEdelPDF" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>This will be used by all production PDF Queues for generating a unique, meaningful named PDF file for each customer.</Description>
    <Sample>/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf</Sample>
  </Variable>
  <Variable VarOI="17727" Name="FR_02_LogoPlaceholder_JPG_IRA_eApp" Type="JPEG Pass-Through Image" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>The Master Formula for an app will concatenate the variable file path with the filename for the proper Logo</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="871" Name="GO_01_GroupPS_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Group PS Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="872" Name="GO_01_GroupPDF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="986" Name="GO_01_PDF_QUEUE_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.</Description>
    <Sample>1</Sample>
  </Variable>
  <Variable VarOI="988" Name="GO_01_GroupTIFF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Group TIFF Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="989" Name="GO_01_IndivPDF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="990" Name="GO_01_IndivPS_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description/>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="991" Name="GO_01_IndivTIFF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Individual TIFF Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="993" Name="GO_01_PDF_Queue_Application_Flag" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>APP Level Queue Rule variable. App will enable/disable a particular "TYPE" of Queue by setting this flag.</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="994" Name="GO_01_PS_Queue_Application_Flag" Type="String" Source="User value" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>APP Level Queue Rule variable. App will enable/disable a particular "TYPE" of Queue by setting this flag.</Description>
    <Sample>N</Sample>
  </Variable>
  <Variable VarOI="1441" Name="GO_01_GroupArchPDF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Group Arch PDF Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="1442" Name="GO_01_GroupEdelPDF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Group Edel PDF Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="1443" Name="GO_01_IndivArchPDF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Individual Arch PDF Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="1444" Name="GO_01_IndivEdelPDF_Flag" Type="String" Source="User value" ResetTime="No reset" Access="Any" IsArray="No">
    <Description>Individual Edel PDF Enable/Disable Flag. Used in Queue Rules.</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="1040" Name="CF_FromTiaaNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>CF_FromTiaaNumber</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="1042" Name="CF_FromCrefNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>CF_FromCrefNumber</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="2411" Name="GO_01_MOB_OrchestrationID" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_OrchestrationID</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_DR_ORCHESTRATION_ID &lt;&gt; '' THEN
VALUE = GO_DR_ORCHESTRATION_ID ELSE
VALUE = ' '
ENDIF

</Formula>
  </Variable>
  <Variable VarOI="3622" Name="GO_DR_TRANSACTION_REQ_ID" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_TRANSACTION_REQ_ID</Description>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="4178" Name="GO_01_MOB_Composite_OrchestrationID" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Composite_OrchestrationID</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_DR_COMPOSITE_ORCH_ID &lt;&gt; '' THEN
   VALUE = GO_DR_COMPOSITE_ORCH_ID
ELSE
   VALUE = ' '
ENDIF

</Formula>
  </Variable>
  <Variable VarOI="4370" Name="GO_01_MOB_Bus_Code" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Bus_Code</Description>
    <Sample/>
    <Formula Compute="As-needed">IF COMMON.GO_DR_BUSINESS_UNIT_CODE &lt;&gt; '' THEN
VALUE = COMMON.GO_DR_BUSINESS_UNIT_CODE ELSE
VALUE = ' ' 
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="4371" Name="GO_01_MOB_Doc_Code" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_DocCode</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">IF GO_DR_DOC_CODE &lt;&gt; '' THEN
VALUE = GO_DR_DOC_CODE ELSE
VALUE = ' ' 
ENDIF

</Formula>
  </Variable>
  <Variable VarOI="4372" Name="GO_01_MOB_Doc_Version" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Doc_Version</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">IF GO_DR_VERSION &lt;&gt; '' THEN
VALUE = GO_DR_VERSION ELSE
VALUE = ' ' 
ENDIF


</Formula>
  </Variable>
  <Variable VarOI="5352" Name="CF_FromTiaaNumberClosed" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>CF_FromTiaaNumberClosed</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="5353" Name="CF_FromTiaaNumberOpen" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>CF_FromTiaaNumberOpen</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="5451" Name="GO_01_MOB_ConFirstName" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_ConFirstName</Description>
    <Sample/>
    <Formula Compute="As-needed">IF OA_FirstName &lt;&gt; '' THEN
VALUE = OA_FirstName
ELSEIF RPPM_FirstName &lt;&gt; '' THEN
VALUE = RPPM_FirstName 
ELSEIF IL_Consultant_FirstName &lt;&gt; '' THEN
VALUE = IL_Consultant_FirstName
ELSE
VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="5492" Name="GO_01_MOB_ConLastName" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_ConLastName</Description>
    <Sample/>
    <Formula Compute="As-needed">IF OA_LastName &lt;&gt; '' THEN
VALUE = OA_LastName 
ELSEIF RPPM_LastName &lt;&gt; '' THEN
VALUE = RPPM_LastName
ELSEIF IL_Consultant_LastName &lt;&gt; '' THEN
VALUE = IL_Consultant_LastName 
ELSE
VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="15365" Name="GO_01_MOB_Unique_Key" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Unique_Key</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_DR_UNIQUE_KEY &lt;&gt; '' THEN
VALUE = GO_DR_UNIQUE_KEY ELSE
VALUE = ' ' 
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="15456" Name="CF_SubPlanId" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>55555</Sample>
  </Variable>
  <Variable VarOI="15457" Name="CF_Apin" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>44444</Sample>
  </Variable>
  <Variable VarOI="15458" Name="CF_BulkTradeBatchNum" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>77</Sample>
  </Variable>
  <Variable VarOI="16069" Name="FR_BAO_ProposalNumber" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Traditional IRA</Sample>
  </Variable>
  <Variable VarOI="16954" Name="GO_01_MOB_TIAAID" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_TIAAID</Description>
    <Sample/>
    <Formula Compute="As-needed">IF QD_TIAAID &lt;&gt; '' THEN
VALUE = QD_TIAAID
ELSE
VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="17617" Name="GO_DELIVERY_METHOD" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DELIVERY_METHOD</Description>
    <Sample>*********</Sample>
  </Variable>
  <Variable VarOI="18325" Name="GO_01_MOB_CustNum" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_CustNum</Description>
    <Sample/>
    <Formula Compute="As-needed">IF (GO_MI_PCKGE_CDE = 'PTRKIACK' OR GO_MI_PCKGE_CDE = 'ANNCONST')THEN
      IF GO_MI_UniversalType = 'C' THEN
VALUE = GO_MI_BC_PIN_NBR
     ELSE
	VALUE = ' '
     ENDIF
ELSE
     VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1141" Name="GO_01_MOB_DocTypeID" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_DocTypeID</Description>
    <Sample>DCSD1234ST</Sample>
    <Formula Compute="Customer(Don't Compute)">IF GO_AR_RPT_ID = "PTRKINCILL" THEN
   Value = GO_AR_RPT_ID
ELSEIF GO_MI_ARCHIVAL_IND = "I" AND GO_MI_PCKGE_CDE &lt;&gt; "ALTERNATECARRIER"  THEN
   VALUE = Left(GO_AR_RPT_ID,  (Len(GO_AR_RPT_ID) - 1) ) &amp; "W"
ELSEIF 
   (GO_AR_RPT_ID &lt;&gt; '' OR  GO_MI_ARCHIVAL_IND &lt;&gt; "N") OR 
     (GO_MI_PCKGE_CDE = "ALTERNATECARRIER")
THEN
   VALUE = Left(GO_AR_RPT_ID,  (Len(GO_AR_RPT_ID) - 1) ) &amp; "M"
ELSE
   VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1143" Name="GO_01_MOB_Email_TemplateID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Email_TemplateID</Description>
    <Sample>PTRKTREJ</Sample>
  </Variable>
  <Variable VarOI="1144" Name="GO_01_MOB_Delivery_Type" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Delivery_Type</Description>
    <Sample>PR</Sample>
    <Formula Compute="As-needed">//11/20/13 ¿ updated by Brandon Nielsen and E. Schaffer to 
//include QDIA logic, forcing to PH.
//06/13/14  updated by Russell Squire.  added condition for ACISLGLBA, and forcing to PH also
//11/20/14 updated by Brandon Nielsen PARTNOTIF to to be included in the below logic.
//12/19/17 updated by Arnab Pal: PARKIACK and ANNCONST to to be included in the below logic.
//2/8/18 updated by Arnab Pal: UW_ADHOC_MAILING to be included in the below logic for FRD Adhoc Mailing.

IF GO_MI_PCKGE_CDE = "QDIA" OR GO_MI_PCKGE_CDE = "PARTNOTIF" OR (GO_MI_PCKGE_CDE = "ACISLGLBA" AND GO_MI_PCKGE_DLVRY_TYP = "A") OR GO_MI_PCKGE_CDE = "ANNCONST" OR GO_MI_PCKGE_CDE = "PTRKIACK" OR GO_MI_PCKGE_CDE = "UW_ADHOC_MAILING" OR GO_MI_PCKGE_CDE = "IAMONTHLYEFTS" THEN
   VALUE = "PH"
ELSE
	IF GO_OVERRIDE_NON_PRINT_DELIVERY_TYPE="EMAIL" THEN
		IF (GO_MI_PCKGE_DLVRY_TYP &lt;&gt; "S" AND
		 GO_MI_PCKGE_DLVRY_TYP &lt;&gt; "O"  AND
		 GO_MI_PCKGE_DLVRY_TYP &lt;&gt; "R") THEN
		VALUE = 'PH' 
	ELSE
		VALUE = 'PR'
	ENDIF
	ELSE
	   IF GO_MI_PCKGE_DLVRY_TYP = "E" or GO_MI_PCKGE_DLVRY_TYP = "EMAIL" THEN 
			VALUE = 'PH'  
	   ELSE
			VALUE = 'PR'
	  ENDIF
	ENDIF
ENDIF 

IF GO_MI_PCKGE_CDE = "PTRKLONI" THEN
   GO_DELIVERY_METHOD = ''
   IF GO_MI_PCKGE_DLVRY_TYP = "S" THEN
      GO_DELIVERY_METHOD = 'PR'
   ENDIF
   IF GO_MI_PCKGE_DLVRY_TYP = "E" THEN
      GO_DELIVERY_METHOD = 'ED'
   ENDIF
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1150" Name="GO_01_MOB_Seq_Number" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Seq_Number</Description>
    <Sample/>
    <Formula Compute="As-needed">//B Nielsen 2/24/15. Adding logic around CONFXFRALLOC ID so that it pulls in all 7 bytes instead of 5 from the DRI to eliminate duplicates.


//Russell Squire 3/24/16 commenting out DCS logic, and adding CCP logic
//IF GO_MI_PST_RQST_ID &lt;&gt; '' THEN
//   IF GO_MI_PCKGE_CDE="CONFXFRALLOC" THEN
//	VALUE = Mid(GO_MI_PST_RQST_ID, 9, 12) &amp; RIGHT(GO_MI_PST_RQST_ID, 7)
//   ELSE
//	VALUE = Mid(GO_MI_PST_RQST_ID, 9, 12) &amp; RIGHT(GO_MI_PST_RQST_ID, 5)
//   ENDIF
//ELSE
//   VALUE = ' '
//ENDIF 

//rjl - 0602023 added this for onetime generic passthrough for mobius archive.
IF GO_MI_PCKGE_CDE = 'ONETIME_GENERIC_PASSTHROUGH' THEN
   value = Format( SYS_CustomerInRun, "0000000")
ELSE
   value=GO_DR_BATCH_COUNTER 
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1151" Name="GO_01_MOB_Pin_Number" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_Mobius_Data_Header</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_01_MOB_PIN_OVERRIDE = '{PINOVERRIDE}' OR GO_01_MOB_PIN_OVERRIDE = 'PINOVERRIDE' THEN
        GO_01_MOB_PIN_OVERRIDE = ''
ENDIF

IF GO_MI_BC_PIN_NBR &lt;&gt; '' THEN
      IF GO_01_MOB_PIN_OVERRIDE &lt;&gt; '' THEN
		VALUE = GO_01_MOB_PIN_OVERRIDE  &amp; GO_MI_BC_PIN_NBR
      	ELSE IF (GO_MI_PCKGE_CDE = 'PTRKIACK' OR GO_MI_PCKGE_CDE = 'ANNCONST')THEN
		IF GO_MI_UniversalType = 'C' THEN
		VALUE = '0000000'
		ELSE VALUE = GO_MI_BC_PIN_NBR
     		ENDIF
    	     ELSE VALUE = GO_MI_BC_PIN_NBR
		ENDIF
	ENDIF
ELSE
     VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1152" Name="GO_01_MOB_Email" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Email</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_MI_ALT_DLVRY_ADDR &lt;&gt; '' THEN
VALUE = GO_MI_ALT_DLVRY_ADDR ELSE
VALUE = ' ' 
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1153" Name="GO_01_MOB_Prefix" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Prefix</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">IF GO_MI_PREFIX &lt;&gt; '' THEN
VALUE = GO_MI_PREFIX ELSE
VALUE = ' '
ENDIF


</Formula>
  </Variable>
  <Variable VarOI="1154" Name="GO_01_MOB_Last_Name" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Last_Name</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_MI_LAST_NAME &lt;&gt; '' THEN
VALUE =  GO_MI_LAST_NAME ELSE
VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1155" Name="GO_01_MOB_First_Name" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_First_Name</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_MI_FIRST_NAME &lt;&gt; '' THEN
VALUE = GO_MI_FIRST_NAME ELSE
VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1156" Name="GO_01_MOB_Middle_Name" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Middle_Name</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_MI_MID_NAME &lt;&gt; '' THEN
VALUE =GO_MI_MID_NAME ELSE
VALUE = ' '
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="1157" Name="GO_01_MOB_Suffix" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample/>
    <Formula Compute="As-needed">If Trim(GO_MI_SUFFIX) = '' Then
	Value = ' '
Else
	Value = GO_MI_SUFFIX
EndIf
</Formula>
  </Variable>
  <Variable VarOI="1205" Name="GO_01_MOB_Approved_Flag" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Approved_Flag</Description>
    <Sample>Y</Sample>
  </Variable>
  <Variable VarOI="1230" Name="GO_01_MOB_DocID" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_DocID</Description>
    <Sample/>
    <Formula Compute="Customer(Don't Compute)">VALUE = GO_DDA_DOC_REQ_ID 


</Formula>
  </Variable>
  <Variable VarOI="1276" Name="GO_01_MOB_PlanID" Type="String" Source="Formula" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>GO_01_MOB_PlanID</Description>
    <Sample/>
    <Formula Compute="As-needed">IF GO_MI_PLAN_ID &lt;&gt; '' THEN
VALUE = GO_MI_PLAN_ID ELSE
VALUE = ' '
ENDIF

</Formula>
  </Variable>
  <Variable VarOI="1277" Name="GO_01_MOB_Bus_Date" Type="Date" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Bus_Date</Description>
    <Sample/>
    <Formula Compute="As-needed">IF ISNULL(GO_MI_BUSINESS_DATE) THEN
VALUE = DateValue(' ') ELSE
VALUE = GO_MI_BUSINESS_DATE
ENDIF

</Formula>
  </Variable>
  <Variable VarOI="1756" Name="GO_01_MOB_Descr" Type="String" Source="Formula" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Descr</Description>
    <Sample>Fee Confirmation Statement</Sample>
    <Formula Compute="As-needed">IF GO_MI_PORTAL_DOC_DESC &lt;&gt; '' THEN
VALUE = GO_MI_PORTAL_DOC_DESC ELSE
VALUE = GO_01_MOB_Doc_Descr
ENDIF
</Formula>
  </Variable>
  <Variable VarOI="739" Name="GO_AR_RPT_ID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>Audit Report</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="5389" Name="OA_FirstName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>OA_FirstName</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="10006" Name="RPPM_FirstName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>OA_FirstName</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="16949" Name="IL_Consultant_FirstName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>John</Sample>
  </Variable>
  <Variable VarOI="5392" Name="OA_LastName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>OA_LastName</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="10008" Name="RPPM_LastName" Type="String" Source="File" ResetTime="Before each customer" Access="Any" IsArray="No">
    <Description>OA_LastName</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="16950" Name="IL_Consultant_LastName" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description/>
    <Sample>Doe</Sample>
  </Variable>
  <Variable VarOI="15417" Name="GO_DR_UNIQUE_KEY" Type="String" Source="File" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_DR_UNIQUE_KEY</Description>
    <Sample>831035185603</Sample>
  </Variable>
  <Variable VarOI="16953" Name="QD_TIAAID" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>new field for MOB02 key</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="1313" Name="GO_MI_UniversalType" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_MI_UniversalType</Description>
    <Sample>001001201103251658000000001</Sample>
  </Variable>
  <Variable VarOI="5393" Name="GO_OVERRIDE_NON_PRINT_DELIVERY_TYPE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_OVERRIDE_NON_PRINT_DELIVERY_TYPE</Description>
    <Sample>EMAIL</Sample>
  </Variable>
  <Variable VarOI="18308" Name="GO_01_MOB_PIN_OVERRIDE" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>used to add a prefix to pin</Description>
    <Sample/>
  </Variable>
  <Variable VarOI="1142" Name="GO_01_MOB_Doc_Descr" Type="String" Source="User value" ResetTime="Automatically" Access="Any" IsArray="No">
    <Description>GO_01_MOB_Doc_Descr</Description>
    <Sample>Transfer Reject Letters for Open Plan Solutions</Sample>
  </Variable>
</VariableList>
