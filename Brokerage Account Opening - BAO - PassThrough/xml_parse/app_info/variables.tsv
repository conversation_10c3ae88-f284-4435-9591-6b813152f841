VarOI	Name	Type	Source	ResetTime	Access	IsArray	Sample	Description
22	SYS_LanguageCustomer	String	System	Automatically	Any	No		The name of the current customer's language. This is used to pick the correct language-version of pages and messages and to obtain names of days and months.
23	SYS_LocaleCustomer	String	System	Automatically	Any	No		The name of the current customer's locale.  This is used to determine the type of formatting for numeric, currency, and dates.
17	SYS_CustomerEffectiveDate	Date	System	Automatically	Any	No	December 18, 2007	The as-of date that documents should be created for this customer (used only for multi-version packages).
26	SYS_CustInvalidDataLevel	Integer	System	Automatically	Any	No	11	The maximum invalid data level for the current customer, normally based on the action set in the variable's validation:  0= no invalid data, 1=continue (set default), 2=error, 10=skip customer.
21814	BAO_Captiva_2D_Barcode_flag	String	User value	Before section ApplicationForm	Any	No	Y	
19463	FR_00_EXT_PDFFiles_MasterFormula_LiquidInsure_TC_SD	String	Formula	Automatically	Any	No		
19459	FR_00_EXT_PDFFiles_MasterFormula_LiquidInsure_TC	String	Formula	Automatically	Any	No		
21792	FR_00_EXT_PDFFiles_MasterFormula_BrokerageSweep_TC	String	Formula	Automatically	Any	No		
19458	FR_00_EXT_PDFFiles_MasterFormula_BankSweep_TC	String	Formula	Automatically	Any	No		
16031	FR_00_EXT_PDFFiles_MasterFormula	String	Formula	Automatically	Any	No		FR_00_EXT_PDFFiles_MasterFormula_rjk
1447	GO_00_GLOBAL_AuditReport_FileMappings	String	Formula	No reset	Any	No		Engine Level GLOBAL  Formula for dynamically setting all  AUDITREPORT file mappings/ file names.
795	GO_DDA_Master1	String	Formula	Before each customer	Any	No		Customer Level DDA variable for tracking missing documents. Posts BEGIN tag.
796	GO_DDA_Master2	String	Formula	Before each customer	Any	No		Customer Level DDA variable for tracking missing documents. Posts  END  tag.
1432	GO_00_Application_MASTER_FORMULA	String	Formula	Before each customer	Any	No		Customer level GLOBL formula for setting filenames & queue selections.
804	GO_00_GLOBAL_Logo_Placeholder_NO_Address_TIFF	String	Formula	Before each customer	Any	No		NO ADDRESS TIFF:  Customer Level Global MASTER Variable for calculating  TIFF Image placeholder variable value.  Multiple numbered vars are created for use in applications where more than one type of logo is used in same document. See for eg:  ACS
17725	GO_00_GLOBAL_Logo_Placeholder_NO_Address_Master_Formula_IRA	String	Formula	Before each customer	Any	No		GO_00_GLOBAL_Logo_Placeholder_NO_Address_Master_Formula
1434	GO_00_GLOBAL_Queue_Selections	String	Formula	No reset	Any	No		Engine Level Global formula for selecting various queues based on script file settings
8341	GO_BNG_ENG_TLE	String	Formula	Before each customer	Any	No		
8239	GO_DOC_SEND_ADDR_LINE1	String	User value	Automatically	Any	No		
8240	GO_DOC_SEND_ADDR_LINE2	String	User value	Automatically	Any	No		
8241	GO_DOC_SEND_ADDR_LINE3	String	User value	Automatically	Any	No		
8242	GO_DOC_SEND_ADDR_LINE4	String	User value	Automatically	Any	No		
8243	GO_DOC_SEND_ADDR_LINE5	String	User value	Automatically	Any	No		
8244	GO_DOC_SEND_ADDR_LINE6	String	User value	Automatically	Any	No		
8245	GO_DOC_SEND_ADDR_ZIP_CODE	String	User value	Automatically	Any	No		
8246	GO_DOC_ACCOUNT_NUMBER	String	User value	Automatically	Any	No		
8247	GO_DOC_TOTAL_AMOUNT_DUE	String	User value	Automatically	Any	No		
8248	GO_DOC_TOTAL_AMOUNT_DUE_SIGN	String	User value	Automatically	Any	No		
8249	GO_DOC_PRODUCT_CODE	String	User value	Automatically	Any	No		
8250	GO_DOC_SPECIAL_HANDLING_CODE	String	User value	Automatically	Any	No		
8251	GO_DOC_COUNTRY_CODE	String	User value	Automatically	Any	No		
8252	GO_DOC_INSERT_COMBO	String	User value	Automatically	Any	No		
8253	GO_DOC_PKG_TYPE	String	User value	Automatically	Any	No		
8254	GO_DOC_RETURN_ADDR_LINE1	String	User value	Automatically	Any	No		
8255	GO_DOC_RETURN_ADDR_LINE2	String	User value	Automatically	Any	No		
8256	GO_DOC_RETURN_ADDR_LINE3	String	User value	Automatically	Any	No		
8257	GO_DOC_RETURN_ADDR_LINE4	String	User value	Automatically	Any	No		
8258	GO_DOC_RETURN_ADDR_LINE5	String	User value	Automatically	Any	No		
8259	GO_DOC_REMIT_ADDR_LINE1	String	User value	Automatically	Any	No		
8260	GO_DOC_REMIT_ADDR_LINE2	String	User value	Automatically	Any	No		
8261	GO_DOC_REMIT_ADDR_LINE3	String	User value	Automatically	Any	No		
8262	GO_DOC_REMIT_ADDR_LINE4	String	User value	Automatically	Any	No		
8263	GO_DOC_REMIT_ADDR_LINE5	String	User value	Automatically	Any	No		
8264	GO_DOC_REMIT_ADDR_ZIP_CODE	String	User value	Automatically	Any	No		
8297	GO_FILE_TOTAL_AMOUNT_DUE	String	User value	No reset	Any	No		
8266	GO_FILE_TOTAL_AMOUNT_DUE_SIGN	String	User value	No reset	Any	No		
8267	GO_FILE_IMAGE_COUNT	String	User value	No reset	Any	No		
8268	GO_FILE_SHEET_COUNT	String	User value	No reset	Any	No		
8269	GO_FILE_STATEMENT_COUNT	String	User value	No reset	Any	No		
9174	GO_DOC_QV_INDEX_SEARCH_2	String	User value	Automatically	Any	No		
9180	GO_DOC_QV_INDEX_SEARCH_3	String	User value	Automatically	Any	No		
810	ECS_01_IndivFileNamePDF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production PDF Queues for generating a unique, meaningful named PDF file for each customer.
752	GO_MI_PST_RQST_ID	String	File	Automatically	Any	No	PST01672734	CS_MI_PST_RQST_ID
771	GO_MI_LETTER_DATE	Date	User value	Automatically	Any	No		GO_MI_LETTER_DATE
756	GO_MI_FULL_NAME	String	File	Automatically	Any	No	GO_MI_Mr. John Q. Participant	GO_MI_FULL_NAME
768	GO_MI_ADDRESS_LINE	String	User value	Automatically	Any	Yes	GO_MI_ADDRESS_LINE	GO_MI_ADDRESS_LINE
808	GO_02_LogoPlaceholder_JPG	JPEG Pass-Through Image	User value	Before each customer	Any	No		The Master Formula for an app will concatenate the variable file path with the filename for the proper Logo
803	GO_02_LogoPlaceholder_TIF	TIFF-G4 Image	User value	Before each customer	Any	No		Global Variable for holding  TIFF Images.  Multiple numbered vars are created for use in applications where more than one type of logo is used in same document. See for eg:  ACS
4836	PageNumber	String	User value	Automatically	Any	No	A001	
2418	GO_01_Mobius_02_Data_String	String	Formula	Before each customer	Any	No	#POST#-FollowedByEmailFields	MOB02 barcode data string
1140	GO_01_Mobius_Data_String	String	Formula	Before each customer	Any	No	#POST#-FollowedByEmailFields	GO_01_Mobius_Data_String_POC
1431	GO_MI_BUSINESS_DATE	Date	User value	Automatically	Any	No	January 1, 2011	GO_MI_BUSINESS_DATE
2410	GO_DR_ORCHESTRATION_ID	String	User value	Automatically	Any	No	2352626	
1	SYS_DateCurrent	Date	System	Automatically	Any	No	September 25, 2007	The date that the engine is being run to produce documents (today).
16974	GO_00_GLOBAL_RB_Tiaa	String	Formula	No reset	Any	No	TIAA	Global Formula for dynamically setting Tiaa Text
14935	BAO_ListofForms	String	File	Automatically	Any	Yes	TIAA-CREF Borkerage Services - Account Application	
16133	FR_BAO_OtherDescription	String	File	Automatically	Any	No	test test test test	OtherDescription
3398	FR_02_Title	String	User value	Automatically	Any	Yes	Mr.	
3399	FR_02_FirstName	String	User value	Automatically	Any	Yes	John	
3400	FR_02_MiddleName	String	User value	Automatically	Any	Yes	Q	
3401	FR_02_LastName	String	User value	Automatically	Any	Yes	Public	
3683	FR_02_Suffix	String	User value	Automatically	Any	Yes		
14970	FR_AccountOwnerEmail	String	File	Automatically	Any	Yes	String	
14973	FR_AccountOwnerHomePhone	String	File	Before each customer	Any	Yes	(999)999-9999	
14980	FR_AccountOwnerCitizenship	String	File	Automatically	Any	Yes	String	
14996	FR_BAO_02_AccountOwnerSSN	String	User value	Automatically	Any	Yes		
15001	FR_BAO_02_TrustEffectiveDate	String	User value	Automatically	Any	Yes		
15002	FR_BAO_02_TrustSSN	String	User value	Automatically	Any	Yes		
15003	FR_BAO_02_TrustAccountName	String	User value	Automatically	Any	Yes	Full Name Array	FR_BAO_02_TrustAccount_Name
15011	FR_BAO_02_AccountOwner_BirthDate	String	User value	Automatically	Any	Yes		
15373	FR_AccountOwnerResidentialStreetAddress	String	File	Automatically	Any	Yes	String	
15374	FR_AccountOwnerResidentialCity	String	File	Automatically	Any	Yes	String	
15376	FR_AccountOwnerResidentialZipcode	String	File	Automatically	Any	Yes	String	
15378	FR_AccountOwnerMailingStreetAddress	String	File	Automatically	Any	Yes	String	
15379	FR_AccountOwnerMailingCity	String	File	Automatically	Any	Yes	String	
15381	FR_AccountOwnerMailingZipcode	String	File	Automatically	Any	Yes	String	
15383	FR_BAO_02_State	String	User value	Automatically	Any	Yes		
15478	FR_AccountOwnerBusinessPhone	String	File	Automatically	Any	Yes	(999)999-9999	
16137	FR_AccountOwnerExtension	String	File	Before each customer	Any	Yes	x2888	Extension
16165	FR_AccountOwnerHomeExtension	String	File	Before each customer	Any	Yes	ex. 2	
16643	FR_BAO_02_AccountOwnerSSN2	String	User value	Automatically	Any	No		
16644	FR_BAO_02_AccountOwner_BirthDate2	String	User value	Automatically	Any	No		
14983	FR_BAO_SourceOfIncome	String	File	Automatically	Any	Yes	Active	
15439	FR_BAO_EmployerAddress	String	File	Before each customer	Any	Yes	String	
15440	FR_BAO_EmployerCity	String	File	Before each customer	Any	Yes	String	
15442	FR_BAO_EmployerName	String	File	Before each customer	Any	Yes	String	
15444	FR_BAO_EmployerTitle	String	File	Before each customer	Any	Yes	String	
15445	FR_BAO_EmployerZipcode	String	File	Before each customer	Any	Yes	String	
15466	FR_BAO_Trade_PersonName	String	File	Before each customer	Any	Yes	String	
15467	FR_BAO_Trade_CompanyNameSymbol	String	File	Before each customer	Any	Yes	String	
15468	FR_BAO_Firm_PersonName	String	File	Before each customer	Any	Yes	String	
15469	FR_BAO_Firm_Relationship	String	File	Before each customer	Any	Yes	String	
15470	FR_BAO_Firm_Name	String	File	Before each customer	Any	Yes	String	
15481	FR_BAO_TIAACREF_Relationship	String	File	Before each customer	Any	Yes	String	
15482	FR_BAO_TIAACREF_PersonName	String	File	Before each customer	Any	Yes	String	
16142	FR_BAO_SourceOfFundsOtherDescription	String	File	Automatically	Any	No	Test Test Test Test	
14964	FR_AccountOwnerName	String	File	Automatically	Any	Yes	String	
3283	FR_PrimaryBeneficiaryName	String	File	Before each customer	Any	Yes	String	
3284	FR_PrimaryBeneficiaryPercentage	String	File	Before each customer	Any	Yes	0	
3287	FR_PrimaryBeneficiaryRelationship	String	File	Before each customer	Any	Yes	String	
3669	FR_ER_02_PrimaryBeneficiarySSN	String	User value	Automatically	Any	Yes		
15012	FR_BAO_02_Beneficiary_BirthDate	String	User value	Automatically	Any	Yes		
16646	FR_BAO_02_BeneficiaryFlag1	String	User value	Automatically	Any	No	Y	
16649	FR_BAO_02_BeneficiaryFlag3	String	User value	Automatically	Any	No	Y	
16650	FR_BAO_02_BeneficiaryFlag4	String	User value	Automatically	Any	No	Y	
16648	FR_BAO_02_BeneficiaryFlag2	String	User value	Automatically	Any	No	Y	
3288	FR_ContingentBeneficiaryName	String	File	Before each customer	Any	Yes	String	
3289	FR_ContingentBeneficiaryPercentage	String	File	Before each customer	Any	Yes	0	
3292	FR_ContingentBeneficiaryRelationship	String	File	Before each customer	Any	Yes	String	
3681	FR_ER_02_ContingentBeneficiarySSN	String	User value	Automatically	Any	Yes		
16651	FR_BAO_02_ContingencyFlag2	String	User value	Automatically	Any	No	Y	
16647	FR_BAO_02_ContingencyFlag1	String	User value	Automatically	Any	No	Y	
16645	FR_BAO_02_SpousalWaiver	String	File	Automatically	Any	No		
16652	FR_BAO_02_ContingencyFlag3	String	User value	Automatically	Any	No	Y	
16653	FR_BAO_02_ContingencyFlag4	String	User value	Automatically	Any	No	Y	
16164	FR_ProposalNumber	String	File	Automatically	Any	No	346654	
18532	FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED	String	User value	Automatically	Any	Yes	*********	
18695	FR_BAO_HighlightSSN_Field	String	User value	Automatically	Any	No	YES	
14966	FR_AccountOwnerFirstName	String	File	Automatically	Any	Yes	String	
14968	FR_AccountOwnerLastName	String	File	Automatically	Any	Yes	String	
16146	FR_BAO_InitialTransactionsAccountNumber	String	File	Automatically	Any	No	234567	
16150	FR_BAO_InitialTransactionsTransferCashAmount	String	File	Automatically	Any	No	20.0	
16151	FR_BAO_InitialTransactionsSharesDescription	String	File	Automatically	Any	Yes	String	
16152	FR_BAO_InitialTransactionsSharesQuantity	String	File	Automatically	Any	Yes	**********	
4251	FR_01_FullName	String	Formula	Automatically	Any	No	Full Name	
31	SYS_PageTotalPrinted	Integer	System	Automatically	Any	No	345	The last printed page number in the current document being written.  See SYS_PagePrintedValue for the current printed page number of a specific page.  See SYS_PagePrintedValue for a specific page number.
51	SYS_PagePrintedValue	Integer	System	Automatically	Any	No	21	The page number that should be printed on the current page.  The page number may differ from page count because of page count restart or limitation on counting blank pages.
18476	FR_PrimaryBeneficiaryBirthDate_Display	String	File	Before each customer	Any	Yes	1967-08-13	
18478	FR_PrimaryBeneficiaryName_Display	String	File	Before each customer	Any	Yes	String	
18481	FR_PrimaryBeneficiarySSN_Display	String	File	Before each customer	Any	Yes	*********	
18482	FR_PrimaryBeneficiary_City_Display	String	File	Automatically	Any	Yes	charlotte	
18483	FR_PrimaryBeneficiary_State_Display	String	File	Automatically	Any	Yes	MA	
18484	FR_PrimaryBeneficiary_StreetAddress_Display	String	File	Automatically	Any	Yes	1733 sweet street	
18485	FR_PrimaryBeneficiary_zipcode_Display	String	File	Automatically	Any	Yes	28162	
18487	FR_PrimaryBeneficiary_PhoneNumber_Display	String	File	Automatically	Any	Yes	**********	
18489	FR_ContingentBeneficiaryBirthDate_Display	String	File	Before each customer	Any	Yes	1967-08-13	
18491	FR_ContingentBeneficiaryName_Display	String	File	Before each customer	Any	Yes	String	
18494	FR_ContingentBeneficiarySSN_Display	String	File	Before each customer	Any	Yes	*********	
18496	FR_ContingentBeneficiary_City_Display	String	File	Automatically	Any	Yes	atlanta	
18497	FR_ContingentBeneficiary_State_Display	String	File	Automatically	Any	Yes	CO	
18498	FR_ContingentBeneficiary_StreetAddress_Display	String	File	Automatically	Any	Yes	173 candler street	
18499	FR_ContingentBeneficiary_zipcode_Display	String	File	Automatically	Any	Yes	55562	
18500	FR_ContingentBeneficiaryContactPhoneNumber_Display	String	File	Automatically	Any	Yes	(999)999-9999	
16154	FR_BAO_DecendentFirstName	String	File	Automatically	Any	No	Firstname	
16156	FR_BAO_DecendentLastName	String	File	Automatically	Any	No	Lastname	
18543	FR_BAO_02_Decendent_BirthDate_display	String	User value	Automatically	Any	Yes		
18544	FR_BAO_02_Decendent_DeathDate_display	String	User value	Automatically	Any	Yes		
18772	FR_BAO_02_Trust_AmmendmentDate	String	File	Automatically	Any	Yes	1	
18701	FR_Grantor_FirstName	String	File	Automatically	Any	Yes	6TEST	
18703	FR_Grantor_LastName	String	File	Automatically	Any	Yes	ACCOUNT	
18710	FR_Grantor_ResAddress	String	File	Automatically	Any	Yes	********* KNOXWOOD DR	
18711	FR_Grantor_ResCity	String	File	Automatically	Any	Yes	6666HUNTERSVILLE	
18713	FR_Grantor_ResZip	String	File	Automatically	Any	Yes	*********	
18717	FR_JointGrantor_FirstName	String	File	Automatically	Any	Yes	6TEST	
18719	FR_JointGrantor_LastName	String	File	Automatically	Any	Yes	ACCOUNT	
18726	FR_JointGrantor_ResStreetAddress	String	File	Automatically	Any	Yes	********* KNOXWOOD DR	
18727	FR_JointGrantor_ResCity	String	File	Automatically	Any	Yes	6666HUNTERSVILLE	
18729	FR_JointGrantor_ResZip	String	File	Automatically	Any	Yes	*********	
16179	FR_BAO_02_SEPIRA_TIN_SSN	String	User value	Automatically	Any	No	*********	
16134	FR_BAO_TrustAccountName	String	File	Automatically	Any	No	Herbert	TrustAccountName
16160	FR_BAO_DecendentRelationship	String	File	Automatically	Any	No	String	
16708	FR_BAO_02_SpousalWaiverFlag1	String	User value	Automatically	Any	No	Y	
20320	FR_BAO_02_AccountOwnerBrokerageAccountNumber	String	User value	Automatically	Any	Yes		TLENGONG-12426
16136	FR_BAO_TrustEffectiveDate	Date	File	Automatically	Any	No	2015-01-30-05:00	TrustEffectiveDate
20321	FR_BAO_AmendmentDateofTrust	Date	File	Automatically	Any	No	2015-01-30-05:00	TLENGONG-12426
16062	BAO_BarcodeInformation	String	Formula	Automatically	Any	No		Information Field - 18 byte field, left justified
16060	BAO_BarcodeFormNumber	String	Formula	Automatically ApplicationForm	Any	No		
16061	BAO_BarcodePrefix	String	Formula	Automatically	Any	No		Prefix Field - 2 bytes field
16065	BAO_BarcodePage	String	Formula	Automatically	Any	No		Page Field - 2 byte field
18542	BAO_BarcodeFormVersion	String	Formula	Automatically ApplicationForm	Any	No		
16210	BAO_BarcodeDataStringINSTRUCTIONS	String	Formula	Automatically	Any	No		
18884	BAO_BarcodePage_Minus3	String	Formula	Automatically	Any	No		Page Field - 2 byte field
764	GO_DDA_DOC_REQ_ID	String	User value	Automatically	Any	No	1234	VA
14805	FR_01_BAO_EXT_PDFFiles_PLACEHOLDER	PDF Pass-Through	User value	Before section ApplicationForm	Any	No		FR_01_BAO_EXT_PDFFiles_PLACEHOLDER
16028	FR_BAO_PassThrough_Test	PDF Pass-Through	File	Before section ApplicationForm	Any	No		
16123	FR_BAO_CitiPassThrough_Test	PDF Pass-Through	User value	Before section ApplicationForm	Any	No		
17971	FR_01_BAO_EXT_PDFFiles_PLACEHOLDER_Landscape	PDF Pass-Through	User value	Before section ApplicationForm	Any	No		FR_01_BAO_EXT_PDFFiles_PLACEHOLDER
18278	FR_BAO_CoverLetter_PassThrough	PDF Pass-Through	Formula	Automatically	Any	No		
19456	FR_BAO_PassThrough_BankSweep_TC	PDF Pass-Through	User value	No reset	Any	Yes		
19457	FR_BAO_PassThrough_LiquidInsurance_TC	PDF Pass-Through	User value	No reset	Any	Yes		
21794	FR_BAO_PassThrough_BrokerageSweep_TC_DGVXX	PDF Pass-Through	User value	No reset	Any	Yes		
21791	FR_BAO_PassThrough_BrokerageSweep_TC_DGUXX	PDF Pass-Through	User value	No reset	Any	Yes		
21793	FR_BAO_PassThrough_BrokerageSweep_TC_DVPXX	PDF Pass-Through	User value	No reset	Any	Yes		
21795	FR_BAO_PassThrough_BrokerageSweep_TC_GFYXX	PDF Pass-Through	User value	No reset	Any	Yes		
21796	FR_BAO_PassThrough_BrokerageSweep_TC_TTIXX	PDF Pass-Through	User value	No reset	Any	Yes		
3687	FR_02_CurrentForm	String	User value	Before each customer	Any	Yes		FR_02_CurrentForm
142	SYS_SubDocInDocument	Integer	System	Automatically	Rule	No	11	The current document being written to the current customer.
3693	FR_02_CurrentVersionDate	String	User value	Before each customer	Any	Yes		FR_02_CurrentVersionDate
18540	FR_BAO_FormNumber	String	File	Before section ApplicationForm	Any	No	F11207	
4623	FR_02_WPID	String	User value	Automatically	Any	No	TAKION	
3690	FR_02_FormId	String	User value	Automatically	Any	No	F111111 (06/12)	FR_02_FormID
20318	BAO_NonIRAOption_2	String	File	Before section ApplicationForm	Any	No	Trust	
763	GO_MI_PCKGE_CDE	String	File	Automatically	Any	No	PTBENCNF	GO_MI_PCKGE_CDE
3565	FR_LetterType	String	File	Automatically	Any	No	CoverLetter	FR_LetterType
4637	GO_DR_DOC_CODE	String	File	Automatically	Any	No	CW_SpousalWaiver	GO_DR_DOC_CODE
21787	FR_BAO_DreyfusGovCashManageInvester	String	File	Automatically	Any	No	String	
21785	FR_BAO_DreyfusGovCashManageServiceShares	String	File	Automatically	Any	No	String	
21786	FR_BAO_DreyfusGovSecCashManageInvestor	String	File	Automatically	Any	No	String	
21788	FR_BAO_FederatedHermesGovObligationsCash	String	File	Automatically	Any	No	String	
21789	FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash	String	File	Automatically	Any	No	String	
21790	FR_BAO_Liquid	String	File	Automatically	Any	No	String	
20317	BAO_IRAOption_2	String	File	Before section ApplicationForm	Any	No	Inherited Roth IRA	
852	GO_01_TestOrProd_Flag	String	User value	No reset	Any	No	T	
775	GO_MI_PCKGE_DLVRY_TYP	String	File	Automatically	Any	No	D	GO_MI_PCKGE_DLVRY_TYP
1354	PRO_CheckEFTInd	String	File	Automatically	Any	No	Check	
16975	GO_00_GLOBAL_RB_COMPANY	String	User value	No reset	Any	No		Global Formula for dynamically setting Tiaa Text, set in Control Script
1355	PRO_HoldCode	String	File	Automatically	Any	No	PS01	
13928	FIF_CheckEFTInd	String	File	Automatically	Any	No	Check	
19412	FIF_CheckEFTInd_N	String	File	Automatically	Any	No	Check	
19353	CIS_EOP_REPRINT_IND	String	File	Automatically	Any	No	R	
20507	CIS_EOP_WATERMARK_IND	String	User value	Automatically	Any	No		
14990	FR_BAO_NonIraOption	String	File	Automatically	Any	No	Traditional IRA	
3351	FR_02_TempArray	String	User value	Automatically	Any	Yes		Temporary array
16135	FR_BAO_TrustSSN	String	File	Automatically	Any	No	*********	TrustSSN
14965	FR_AccountOwnerTitle	String	File	Before each customer	Any	Yes	String	
14967	FR_AccountOwnerMiddleName	String	File	Before each customer	Any	Yes	String	
15014	FR_AccountOwnerSuffix	String	File	Before each customer	Any	Yes	String	
14969	FR_AccountOwnerSSN	String	File	Before each customer	Any	Yes	*********	
14972	FR_AccountOwnerBirthDate	Date	File	Automatically	Any	Yes	1967-08-13	
15375	FR_AccountOwnerResidentialState	String	File	Automatically	Any	Yes	String	
15380	FR_AccountOwnerMailingState	String	File	Automatically	Any	Yes	String	
14971	FR_AccountOwnerGender	String	File	Automatically	Any	Yes	Male	
14974	FR_AccountOwnerMaritalStatus	String	File	Automatically	Any	Yes	String	
15443	FR_BAO_EmployerState	String	File	Before each customer	Any	Yes	String	
14982	FR_BAO_EmploymentStatus	String	File	Automatically	Any	Yes	Active	
14985	FR_AccountOwnerTradeCompanyCB	String	File	Automatically	Any	Yes	String	
14986	FR_AccountOwnerTIAACREFCB	String	File	Automatically	Any	Yes	String	
14987	FR_AccountOwnerMemberFirmCB	String	File	Automatically	Any	Yes	String	
14988	FR_AccountOwnerSeniorMilitaryCB	String	File	Automatically	Any	Yes	String	
14991	FR_BAO_InvestmentObjective	String	File	Automatically	Any	No	Traditional IRA	
14989	FR_AccountOwnerInvestmentProfile	String	File	Automatically	Any	Yes	String	
14992	FR_BAO_AnnualIncome	String	File	Automatically	Any	No	Traditional IRA	
14993	FR_BAO_NetWorth	String	File	Automatically	Any	No	Traditional IRA	
14994	FR_BAO_TaxBraket	String	File	Automatically	Any	No	Traditional IRA	
14995	FR_BAO_SourceOfFunds	String	File	Automatically	Any	Yes	Traditional IRA	
16067	FR_BAO_AccountType	String	File	Automatically	Any	No	Traditional IRA	
3285	FR_PrimaryBeneficiarySSN	String	File	Before each customer	Any	Yes	*********	
3286	FR_PrimaryBeneficiaryBirthDate	Date	File	Before each customer	Any	Yes	1967-08-13	
3437	FR_PrimaryBeneficiaryGender	String	File	Before each customer	Any	Yes	Male	
16131	FR_PrimaryBeneficiaryLDPS	String	File	Before each customer	Any	Yes	Y	PrimaryBeneficiaryLDPS
3290	FR_ContingentBeneficiarySSN	String	File	Before each customer	Any	Yes	*********	
3291	FR_ContingentBeneficiaryBirthDate	Date	File	Before each customer	Any	Yes	1967-08-13	
3442	FR_ContingentBeneficiaryGender	String	File	Before each customer	Any	Yes	Male	
16132	FR_ContingentBeneficiaryLDPS	String	File	Before each customer	Any	Yes	Y	ContingentBeneficiaryLDPS
15016	FR_BAO_IraOption	String	File	Automatically	Any	No	Traditional IRA	
16153	FR_BAO_EmployerTaxID	String	File	Automatically	Any	No	2131415	
18450	FR_BAO_DeliveryMethod	String	File	Before each customer	Any	No	E-Signature-Agent	
18531	FR_AccountOwner_PhoneType	String	File	Automatically	Any	Yes	Business	
16642	FR_BAO_VoteProxy	String	File	Automatically	Any	No	*********	VoteProxy
18449	FR_BAO_ConfirmSuppressionStatus_Ind	String	File	Automatically	Any	No	Y	Confirm Suppression Indicator
16176	FR_BAO_SweepSelectionSweepAccount	String	File	Automatically	Any	No	Y	
18696	FR_PrimaryBeneficiary_BeneficiaryType	String	File	Automatically	Any	Yes	Individual	
18697	FR_ContingentBeneficiary_BeneficiaryType	String	File	Automatically	Any	Yes	Trust	
16145	FR_BAO_InitialTransactionsTransferFunds	String	File	Automatically	Any	No	Y	
16147	FR_BAO_InitialTransactionsFullTransfer	String	File	Automatically	Any	No	N	
16178	FR_BAO_SweepSelectionPrime	String	File	Automatically	Any	No	Y	
16148	FR_BAO_InitialTransactionsDeliveringAccountClosed	String	File	Automatically	Any	No	Y	
16149	FR_BAO_InitialTransactionsPartialTransfer	String	File	Automatically	Any	No	Y	
16177	FR_BAO_SweepSelectionCash	String	File	Automatically	Any	No	Y	
17913	FR_BAO_PhoneType	String	File	Automatically	Any	No	Home	
50	SYS_PageInDocument	Integer	System	Automatically	Any	No	21	The current number of counted pages within the document being written.  See SYS_PageTotalInDocument for the total counted pages in the document.  You can set which pages are counted in the application.
30	SYS_PageTotalInDocument	Integer	System	Automatically	Any	No	345	The total number of counted pages in the current document. The number of counted pages can differ from the number of physical pages if you set a page counting method in the application or use duplex. See SYS_PageInDocument for the current page number.
18486	FR_PrimaryBeneficiary_PhoneNumber	String	File	Automatically	Any	Yes	**********	
18466	FR_PrimaryBeneficiary_StreetAddress	String	File	Automatically	Any	Yes	1733 sweet street	
18467	FR_PrimaryBeneficiary_City	String	File	Automatically	Any	Yes	charlotte	
18468	FR_PrimaryBeneficiary_State	String	File	Automatically	Any	Yes	MA	
18469	FR_PrimaryBeneficiary_zipcode	String	File	Automatically	Any	Yes	28162	
18614	FR_ContingentBeneficiary_Category	String	File	Automatically	Any	Yes	A	
18495	FR_ContingentBeneficiaryGender_Display	String	File	Before each customer	Any	Yes	Male	
18492	FR_ContingentBeneficiaryPercentage_Display	String	File	Before each customer	Any	Yes	0	
8380	FR_ContingentBeneficiaryContactPhoneNumber	String	File	Automatically	Any	Yes	(999)999-9999	
18470	FR_ContingentBeneficiary_StreetAddress	String	File	Automatically	Any	Yes	173 candler street	
18471	FR_ContingentBeneficiary_City	String	File	Automatically	Any	Yes	atlanta	
18472	FR_ContingentBeneficiary_State	String	File	Automatically	Any	Yes	CO	
18473	FR_ContingentBeneficiary_zipcode	String	File	Automatically	Any	Yes	55562	
18480	FR_PrimaryBeneficiaryRelationship_Display	String	File	Before each customer	Any	Yes	String	
18883	FR_JointTenantAccount	String	File	Automatically	Any	No	RightsOfSurvivorship	
18563	FR_BAO_Decendent_PlanType	String	File	Automatically	Any	No	Traditional IRA	
18570	FR_BAO_TypeOfTrust	String	File	Automatically	Any	No	Irrevocable	
16155	FR_BAO_DecendentMiddleName	String	File	Automatically	Any	No	Middlename	
16158	FR_BAO_DecendentBirthDate	Date	File	Automatically	Any	No	1960-11-11	
16159	FR_BAO_DecendentDeathDate	Date	File	Automatically	Any	No	1990-11-11	
18568	FR_BAO_Trust_DelegateToAppointedAgent	String	File	Automatically	Any	No	Y	
18569	FR_BAO_Trust_DelegateToOutsideProfessional	String	File	Automatically	Any	No	Y	
18771	FR_BAO_Trust_AmmendmentDate	Date	File	Automatically	Any	No	2016-01-01	
18770	FR_AccountOwner_GrantorType	String	File	Automatically	Any	Yes	Y	
18702	FR_Grantor_Middlename	String	File	Automatically	Any	Yes	B	
18704	FR_Grantor_SSN	String	File	Automatically	Any	Yes	*********	
18707	FR_Grantor_BirthDate	Date	File	Automatically	Any	Yes	6666-06-21	
18712	FR_Grantor_ResState	String	File	Automatically	Any	Yes	NC	
18718	FR_JointGrantor_MiddleName	String	File	Automatically	Any	Yes	B	
18720	FR_JointGrantor_SSN	String	File	Automatically	Any	Yes	*********	
18723	FR_JointGrantor_Birthdate	Date	File	Automatically	Any	Yes	6666-06-21	
18728	FR_JointGrantor_ResState	String	File	Automatically	Any	Yes	NC	
16157	FR_BAO_DecendentSSN	String	File	Automatically	Any	No	*********	
20322	FR_BAO_TrustType	String	File	Automatically	Any	No	Revocable	TLENGONG-12426
15015	FR_AccountOwnerBrokerageAccountNumber	String	File	Automatically	Any	Yes	String	
70	SYS_QueueCurrent	String	System	Automatically	Any	No	Under 1 OZ	The name for the output queue for the current document.
4837	PageNumberPrefix	String	User value	Automatically	Any	No		
995	GO_01_TIFF_Queue_Application_Flag	String	User value	Before each customer	Any	No	N	"APP Level Queue Rule variable. App will enable/disable a particular ""TYPE"" of Queue by setting this flag."
1435	GO_01_PDF_Arch_Queue_Application_Flag	String	User value	Before each customer	Any	No	N	"APP Level Queue Rule variable. App will enable/disable a particular ""TYPE"" of Queue by setting this flag."
737	GO_01_Init_Image_Library_Array	String	User value	No reset	Any	Yes		GO_01_Init_Image_Library_Array - This array variable is mapped to the column of all possible image file names listed in the Image Initialization File
738	GO_DDA_Customer_Track_Variable	String	User value	Automatically	Any	No		
736	GO_01_Init_Image_Library_Array_TIFF	String	User value	No reset	Any	Yes		GO_01_Init_Image_Library_Array - This array variable is mapped to the column of all possible image file names listed in the Image Initialization File
1463	GO_AR_AUDIT_RPT_ID	String	Formula	Automatically	Any	No		Audit Report ID
20	SYS_CustomerInRun	Integer	System	Automatically	Any	No		The current customer number being processed.
741	GO_AR_FILE_NAME	String	User value	Automatically	Any	No		Audit Report
742	GO_AR_SYSTEM	String	User value	Automatically	Any	No		Audit Report
743	GO_AR_APP	String	User value	Automatically	Any	No		Audit Report
751	GO_AR_TOTAL_NOTICE	String	User value	Automatically	Any	No		Audit Report
745	GO_AR_START_RQST_ID	String	User value	Automatically	Any	No		Audit Report
746	GO_AR_END_RQST_ID	String	User value	Automatically	Any	No		Audit Report
747	GO_AR_START_NAME	String	User value	Automatically	Any	No		Audit Report
748	GO_AR_END_NAME	String	User value	Automatically	Any	No		Audit Report
749	GO_AR_FOREIGN_NOTICE	String	User value	Automatically	Any	No		Audit Report
750	GO_AR_BAD_ADDR_NOTICE	String	User value	Automatically	Any	No		Audit Report
744	GO_AR_CYC_DATE	String	User value	Automatically	Any	No	20100817144019	Audit Report
740	GO_AR_RDT	String	User value	Automatically	Any	No		Audit Report
753	GO_MI_BC_PIN_NBR	String	User value	Automatically	Any	No	1234567	GO_MI_BC_PIN_NBR
1092	GO_00_AuditReports_Address_Typ_Code	String	Formula	Automatically	Any	No		"Copy of GO_MI_ADD_TYP_CDE  from I/P file for suppressing ""U"" in audit reports"
1001	GO_AR_RPT_ID_TIFF	String	Formula	No reset	Any	No	DCSD3019SI	Audit Report TIFF
1003	GO_00_GLOBAL_TIFF_AuditReport_SeqNo	Integer	User value	No reset	Any	No	001	
1462	GO_AR_RPT_ID_ARCH	String	Formula	No reset	Any	No	DCSD3019SI	Audit Report ARCH
1448	GO_00_GLOBAL_ARCH_AuditReport_SeqNo	Integer	User value	No reset	Any	No	001	
761	GO_DR_BATCH_COUNTER	Integer	User value	Automatically	Any	No		GO_DR_BATCH_COUNTER
757	GO_DR_RQST_USER_ID	String	User value	Automatically	Any	No		GO_DR_RQST_USER_ID
758	GO_DR_RQST_USER_NAME	String	User value	Automatically	Any	No		GO_DR_RQST_USER_NAME
759	GO_DR_RQST_DATE_TIME	String	User value	Automatically	Any	No		GO_DR_RQST_DATE_TIME
3710	GO_DR_COMPOSITE_ORCH_ID	String	File	Automatically	Any	No	CompOrchId_11	GO_DR_COMPOSITE_ORCH_ID
4636	GO_DR_BUSINESS_UNIT_CODE	String	File	Automatically	Any	No	PENSION	GO_DR_BUSINESS_UNIT_CODE
4638	GO_DR_VERSION	String	File	Automatically	Any	No	1	GO_DR_VERSION
4639	GO_DR_DOC_SEQUENCE	String	File	Automatically	Any	No	1	GO_DR_DOC_SEQUENCE
760	GO_DR_BATCH_IND	String	User value	Automatically	Any	No		GO_DR_BATCH_IND
765	GO_MI_PRNTR_ID_CDE	String	File	Automatically	Any	No	TZUG	GO_MI_PRNTR_ID_CDE
1147	GO_MI_PREFIX	String	File	Automatically	Any	No		GO_MI_PREFIX
1149	GO_MI_FIRST_NAME	String	File	Automatically	Any	No	John	GO_MI_FIRST_NAME
1148	GO_MI_MID_NAME	String	File	Automatically	Any	No		GO_MI_MIDDLE_NAME
1146	GO_MI_SUFFIX	String	File	Before each customer	Any	No		GO_MI_SUFFIX
766	GO_MI_LAST_NAME	String	File	Automatically	Any	No		GO_MI_LAST_NAME
754	GO_MI_ADD_TYP_CDE	String	User value	Automatically	Any	No		CS_MI_ADD_TYP_CDE
770	GO_MI_ALT_DLVRY_ADDR	String	File	Automatically	Any	No	Fax Number or Email Address	GO_MI_ALT_DLVRY_ADDR
776	GO_MI_PCKGE_IMAGE_IND	String	File	Automatically	Any	No		GO_MI_PCKGE_IMAGE_IND
1449	GO_MI_PORTAL_DOC_DESC	String	User value	Automatically	Any	No		GO_MI_PORTAL_DOC_DESC
1429	GO_MI_ARCHIVAL_IND	String	File	Automatically	Any	No	D	GO_MI_ARCHIVAL_IND
1430	GO_MI_PLAN_ID	String	File	Automatically	Any	No	D	GO_MI_PLAN_ID
16068	FR_BAO_AccountCategory	String	File	Automatically	Any	No	Traditional IRA	
15377	FR_AccountOwnerResidentialCountry	String	File	Automatically	Any	Yes	String	
15382	FR_AccountOwnerMailingCountry	String	File	Automatically	Any	Yes	String	
14981	FR_AccountOwnerStateofResidence	String	File	Automatically	Any	Yes	String	
21784	FR_BAO_Edelivery	String	File	Automatically	Any	No	String	
15471	FR_BAO_Margin	String	File	Automatically	Any	No	String	
930	GO_MI_EMPL_SGNTRY_NME	String	File	Automatically	Any	No	GO_MI_Empl_Sgntry_Name	GO_MI_EMPL_SGNTRY_NME
773	GO_MI_EMPL_UNIT_WORK_NME	String	File	Automatically	Any	No	Mail Item Work Unit Name	GO_MI_EMPL_UNIT_WORK_NME
3558	FR_StateOfResidence	String	File	Automatically	Any	No	NY	FR_StateOfResidence
3559	FR_Citizenship	String	File	Automatically	Any	No	United States	FR_Citizenship
5953	FR_PhoneNumber	String	File	Automatically	Any	No	String	FR_PhoneNumber
3562	FR_PlanName	String	File	Automatically	Any	No	XYZ Plan	FR_PlanName
3563	FR_PlanId	String	File	Automatically	Any	No	P12345	FR_PlanId
3620	FR_FormNumber	String	File	Before section FORM	Any	No	FR_FormNumber	FR_FormNumber
3726	FR_InstitutionName	String	File	Automatically	Any	No	FR_XYZ Institution	FR_InstitutionName
3727	FR_RequestDate	Date	File	Automatically	Any	No	2012-08-06	FR_RequestDate
4580	FR_MarriedIndicator	String	User value	Automatically	Any	No		FR_MarriedIndicator
3751	FR_WithdrawalMethodType	String	File	Automatically	Any	No	CD	FR_WithdrawalMethodType
3754	FR_TotalWithdrawalAmount	Currency	User value	Automatically	Any	No	99,999,999.99	FR_TotalWithdrawalAmount
4574	FR_SwatFrequency	String	File	Automatically	Any	No	Quarterly	FR_SwatFrequency
4575	FR_SwatNumberofPayments	Integer	File	Automatically	Any	No	5	FR_SwatNumberofPayments
4577	FR_SwatPaymentsStopDate	Date	File	Automatically	Any	No	2012-07-01	FR_SwatPaymentsStopDate
4581	FR_SwatNoFundsOrStopIndicator	String	File	Automatically	Any	No	Y	FR_SwatNoFundsOrStopIndicator
11692	FR_SwTransactionType	String	File	Automatically	Any	No	RMD	FR_SwTransactionType
11693	FR_SwDistributionType	String	File	Automatically	Any	No	SWAT	FR_SwDistributionType
11694	FR_AnnualizedRequestAmount	Currency	File	Automatically	Any	No	11111.11	FR_AnnualizedRequestAmount
11695	FR_SwRmd2in1OptionIndicator	String	File	Automatically	Any	No	Y	FR_SwRmd2in1OptionIndicator
11696	FR_SwRmd2in1RequiredIndicator	String	File	Automatically	Any	No	Y	FR_SwRmd2in1RequiredIndicator
11697	FR_SwRmd2in1EligibleIndicator	String	File	Automatically	Any	No	Y	FR_SwRmd2in1EligibleIndicator
4443	FR_TotalPercentageDollarValue	Currency	File	Automatically	Any	No	$22,222.22	FR_TotalPercentageDollarValue
12025	FR_SwSubPlanDistributionType	String	File	Automatically	Any	No	SWAT	FR_SwSubPlanDistributionType
3753	FR_TotalWithdrawalPercentage	Float	File	Automatically	Any	No	50.00	FR_TotalWithdrawalPercentage
4579	FR_TotalBalanceAmount	Currency	File	Automatically	Any	No	$22,222.22	FR_TotalBalanceAmount
4466	LT_FundNameArray	String	File	Automatically	Any	Yes	Payment Duration Fund Name	LT_FundNameArray
6460	LT_FundTickerSymbolArray	String	File	Automatically	Any	Yes	#1111	LT_FundTickerSymbolArray
6401	LT_FundWithdrawalPercentageArray	Float	File	Automatically	Any	Yes	25	LT_FundWithdrawalPercentageArray
6402	LT_FundWithdrawalAmountArray	Currency	File	Automatically	Any	Yes	********11111.11	LT_FundWithdrawalPercentageArray
3756	FR_PullForm_NonStaplingPortraitFileName	String	User value	Automatically	Any	No		FR_PullForm_NonStaplingPortraitFileName
772	GO_MI_LTTR_SLTTN_TXT	String	File	Automatically	Any	No	GO MI Salutation Text	GO_MI_LTTR_SLTTN_TXT
1450	GO_MI_ARCHACK	String	File	Automatically	Any	No	D	GO_MI_ARCHACK
1427	GO_MI_EDELACK	String	File	Automatically	Any	No	D	GO_MI_EDELACK
1451	GO_MI_PRINTACK	String	File	Automatically	Any	No	D	GO_MI_PRINTACK
954	GO_PI_CONTRACTS	String	User value	Automatically	Any	Yes		GO_PI_CONTRACTS
9171	LT_RmdYearEndBalanceAdjustment	Currency	File	Automatically	Any	No	666.66	LT_RmdYearEndBalanceAdjustment
9172	LT_ExcludeGrandfatheredAmountIndicator	String	File	Automatically	Any	No	Y	LT_ExcludeGrandfatheredAmountIndicator
9706	LT_RothTotalAmount	Currency	File	Automatically	Any	No	111,111,111.11	LT_RothTotalAmount
8627	LT_InServiceType	String	File	Automatically	Any	No	InServiceType	LT_InServiceType
4296	FR_RecurringPaymentIndicator	String	File	Automatically	Any	No	SWAT	FR_RecurringPaymentIndicator
2380	GO_MI_FAX_NUMBER	String	User value	Automatically	Any	No	<EMAIL>	GO_MI_FAX_NUMBER
822	GO_PI_EXPORT_IND	String	File	Automatically	Any	Yes	A	GO_PI_EXPORT_IND
828	GO_PI_TASK_ID	String	File	Automatically	Any	Yes	PI Task ID	GO_PI_TASK_ID
829	GO_PI_TASK_TYPE	String	File	Automatically	Any	Yes	PI Task Type	GO_PI_TASK_TYPE
823	GO_PI_TASK_GUID	String	File	Automatically	Any	Yes	PI Task Guid	GO_PI_TASK_GUID
824	GO_PI_ACTION_STEP	String	File	Automatically	Any	Yes	PI Action Step	GO_PI_ACTION_STEP
834	GO_PI_DOC_CONTENT	String	File	Automatically	Any	Yes	PI Doc Content	GO_PI_DOC_CONTE
827	GO_PI_TASK_STATUS	String	File	Automatically	Any	Yes	A	GO_PI_TASK_STATUS
833	GO_PI_PLAN_ID	String	File	Automatically	Any	Yes	PI Plan ID	GO_PI_PLAN_ID
826	GO_PI_TIAA_TIME	String	File	Automatically	Any	Yes	11:22:33	GO_PI_TIAA_TIME
830	GO_PI_SSN	String	File	Automatically	Any	Yes	ABCDEFGHI	GO_PI_SSN
831	GO_PI_PIN_NPIN_PPG	String	File	Automatically	Any	Yes	PI Pin Npin PPG	GO_PI_PIN_NPIN_PPG
832	GO_PI_PIN_TYPE	String	File	Automatically	Any	Yes	A	GO_PI_PIN_TYPE
5343	LT_DeclineReasonText	String	User value	Automatically	Any	No	Decline Reason Text	LT_DeclineReasonText
4456	LT_RequestOptionType	String	File	Before each customer	Any	No	S	LT_RequestOptionType
4457	LT_WithdrawalType	String	File	Automatically	Any	No	C	LT_WithdrawalType
4458	LT_F402fOption	String	User value	Before each customer	Any	No	Y	LT_F402fOption
4459	LT_RelativeValueDisclosureOption	String	File	Before each customer	Any	No	Y	LT_RelativeValueDisclosureOption
6381	LT_NewEnrollmentIndicator	String	File	Automatically	Any	No	Y	LT_NewEnrollmentIndicator
10551	LT_LetterTransactionType	String	File	Automatically	Any	No	Cash	LT_LetterTransactionType
10552	LT_LetterDistributionType	String	File	Automatically	Any	No	OneTime	LT_LetterDistributionType
774	GO_MI_BIN_ITEMS	String	User value	Automatically	Any	Yes		GO_MI_BIN_ITEMS
4276	LT_PlanName	String	User value	Automatically	Any	No	XYZ University	LT_PlanName
4628	LT_Frequency	String	File	Automatically	Any	No	Annual	LT_Frequency
4501	LT_LastPaymentDate	Date	File	Automatically	Any	No	2012-11-01	LT_LastPaymentDate
4502	LT_NetAmountOfLastPayment	Currency	File	Automatically	Any	No	3333.33	LT_NetAmountOfLastPayment
5955	LT_PaymentStopDate	Date	File	Automatically	Any	No	2012-09-13	LT_PaymentStopDate
6467	LT_OtpDeliveryMode	String	File	Automatically	Any	No	M	LT_OtpDeliveryMode
8349	LT_RecurringPhase	String	File	Automatically	Any	No	Maintenance	LT_RecurringPhase
4630	LT_FirstPaymentDate	Date	File	Automatically	Any	No	2012-09-01	LT_FirstPaymentDate
6388	LT_NumberOfPayments	Integer	File	Automatically	Any	No	1234	
8485	LT_UntilFundsDepletedInd	String	File	Automatically	Any	No	Y	LT_UntilFundsDepletedInd
8403	LT_FinalPaymentDate	Date	File	Automatically	Any	No	2014-03-15	LT_FinalPaymentDate
5387	LT_SubsequentPaymentsDay	Integer	User value	Automatically	Any	No	15	LT_SubsequentPaymentsDay
6399	LT_ProductSubCode	String	File	Automatically	Any	No	PSub11	LT_ProductSubCode
6469	LT_RequestedPercentage	Float	File	Automatically	Any	No	11.11	LT_RequestedPercentage
6206	LT_TiaaIndexContractNumber	String	File	Automatically	Any	No	T_INDEX-2	LT_TiaaIndexContractNumber
6406	LT_RmdPlanTotal	Currency	File	Automatically	Any	No	*********1234.12	LT_RmdPlanTotal
6412	LT_OptionalWithholdingAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_OptionalWithholdingAmount
6415	LT_TaxMaritalStatus	String	File	Automatically	Any	No	HH	LT_TaxMaritalStatus
6416	LT_TaxExemptIndicator	String	File	Automatically	Any	No	Y	LT_TaxExemptIndicator
6417	LT_NumberofExemptions	Integer	File	Automatically	Any	No	1234	LT_NumberofExemptions
6418	LT_TaxFlatDollarAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_TaxFlatDollarAmount
6419	LT_TaxFixedPercentage	Float	File	Automatically	Any	No	*********1234.12	LT_TaxFixedPercentage
6428	LT_TaxWithholdingAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_TaxWithholdingAmount
6422	LT_TaxFixedDollarAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_TaxFixedDollarAmount
6434	LT_RmdCalculationMethod	String	File	Automatically	Any	No	Uniform	LT_RmdCalculationMethod
6435	LT_SpouseDateofBirth	Date	File	Automatically	Any	No	1980-08-13	LT_SpouseDateofBirth
6470	LT_UniformLifeExpectancyFactor	Float	File	Automatically	Any	No	11.11	LT_UniformLifeExpectancyFactor
6471	LT_JointLifeExpectancyFactor	Float	File	Automatically	Any	No	22.22	LT_JointLifeExpectancyFactor
6438	LT_RmdTotalWithdrawalPayments	Currency	File	Automatically	Any	No	121212.12	LT_RmdTotalWithdrawalPayments
6439	LT_RmdPriorYearGrandfatheredAmount	Currency	File	Automatically	Any	No	************.34	LT_RmdPriorYearGrandfatheredAmount
6440	LT_RmdPriorYearEndAccumulation	Currency	File	Automatically	Any	No	**********.56	LT_RmdPriorYearEndAccumulation
10553	LT_GfAccumulationEligibilityIndicator	String	File	Automatically	Any	No	Y	LT_GfAccumulationEligibilityIndicator
3175	LT_BankName	String	File	Automatically	Any	No	Bank Name	LT_BankName
3174	LT_BankTransitNumber	String	File	Automatically	Any	No	Transit Number	LT_BankTransitNumber
3173	LT_BankAccountNumber	String	File	Automatically	Any	No	Bank Account Number	LT_BankAccountNumber
6442	LT_BankRoutingNumber	String	File	Automatically	Any	No	BRN-********	LT_BankRoutingNumber
4635	LT_BankAccountType	String	File	Automatically	Any	No	C	LT_BankAccountType
6443	LT_ExternalRolloverPlanType	String	File	Automatically	Any	No	ExtRollType	LT_ExternalRolloverPlanType
3181	LT_ExternalCompanyName	String	File	Automatically	Any	No	External Company Name	LT_ExternalCompanyName
6444	LT_ExternalCompanyAddressLine1	String	File	Automatically	Any	No	External Company Address Line 1	LT_ExternalCompanyAddressLine1
6445	LT_ExternalCompanyAddressLine2	String	File	Automatically	Any	No	External Company Address Line 2	LT_ExternalCompanyAddressLine2
4282	LT_ExternalCompanyAccountNumber	String	File	Automatically	Any	No	Ext. Co. Acct. 123	LT_ExternalCompanyAccountNumber
6446	LT_City	String	File	Automatically	Any	No	New York City	LT_City
6447	LT_State	String	File	Automatically	Any	No	New York	LT_State
6448	LT_ZipCode	String	File	Automatically	Any	No	10017-1234	LT_ZipCode
6455	LT_RolloverType	String	File	Automatically	Any	No	Internal	LT_RolloverType
6456	LT_NewEnrollmentInRollover	String	File	Automatically	Any	No	Y	LT_NewEnrollmentInRollover
6457	LT_RolledOverPlanName	String	File	Automatically	Any	No	Rolled Over Plan Name	LT_RolledOverPlanName
6458	LT_RolledOverTiaaContractNumber	String	File	Automatically	Any	No	RO111111-1	LT_RolledOverTiaaContractNumber
6459	LT_InvestmentOption	String	File	Automatically	Any	No	C	LT_InvestmentOption
6474	LT_CarrierName	String	File	Automatically	Any	No	Mr. Carrier Name	LT_CarrierName
6475	LT_CarrierAddressLines	String	File	Automatically	Any	Yes	Carrier Address Line 1	LT_CarrierAddressLines
6461	LT_OtherAccountName	String	File	Automatically	Any	No	Other Account Name	LT_OtherAccountName
6462	LT_OtherAccountNumber	String	File	Automatically	Any	No	OANbr-********	LT_OtherAccountNumber
6464	LT_OtherAccountType	String	File	Automatically	Any	No	OAType	LT_OtherAccountType
7945	LT_IvcOption	String	File	Automatically	Any	No	I	LT_IvcOption
6373	LT_EmployerName	String	File	Automatically	Any	No	Employer Name in Personal Info Section	LT_EmployerName
4460	LT_SpousalWaiverOption	String	File	Before each customer	Any	No	Y	LT_SpousalWaiverOption
4548	LT_SponsorIndicator	String	File	Before each customer	Any	No	Y	LT_SponsorIndicator
4546	LT_MarriedUnmarriedIndicator	String	User value	Automatically	Any	No	M	LT_MarriedUnmarriedIndicator
5047	LT_RecurringPaymentIndicator	String	File	Automatically	Any	No	SWAT	LT_RecurringPaymentIndicator
8626	LT_MaritalStatusValidation	String	File	Automatically	Any	No	Y	LT_MaritalStatusValidation
10083	LT_PrismSlaInd	String	File	Automatically	Any	No	Y	LT_PrismSlaInd
10084	LT_PrismSponsorApprovalDays	Integer	File	Automatically	Any	No	5	LT_PrismSponsorApprovalDays
10085	LT_PrismSponsorApprovalExtDays	Integer	File	Automatically	Any	No	10	LT_PrismSponsorApprovalExtDays
6371	LT_TransactionType	String	File	Automatically	Any	No	Cash	LT_TransactionType
6372	LT_DistributionType	String	File	Automatically	Any	No	OneTime	LT_DistributionType
6374	LT_GrossAmount	Currency	File	Automatically	Any	No	********11111.11	LT_GrossAmount
6376	LT_Fees	Currency	File	Automatically	Any	No	2222222222222.22	LT_Fees
6377	LT_EstimatedTaxes	Currency	File	Automatically	Any	No	********33333.33	LT_EstimatedTaxes
6378	LT_NetWithdrawal	Currency	File	Automatically	Any	No	4444444444444.44	LT_NetWithdrawal
6382	LT_DataValidationRequiredIndicator	String	File	Automatically	Any	No	Y	LT_DataValidationRequiredIndicator
6383	LT_PendingOtpIndicator	String	File	Automatically	Any	No	N	LT_PendingOtpIndicator
6384	LT_MailingAddressIndicator	String	File	Automatically	Any	No	N	LT_MailingAddressIndicator
6385	LT_EmailAddressIndicator	String	File	Automatically	Any	No	N	LT_EmailAddressIndicator
9170	LT_EdeliveryPreferenceIndicator	String	File	Automatically	Any	No	Y	LT_EdeliveryPreferenceIndicator
6397	LT_EffectiveDateofDistribution	Date	File	Automatically	Any	No	2013-10-01	LT_EffectiveDateofDistribution
6424	LT_AnnualizedRequestAmount	Currency	File	Automatically	Any	No	********.11	LT_AnnualizedRequestAmount
6425	LT_FrequencyPaymentAmount	Currency	File	Automatically	Any	No	2222.222	LT_FrequencyPaymentAmount
6398	LT_ProductCode	String	File	Automatically	Any	No	PC111	LT_ProductCode
6468	LT_RequestedAmount	Currency	File	Automatically	Any	No	$11111.11	LT_RequestedAmount
4461	LT_TiaaNumber	String	File	Automatically	Any	No	T11111-1	LT_TiaaNumber
6207	LT_IraIndexIndicator	String	File	Automatically	Any	No	B	LT_IraIndexIndicator
4462	LT_CrefNumber	String	File	Automatically	Any	No	C11111-1	LT_CrefNumber
6400	LT_SettlementSelectionType	String	File	Automatically	Any	No	Maximum	LT_SettlementSelectionType
6403	LT_AfterTaxAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_AfterTaxAmount
6405	LT_TaxableAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_TaxableAmount
6407	LT_TotalAmountRequested	Currency	File	Automatically	Any	No	*********.99	LT_TotalAmountRequested
6409	LT_TaxAnnualizedAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_TaxAnnualizedAmount
7969	LT_TaxFrequencyAmount	Currency	User value	Automatically	Any	No	$123,456.78	LT_TaxFrequencyAmount
6411	LT_OmniTaxAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_OmniTaxAmount
6413	LT_OptionalWithholdingPercentage	Float	File	Automatically	Any	No	*********1234.12	LT_OptionalWithholdingPercentage
8095	LT_TaxFrequencyAmountFedAndState	Currency	File	Automatically	Any	No	12345678.90	LT_TaxFrequencyAmountFedAndState
6414	LT_TaxOptOutIndicator	String	File	Automatically	Any	No	Y	LT_TaxOptOutIndicator
6423	LT_TotalTaxWithheldAmount	Currency	File	Automatically	Any	No	*********1234.12	LT_TotalTaxWithheldAmount
6433	LT_TotalTaxFrequencyAmount	Currency	File	Automatically	Any	No	$*********1.12	LT_TotalTaxFrequencyAmount
6441	LT_PaymentMethod	String	File	Automatically	Any	No	EFT	LT_PaymentMethod
3191	LT_SendAddress	String	File	Automatically	Any	Yes	Send Address Line	LT_SendAddress
7518	LT_IncludeIvcAmount	String	File	Automatically	Any	No	N	LT_IncludeIvcAmount
8001	LT_ExcludedIvcAmount	Currency	File	Automatically	Any	No	111111.11	LT_ExcludedIvcAmount
3560	FR_TiaaNumber	String	File	Automatically	Any	No	T11111-1	FR_TiaaNumber
3561	FR_CrefNumber	String	File	Automatically	Any	No	C22222-2	FR_CrefNumber
3564	FR_SubplanId	String	File	Automatically	Any	No	S12345	FR_SubplanId
9705	LT_RothHeaderInd	String	File	Automatically	Any	No	- SUMMARY	LT_RothHeaderInd
14975	FR_AccountOwnerAddressLines	String	File	Before each customer	Any	Yes	Address	
18699	FR_GrantorName	String	File	Automatically	Any	Yes	6666MR TEST B ACCOUNT	
18700	FR_Grantor_Prefix	String	File	Automatically	Any	Yes	MR	
18705	FR_Grantor_Email	String	File	Automatically	Any	Yes	<EMAIL>	
18706	FR_Grantor_Gender	String	File	Automatically	Any	Yes	Female	
18708	FR_Grantor_MaritalStatus	String	File	Automatically	Any	Yes	Mard	
18709	FR_Grantor_BusinessPhone	String	File	Automatically	Any	Yes	**********	
18714	FR_Grantor_ResCountry	String	File	Automatically	Any	Yes	US	
18715	FR_JointGrantor_FullName	String	File	Automatically	Any	Yes	6666MR TEST B ACCOUNT	
18716	FR_JointGrantor_Prefix	String	File	Automatically	Any	Yes	MR	
18721	FR_JointGrantor_Email	String	File	Automatically	Any	Yes	<EMAIL>	
18722	FR_JointGrantor_Gender	String	File	Automatically	Any	Yes	Female	
18724	FR_JointGrantor_MaritalStatus	String	File	Automatically	Any	Yes	Mard	
18725	FR_JointGrantor_BusinessPhone	String	File	Automatically	Any	Yes	**********	
18730	FR_JointGrantor_ResCountry	String	File	Automatically	Any	Yes	US	
15441	FR_BAO_EmployerCountry	String	File	Automatically	Any	Yes	String	
762	GO_DR_BATCH_TOTAL	Integer	User value	Automatically	Any	No		GO_DR_BATCH_TOTAL
767	GO_MI_SSN	String	File	Automatically	Any	No	*********	GO_MI_SSN
1426	GO_MI_RETURN_DOC_TYPE	String	File	Automatically	Any	No	D	GO_MI_RETURN_DOC_TYPE
799	ECS_01_OutputPathforResources	String	User value	No reset	Any	No	/app/exstream/ecs/images	This string variable is hardcoded or set at runtime with the path where the required images reside
19461	FR_BAO_PassThrough_LiquidInsurance_TC_SD	PDF Pass-Through	User value	No reset	Any	Yes		
1573	GO_01_OutputPathforEXTFILES	String	User value	No reset	Any	No		GO_01_OutputPathforEXTFILES.  Value is a VARSET
17977	FR_01_BAO_EXT_BUCKSLIP_PDFFiles_PLACEHOLDER	PDF Pass-Through	User value	Before section ApplicationForm	Any	No		FR_01_BAO_EXT_BUCKSLIP_PDFFiles_PLACEHOLDER
111	SYS_PostSort	Boolean	System	Automatically	Any	No	T	True if running post-sort, False if running pre-sort.
806	ECS_01_OutputPathforTIFF	String	User value	No reset	Any	No	/app/exstream/ecs/output	This variable should be set by the VARSET command in each Control file.  It specificies the path where generated TIFF files shall go
811	ECS_01_OutputPathforPDF	String	User value	No reset	Any	No	/app/exstream/ecs/output	This variable should be set by the VARSET command in each Control file.  It specificies the path where generated PDFs shall go
813	ECS_01_OutputPathforPS	String	User value	No reset	Any	No	/app/exstream/ecs/output	This variable should be set by the VARSET command in each Control file.  It specificies the path where generated Postscript files shall go
985	GO_01_PS_QUEUE_Flag	String	User value	No reset	Any	No	1	Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.
987	GO_01_TIFF_QUEUE_Flag	String	User value	No reset	Any	No	1	Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.
1436	GO_01_ARCHPDF_QUEUE_Flag	String	User value	No reset	Any	No	1	Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.
1440	GO_01_EDELPDF_QUEUE_Flag	String	User value	No reset	Any	No	1	Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.
1454	GO_01_DCS_ReqType	String	User value	No reset	Any	No	DWS	VARSET GO_01_DCS_ReqType. Used for DWS Server
805	ECS_01_IndivFileNameTIFF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production TIFF Queues for generating a unique, meaningful named .tiff file for each customer.
812	ECS_01_IndivFileNamePS	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production Postscript Queues for generating a unique, meaningful named PS file for each customer.
816	ECS_01_OneFileNamePDF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production PDF Queues for generating one file for each PDF run.  It is customzied within each application.
817	ECS_01_OneFileNamePS	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production PS Queues for generating one file for each PS print run.  It is customized  within each application.
982	ECS_01_OneFileNameTIFF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production TIFF Queues for generating one file for each run.  It is customzied within each application.
1433	ECS_01_OneFileNameArchPDF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production Arch PDF Queues for generating one file for each PDF run.  It is customzied within each application.
1437	GO_AR_FILE_NAME_DELTYPE	String	Formula	Automatically	Any	No		Audit Report
1438	ECS_01_OneFileNameEdelPDF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production Edel PDF Queues for generating one file for each PDF run.  It is customzied within each application.
1439	GO_01_PDF_Edel_Queue_Application_Flag	String	User value	Before each customer	Any	No	N	"APP Level Queue Rule variable. App will enable/disable a particular ""TYPE"" of Queue by setting this flag."
1445	ECS_01_IndivFileNameArchPDF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production PDF Queues for generating a unique, meaningful named PDF file for each customer.
1446	ECS_01_IndivFileNameEdelPDF	String	User value	Before each customer	Any	No	/app/exstream/ecs/tuitionDate/PkgIDCustomer.pdf	This will be used by all production PDF Queues for generating a unique, meaningful named PDF file for each customer.
17727	FR_02_LogoPlaceholder_JPG_IRA_eApp	JPEG Pass-Through Image	User value	Before each customer	Any	No		The Master Formula for an app will concatenate the variable file path with the filename for the proper Logo
871	GO_01_GroupPS_Flag	String	User value	No reset	Any	No	Y	Group PS Enable/Disable Flag. Used in Queue Rules.
872	GO_01_GroupPDF_Flag	String	User value	No reset	Any	No	Y	
986	GO_01_PDF_QUEUE_Flag	String	User value	No reset	Any	No	1	Queue Selection FLAG : (values 0 - 3) set in the script file for engine by DCS.
988	GO_01_GroupTIFF_Flag	String	User value	No reset	Any	No	Y	Group TIFF Enable/Disable Flag. Used in Queue Rules.
989	GO_01_IndivPDF_Flag	String	User value	No reset	Any	No	Y	
990	GO_01_IndivPS_Flag	String	User value	No reset	Any	No	Y	
991	GO_01_IndivTIFF_Flag	String	User value	No reset	Any	No	Y	Individual TIFF Enable/Disable Flag. Used in Queue Rules.
993	GO_01_PDF_Queue_Application_Flag	String	User value	Before each customer	Any	No	N	"APP Level Queue Rule variable. App will enable/disable a particular ""TYPE"" of Queue by setting this flag."
994	GO_01_PS_Queue_Application_Flag	String	User value	Before each customer	Any	No	N	"APP Level Queue Rule variable. App will enable/disable a particular ""TYPE"" of Queue by setting this flag."
1441	GO_01_GroupArchPDF_Flag	String	User value	No reset	Any	No	Y	Group Arch PDF Enable/Disable Flag. Used in Queue Rules.
1442	GO_01_GroupEdelPDF_Flag	String	User value	No reset	Any	No	Y	Group Edel PDF Enable/Disable Flag. Used in Queue Rules.
1443	GO_01_IndivArchPDF_Flag	String	User value	No reset	Any	No	Y	Individual Arch PDF Enable/Disable Flag. Used in Queue Rules.
1444	GO_01_IndivEdelPDF_Flag	String	User value	No reset	Any	No	Y	Individual Edel PDF Enable/Disable Flag. Used in Queue Rules.
1040	CF_FromTiaaNumber	String	File	Automatically	Any	No		CF_FromTiaaNumber
1042	CF_FromCrefNumber	String	File	Automatically	Any	No		CF_FromCrefNumber
2411	GO_01_MOB_OrchestrationID	String	Formula	Before each customer	Any	No		GO_01_MOB_OrchestrationID
3622	GO_DR_TRANSACTION_REQ_ID	String	File	Automatically	Any	No	*********	GO_DR_TRANSACTION_REQ_ID
4178	GO_01_MOB_Composite_OrchestrationID	String	Formula	Before each customer	Any	No		GO_01_MOB_Composite_OrchestrationID
4370	GO_01_MOB_Bus_Code	String	Formula	Automatically	Any	No		GO_01_MOB_Bus_Code
4371	GO_01_MOB_Doc_Code	String	Formula	Automatically	Any	No		GO_01_MOB_DocCode
4372	GO_01_MOB_Doc_Version	String	Formula	Before each customer	Any	No		GO_01_MOB_Doc_Version
5352	CF_FromTiaaNumberClosed	String	File	Automatically	Any	No		CF_FromTiaaNumberClosed
5353	CF_FromTiaaNumberOpen	String	File	Automatically	Any	No		CF_FromTiaaNumberOpen
5451	GO_01_MOB_ConFirstName	String	Formula	Automatically	Any	No		GO_01_MOB_ConFirstName
5492	GO_01_MOB_ConLastName	String	Formula	Automatically	Any	No		GO_01_MOB_ConLastName
15365	GO_01_MOB_Unique_Key	String	Formula	Automatically	Any	No		GO_01_MOB_Unique_Key
15456	CF_SubPlanId	String	File	Automatically	Any	No	55555	
15457	CF_Apin	String	File	Automatically	Any	No	44444	
15458	CF_BulkTradeBatchNum	String	File	Automatically	Any	No	77	
16069	FR_BAO_ProposalNumber	String	File	Automatically	Any	No	Traditional IRA	
16954	GO_01_MOB_TIAAID	String	Formula	Automatically	Any	No		GO_01_MOB_TIAAID
17617	GO_DELIVERY_METHOD	String	File	Automatically	Any	No	*********	GO_DELIVERY_METHOD
18325	GO_01_MOB_CustNum	String	Formula	Before each customer	Any	No		GO_01_MOB_CustNum
1141	GO_01_MOB_DocTypeID	String	Formula	Automatically	Any	No	DCSD1234ST	GO_01_MOB_DocTypeID
1143	GO_01_MOB_Email_TemplateID	String	User value	Automatically	Any	No	PTRKTREJ	GO_01_MOB_Email_TemplateID
1144	GO_01_MOB_Delivery_Type	String	Formula	Before each customer	Any	No	PR	GO_01_MOB_Delivery_Type
1150	GO_01_MOB_Seq_Number	String	Formula	Automatically	Any	No		GO_01_MOB_Seq_Number
1151	GO_01_MOB_Pin_Number	String	Formula	Before each customer	Any	No		GO_01_Mobius_Data_Header
1152	GO_01_MOB_Email	String	Formula	Before each customer	Any	No		GO_01_MOB_Email
1153	GO_01_MOB_Prefix	String	Formula	Before each customer	Any	No		GO_01_MOB_Prefix
1154	GO_01_MOB_Last_Name	String	Formula	Before each customer	Any	No		GO_01_MOB_Last_Name
1155	GO_01_MOB_First_Name	String	Formula	Before each customer	Any	No		GO_01_MOB_First_Name
1156	GO_01_MOB_Middle_Name	String	Formula	Before each customer	Any	No		GO_01_MOB_Middle_Name
1157	GO_01_MOB_Suffix	String	Formula	Automatically	Any	No		
1205	GO_01_MOB_Approved_Flag	String	User value	Automatically	Any	No	Y	GO_01_MOB_Approved_Flag
1230	GO_01_MOB_DocID	String	Formula	Before each customer	Any	No		GO_01_MOB_DocID
1276	GO_01_MOB_PlanID	String	Formula	Before each customer	Any	No		GO_01_MOB_PlanID
1277	GO_01_MOB_Bus_Date	Date	Formula	Automatically	Any	No		GO_01_MOB_Bus_Date
1756	GO_01_MOB_Descr	String	Formula	Automatically	Any	No	Fee Confirmation Statement	GO_01_MOB_Descr
739	GO_AR_RPT_ID	String	User value	Automatically	Any	No		Audit Report
5389	OA_FirstName	String	File	Before each customer	Any	No		OA_FirstName
10006	RPPM_FirstName	String	File	Before each customer	Any	No		OA_FirstName
16949	IL_Consultant_FirstName	String	File	Automatically	Any	No	John	
5392	OA_LastName	String	File	Before each customer	Any	No		OA_LastName
10008	RPPM_LastName	String	File	Before each customer	Any	No		OA_LastName
16950	IL_Consultant_LastName	String	File	Automatically	Any	No	Doe	
15417	GO_DR_UNIQUE_KEY	String	File	Automatically	Any	No	831035185603	GO_DR_UNIQUE_KEY
16953	QD_TIAAID	String	User value	Automatically	Any	No		new field for MOB02 key
1313	GO_MI_UniversalType	String	User value	Automatically	Any	No	001001201103251658000000001	GO_MI_UniversalType
5393	GO_OVERRIDE_NON_PRINT_DELIVERY_TYPE	String	User value	Automatically	Any	No	EMAIL	GO_OVERRIDE_NON_PRINT_DELIVERY_TYPE
18308	GO_01_MOB_PIN_OVERRIDE	String	User value	Automatically	Any	No		used to add a prefix to pin
1142	GO_01_MOB_Doc_Descr	String	User value	Automatically	Any	No	Transfer Reject Letters for Open Plan Solutions	GO_01_MOB_Doc_Descr
