custom_id	custom_id_original	formula_clean	variables	var_to_tag
237087	237087	FR_02_CurrentForm(SYS_SubDocInDocument) = "Passthrough" \\nFR_02_CurrentVersionDate(SYS_SubDocInDocument) = "1111"\\n\\nIF Instr(1,"A14175,A11456,A14174,F11032,F11418,ADV123,A12440,TD12345,A40008,A10813",FR_BAO_FormNumber)<> 0 THEN\\nINCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}]	{'FR_BAO_FormNumber': 'FormNumber'}
356859_356860	356859_356860	FR_LetterType = 'A11929'	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
363185	363185	FR_LetterType = 'A14482'	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
363186	363186	FR_LetterType = 'A14483'	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
346632	346632	FR_LetterType = 'C11981A'	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
356918	356918	FR_LetterType = 'A14795'	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
376817	376817	IF(FR_BAO_FormNumber = "F11035") THEN\\n    FR_02_WPID= "TBRIA"\\n    FR_02_FormId = "F11035 (7/24)"\\n    FR_02_CurrentForm(SYS_SubDocInDocument) = "F11035" \\n    FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0724"\\n\\n   INCLUDE\\n\\n      IF (BAO_NonIRAOption_2 = "Trust" OR BAO_NonIRAOption_2 = "Gifts/Transfer to Minors") THEN\\n         BAO_Captiva_2D_Barcode_flag = "N"\\n      ELSE\\n\\n         BAO_Captiva_2D_Barcode_flag = "Y"       \\n      ENDIF\\nENDIF	[{'name': 'BAO_Captiva_2D_Barcode_flag', 'type': 'String', 'is-array': False}, {'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_WPID', 'type': 'String', 'is-array': False}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'BAO_NonIRAOption_2', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'BAO_NonIRAOption_2': 'NonIraOption'}
377601_290295_343528	377601_290295_343528	FR_BAO_NonIraOption = 'Individual'	[{'name': 'FR_BAO_NonIraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_NonIraOption': 'NonIraOption'}
377602_290311_343539	377602_290311_343539	FR_BAO_NonIraOption = 'Joint'	[{'name': 'FR_BAO_NonIraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_NonIraOption': 'NonIraOption'}
377603_290296_343529	377603_290296_343529	FR_BAO_NonIraOption = 'Joint Tenants in Common'	[{'name': 'FR_BAO_NonIraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_NonIraOption': 'NonIraOption'}
377604_290312	377604_290312	FR_BAO_NonIraOption = 'Trust'	[{'name': 'FR_BAO_NonIraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_NonIraOption': 'NonIraOption'}
377605_290297_343540	377605_290297_343540	FR_BAO_NonIraOption = 'Gifts/Transfer to Minors'	[{'name': 'FR_BAO_NonIraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_NonIraOption': 'NonIraOption'}
377606_290322_343530	377606_290322_343530	FR_BAO_NonIraOption = 'Other'	[{'name': 'FR_BAO_NonIraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NonIraOption'}}]	{'FR_BAO_NonIraOption': 'NonIraOption'}
377560_290282	377560_290282	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustAccountName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustAccountName, 34, "L")\\n    FOR iA = 1 TO 34\\n            FR_BAO_02_TrustAccountName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 34\\n               FR_BAO_02_TrustAccountName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustAccountName', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustAccountName', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustAccountName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}]	{'FR_BAO_TrustAccountName': 'TrustAccountName'}
377561	377561	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustSSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustSSN, 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_BAO_02_TrustSSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n          FR_BAO_02_TrustSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_TrustSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_TrustEffectiveDate) >= 1) THEN\\n    IRA_fx_LoadArray(Format(FR_BAO_TrustEffectiveDate,"mmddyyyy"), 8, "L")\\n    FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustEffectiveDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_TrustSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustEffectiveDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'TrustEffectiveDate'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}]	{'FR_BAO_TrustEffectiveDate': 'TrustEffectiveDate', 'FR_BAO_TrustSSN': 'TrustSSN'}
377562_343969_290284_376557_342186_343099_340011_343517_377272_339130	377562_343969_290284_376557_342186_343099_340011_343517_377272_339130	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerTitle) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerTitle(1), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Title(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Title(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerFirstName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerFirstName(1), 27, "L")\\n    FOR iA = 1 TO 27\\n            FR_02_FirstName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 27\\n               FR_02_FirstName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerMiddleName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(1), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Title', 'type': 'String', 'is-array': True}, {'name': 'FR_02_FirstName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Prefix'}}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerFirstName': 'FirstName', 'FR_AccountOwnerTitle': 'Prefix', 'FR_AccountOwnerMiddleName': 'MiddleName'}
377563_343970_290285_376556_342187_343100_340012_343518_377273_339131	377563_343970_290285_376556_342187_343100_340012_343518_377273_339131	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerLastName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerLastName(1), 29, "L")\\n    FOR iA = 1 TO 29\\n            FR_02_LastName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 29\\n               FR_02_LastName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerSuffix) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSuffix(1), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_LastName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSuffix', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Suffix'}}]	{'FR_AccountOwnerLastName': 'LastName', 'FR_AccountOwnerSuffix': 'Suffix'}
377564_290286_377274	377564_290286_377274	IF COMMON.GO_MI_PCKGE_DLVRY_TYP = "C" THEN \\n        FR_BAO_02_AccountOwnerSSN2 = "false"\\n    ELSE\\n        FR_BAO_02_AccountOwnerSSN2 = "true"\\n    ENDIF\\n\\n    IF COMMON.GO_MI_PCKGE_DLVRY_TYP = "C" THEN \\n        FR_BAO_02_AccountOwner_BirthDate2 = "false"\\n    ELSE\\n        FR_BAO_02_AccountOwner_BirthDate2 = "true"\\n    ENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwnerSSN2', 'type': 'String', 'is-array': False}, {'name': 'FR_BAO_02_AccountOwner_BirthDate2', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
377565	377565	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n\	\	    FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate'}
377566_343973_290288_376566_342190_343103_340015_343521_377276_339134	377566_343973_290288_376566_342190_343103_340015_343521_377276_339134	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerResidentialState) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerResidentialState(1), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerResidentialState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_AccountOwnerResidentialState': 'ResidentialState'}
377567_343974_290289_376570_342191_343104_340016_343522_377277_339135	377567_343974_290289_376570_342191_343104_340016_343522_377277_339135	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerMailingState) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMailingState(1), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMailingState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingState'}}]	{'FR_AccountOwnerMailingState': 'MailingState'}
377568_343975_290290_376559_342192_343105_340017_343523_377278_339136	377568_343975_290290_376559_342192_343105_340017_343523_377278_339136	IF Count(FR_AccountOwnerGender) >= 1 THEN\\n    IF(FR_AccountOwnerGender(1) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
377569_343976_290291_342193_343106_340018_343524_339137	377569_343976_290291_342193_343106_340018_343524_339137	IF Count(FR_AccountOwnerGender) >= 1 THEN\\n    IF(FR_AccountOwnerGender(1) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
377570_377637_377680_290292_245320_245350_377280_377281	377570_377637_377680_290292_245320_245350_377280_377281	FR_BAO_02_AccountOwnerSSN2 = 'false'	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwnerSSN2', 'type': 'String', 'is-array': False}]	{}
377571_343978_290293_376567_342196_343109_340020_343526_377282_339141	377571_343978_290293_376567_342196_343109_340020_343526_377282_339141	IF(count(FR_AccountOwnerResidentialStreetAddress) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_AccountOwnerResidentialStreetAddress': 'ResidentialStreetAddress'}
377572_343979_290294_376569_342197_343110_340021_343527_377283_339142	377572_343979_290294_376569_342197_343110_340021_343527_377283_339142	IF(count(FR_AccountOwnerResidentialZipcode) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_AccountOwnerResidentialZipcode': 'ResidentialZipcode'}
377576_377639_377682_290298_245362_245363_343531_344925_344950_344975_345000_345025_377287	377576_377639_377682_290298_245362_245363_343531_344925_344950_344975_345000_345025_377287	FR_BAO_02_AccountOwner_BirthDate2 = 'false'	[{'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate2', 'type': 'String', 'is-array': False}]	{}
377577_341864_343982_290299_341104_342198_343111_340024_343532_377288_339144	377577_341864_343982_290299_341104_342198_343111_340024_343532_377288_339144	IF Count(FR_AccountOwnerMaritalStatus) >= 1 THEN\\n    IF(FR_AccountOwnerMaritalStatus(1) = "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
377578_341865_343983_290300_341105_342199_343112_340025_343533_377289_339145	377578_341865_343983_290300_341105_342199_343112_340025_343533_377289_339145	IF Count(FR_AccountOwnerMaritalStatus) >= 1 THEN\\n    IF(FR_AccountOwnerMaritalStatus(1) <> "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
377579_343984_290301_376561_342200_343113_340026_343534_377290_339146	377579_343984_290301_376561_342200_343113_340026_343534_377290_339146	IF Count(FR_AccountOwnerCitizenship) >= 1 THEN\\n    IF FR_AccountOwnerCitizenship(1) <> "US" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerCitizenship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Citzenship'}}]	{'FR_AccountOwnerCitizenship': 'Citzenship'}
377580_343985_290302_376568_342201_343114_340027_343535_377291_339147	377580_343985_290302_376568_342201_343114_340027_343535_377291_339147	IF(count(FR_AccountOwnerResidentialCity) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_AccountOwnerResidentialCity': 'ResidentialCity'}
377581_343986_290303_376571_342202_343115_340028_343536_377292_339148	377581_343986_290303_376571_342202_343115_340028_343536_377292_339148	IF(count(FR_AccountOwnerMailingStreetAddress) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingStreetAddress'}}]	{'FR_AccountOwnerMailingStreetAddress': 'MailingStreetAddress'}
377582_343987_290304_376573_342203_343116_340029_343537_377293_339149	377582_343987_290304_376573_342203_343116_340029_343537_377293_339149	IF(count(FR_AccountOwnerMailingZipcode) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingZipcode'}}]	{'FR_AccountOwnerMailingZipcode': 'MailingZipcode'}
377583_343988_290305_376572_342204_343117_340030_343538_377294_339150	377583_343988_290305_376572_342204_343117_340030_343538_377294_339150	IF(count(FR_AccountOwnerMailingCity) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingCity'}}]	{'FR_AccountOwnerMailingCity': 'MailingCity'}
377584_343992_290306_376562_342194_343107_340034_343541_377295_339138	377584_343992_290306_376562_342194_343107_340034_343541_377295_339138	IF(count(FR_AccountOwnerEmail) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerEmail', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmailAddress'}}]	{'FR_AccountOwnerEmail': 'EmailAddress'}
377585_290307_377296	377585_290307_377296	IF(count(FR_AccountOwnerHomePhone) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerHomePhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'HomePhone'}}]	{'FR_AccountOwnerHomePhone': 'HomePhone'}
377586_290308_377297	377586_290308_377297	IF(count(FR_AccountOwnerHomeExtension) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerHomeExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'HomeExtension'}}]	{'FR_AccountOwnerHomeExtension': 'HomeExtension'}
377587_343989_290309_342205_343118_340031_343542_377298_339151	377587_343989_290309_342205_343118_340031_343542_377298_339151	IF(count(FR_AccountOwnerBusinessPhone) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerBusinessPhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BusinessPhone'}}]	{'FR_AccountOwnerBusinessPhone': 'BusinessPhone'}
377588_343990_290310_342206_343119_340032_343543_377299_339152	377588_343990_290310_342206_343119_340032_343543_377299_339152	IF(count(FR_AccountOwnerExtension) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Extension'}}]	{'FR_AccountOwnerExtension': 'Extension'}
377591_377592_377593_377594_377595_377596_377597_377598_377599_290313_290314_290315_290316_290317_290318_290319_290320_290321_342209_342210_342211_342212_342213_342214_342215_342216_342217_342892_342893_342894_342895_342896_342897_342898_342899_342900_377486_377487_377488_377489_377490_377491_377492_377493_377494	377591_377592_377593_377594_377595_377596_377597_377598_377599_290313_290314_290315_290316_290317_290318_290319_290320_290321_342209_342210_342211_342212_342213_342214_342215_342216_342217_342892_342893_342894_342895_342896_342897_342898_342899_342900_377486_377487_377488_377489_377490_377491_377492_377493_377494	IF(FR_BAO_TrustSSN <> "") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}]	{'FR_BAO_TrustSSN': 'TrustSSN'}
377608_341633_245280_376631_342228_343131_340038_343547_377302_339251	377608_341633_245280_376631_342228_343131_340038_343547_377302_339251	DIM iA AS Integer\\n\\nIF(Count(FR_BAO_EmployerState) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_EmployerState(1), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_EmployerState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerState'}}]	{'FR_BAO_EmployerState': 'EmployerState'}
377609_245285_376615_377303	377609_245285_376615_377303	IF Count(FR_BAO_EmploymentStatus) >= 1 THEN\\n    IF(FR_BAO_EmploymentStatus(1) = "UEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377610_245286_376616_377304	377610_245286_376616_377304	IF Count(FR_BAO_EmploymentStatus) >= 1 THEN\\n    IF(FR_BAO_EmploymentStatus(1) = "RETD") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377611_245287_377305	377611_245287_377305	IF(count(FR_BAO_SourceOfIncome) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfIncome', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfIncome'}}]	{'FR_BAO_SourceOfIncome': 'SourceOfIncome'}
377612_341635_245288_376617_342235_343133_340040_343554_377306_339253	377612_341635_245288_376617_342235_343133_340040_343554_377306_339253	IF Count(FR_BAO_EmploymentStatus) >= 1 THEN\\n    IF(FR_BAO_EmploymentStatus(1) = "EMPL" OR FR_BAO_EmploymentStatus(1) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377613_341636_245289_376623_342236_343134_340041_343555_377311_339254	377613_341636_245289_376623_342236_343134_340041_343555_377311_339254	IF(count(FR_BAO_EmployerName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerName'}}]	{'FR_BAO_EmployerName': 'EmployerName'}
377614_341637_245290_376630_342237_343135_340042_343556_377312_339255	377614_341637_245290_376630_342237_343135_340042_343556_377312_339255	IF(count(FR_BAO_EmployerTitle) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentTitle'}}]	{'FR_BAO_EmployerTitle': 'EmploymentTitle'}
377615_341638_245291_376632_342238_343136_340043_343557_377313_339256	377615_341638_245291_376632_342238_343136_340043_343557_377313_339256	IF(count(FR_BAO_EmployerAddress) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerStreetAddress'}}]	{'FR_BAO_EmployerAddress': 'EmployerStreetAddress'}
377616_341639_245292_376634_342239_343137_340044_343558_377314_339257	377616_341639_245292_376634_342239_343137_340044_343558_377314_339257	IF(count(FR_BAO_EmployerZipcode) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerZipcode'}}]	{'FR_BAO_EmployerZipcode': 'EmployerZipcode'}
377617_341640_245293_376633_342240_343138_340045_343559_377315_339258	377617_341640_245293_376633_342240_343138_340045_343559_377315_339258	IF(count(FR_BAO_EmployerCity) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerCity'}}]	{'FR_BAO_EmployerCity': 'EmployerCity'}
377618_341641_245294_376620_342241_343139_340046_343560_377316_339259	377618_341641_245294_376620_342241_343139_340046_343560_377316_339259	IF Count(FR_AccountOwnerTradeCompanyCB) >= 1 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
377619_341642_245295_376618_342242_343140_340047_343561_377317_339260	377619_341642_245295_376618_342242_343140_340047_343561_377317_339260	IF(count(FR_BAO_Trade_PersonName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfPerson'}}]	{'FR_BAO_Trade_PersonName': 'NameOfPerson'}
377620_341643_245296_376619_342243_343141_340048_343562_377318_339261	377620_341643_245296_376619_342243_343141_340048_343562_377318_339261	IF(count(FR_BAO_Trade_CompanyNameSymbol) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_CompanyNameSymbol', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'CompanyNameSymbol'}}]	{'FR_BAO_Trade_CompanyNameSymbol': 'CompanyNameSymbol'}
377621_245297_342244_343142_343563	377621_245297_342244_343142_343563	IF Count(FR_AccountOwnerTIAACREFCB) >= 1 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 1 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
377622_341646_245298_376624_342245_343143_340051_343564_377321_339264	377622_341646_245298_376624_342245_343143_340051_343564_377321_339264	IF(count(FR_BAO_Firm_Relationship) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_Firm_Relationship': 'RelationshipToEmployee'}
377623_341647_245299_376625_342246_343144_340052_343565_377322_339265	377623_341647_245299_376625_342246_343144_340052_343565_377322_339265	IF(count(FR_BAO_TIAACREF_Relationship) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_TIAACREF_Relationship': 'RelationshipToEmployee'}
377624_341648_245300_376626_342247_343145_343146_340053_343566_377323_339266	377624_341648_245300_376626_342247_343145_343146_340053_343566_377323_339266	IF(count(FR_BAO_Firm_PersonName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_Firm_PersonName': 'NameOfEmployee'}
377625_341649_245301_376627_342248_340054_343567_377324_339267	377625_341649_245301_376627_342248_340054_343567_377324_339267	IF(count(FR_BAO_TIAACREF_PersonName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_TIAACREF_PersonName': 'NameOfEmployee'}
377626_341650_245302_376628_342249_343147_340055_343568_377325_339268	377626_341650_245302_376628_342249_343147_340055_343568_377325_339268	IF(count(FR_BAO_Firm_Name) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Name', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfFirm'}}]	{'FR_BAO_Firm_Name': 'NameOfFirm'}
377627_341651_245303_376629_342250_343148_340056_343569_377326_339269	377627_341651_245303_376629_342250_343148_340056_343569_377326_339269	IF Count(FR_AccountOwnerTIAACREFCB) >= 1 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox'}
377628_341644_245304_376622_342251_343149_340049_343570_377319_339262	377628_341644_245304_376622_342251_343149_340049_343570_377319_339262	IF Count(FR_AccountOwnerSeniorMilitaryCB) >= 1 THEN\\n    IF(FR_AccountOwnerSeniorMilitaryCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerSeniorMilitaryCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SeniorMilitaryCheckBox'}}]	{'FR_AccountOwnerSeniorMilitaryCB': 'SeniorMilitaryCheckBox'}
377630	377630	IF Count(FR_AccountOwnerSSN) >= 2 AND COMMON.GO_MI_PCKGE_DLVRY_TYP = "C" THEN\\n        FR_BAO_02_AccountOwnerSSN2 = "false"\\nELSE\\n        FR_BAO_02_AccountOwnerSSN2 = "true"\\nENDIF\\n\\nIF Count(FR_AccountOwnerBirthDate) >= 2 AND COMMON.GO_MI_PCKGE_DLVRY_TYP = "C" THEN\\n        FR_BAO_02_AccountOwner_BirthDate2 = "false"\\nELSE\\n        FR_BAO_02_AccountOwner_BirthDate2 = "true"\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwnerSSN2', 'type': 'String', 'is-array': False}, {'name': 'FR_BAO_02_AccountOwner_BirthDate2', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType', 'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate'}
377631	377631	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(2), 9, "L")\\n    FOR iA = 1 TO 5\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(2), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate'}
377632_245283_342232_343207_344918	377632_245283_342232_343207_344918	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerResidentialState) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerResidentialState(2), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerResidentialState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_AccountOwnerResidentialState': 'ResidentialState'}
377633_245284_342233_343208_344919	377633_245284_342233_343208_344919	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerMailingState) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMailingState(2), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMailingState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingState'}}]	{'FR_AccountOwnerMailingState': 'MailingState'}
377634_245321_342284_343185_343591	377634_245321_342284_343185_343591	DIM iA AS Integer\\n\\nIF(Count(FR_BAO_EmployerState) >= 2) THEN\\n    IRA_fx_LoadArray(FR_BAO_EmployerState(2), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_EmployerState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerState'}}]	{'FR_BAO_EmployerState': 'EmployerState'}
377635_245318_342263_343222_344920	377635_245318_342263_343222_344920	IF Count(FR_AccountOwnerGender) >= 2 THEN\\n    IF(FR_AccountOwnerGender(2) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
377636_245319_342264_343223_344921	377636_245319_342264_343223_344921	IF Count(FR_AccountOwnerGender) >= 2 THEN\\n    IF(FR_AccountOwnerGender(2) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
377638_245310_342257_343216_344923	377638_245310_342257_343216_344923	IF(count(FR_AccountOwnerResidentialStreetAddress) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_AccountOwnerResidentialStreetAddress': 'ResidentialStreetAddress'}
377640_245313_342260_343219_344926	377640_245313_342260_343219_344926	IF Count(FR_AccountOwnerMaritalStatus) >= 2 THEN\\n    IF(FR_AccountOwnerMaritalStatus(2) = "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
377641_245314_342261_343220_344927	377641_245314_342261_343220_344927	IF Count(FR_AccountOwnerMaritalStatus) >= 2 THEN\\n    IF(FR_AccountOwnerMaritalStatus(2) <> "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
377642_245307_342254_343213_344930	377642_245307_342254_343213_344930	IF(count(FR_AccountOwnerMailingStreetAddress) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingStreetAddress'}}]	{'FR_AccountOwnerMailingStreetAddress': 'MailingStreetAddress'}
377643_245322	377643_245322	IF Count(FR_BAO_EmploymentStatus) >= 2 THEN\\n    IF(FR_BAO_EmploymentStatus(2) = "UEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377644_245325_342291_343187_343593	377644_245325_342291_343187_343593	IF Count(FR_BAO_EmploymentStatus) >= 2 THEN\\n    IF(FR_BAO_EmploymentStatus(2) = "EMPL" OR FR_BAO_EmploymentStatus(2) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377645_377689_245326_230762_342292_343188_343594	377645_377689_245326_230762_342292_343188_343594	IF(count(FR_BAO_EmployerName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerName'}}]	{'FR_BAO_EmployerName': 'EmployerName'}
377646_245327_342294_343190_343595	377646_245327_342294_343190_343595	IF(count(FR_BAO_EmployerAddress) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerStreetAddress'}}]	{'FR_BAO_EmployerAddress': 'EmployerStreetAddress'}
377647_245328_342297_343193_343596	377647_245328_342297_343193_343596	IF Count(FR_AccountOwnerTradeCompanyCB) >= 2 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
377648_245329_342298_343194_343597	377648_245329_342298_343194_343597	IF(count(FR_BAO_Trade_PersonName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfPerson'}}]	{'FR_BAO_Trade_PersonName': 'NameOfPerson'}
377649_245330_342300_343196_343598	377649_245330_342300_343196_343598	IF Count(FR_AccountOwnerTIAACREFCB) >= 2 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 2 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
377650_245331_342301_343197_343599	377650_245331_342301_343197_343599	IF(count(FR_BAO_Firm_Relationship) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_Firm_Relationship': 'RelationshipToEmployee'}
377651_245332_342302_343198_343600	377651_245332_342302_343198_343600	IF(count(FR_BAO_TIAACREF_Relationship) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_TIAACREF_Relationship': 'RelationshipToEmployee'}
377652_245338_342303_343199_343606	377652_245338_342303_343199_343606	IF(count(FR_BAO_Firm_PersonName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_Firm_PersonName': 'NameOfEmployee'}
377653_245339_342304_343200_343607	377653_245339_342304_343200_343607	IF(count(FR_BAO_TIAACREF_PersonName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_TIAACREF_PersonName': 'NameOfEmployee'}
377654_245333_342307_343203_343601	377654_245333_342307_343203_343601	IF Count(FR_AccountOwnerSeniorMilitaryCB) >= 2 THEN\\n    IF(FR_AccountOwnerSeniorMilitaryCB(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerSeniorMilitaryCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SeniorMilitaryCheckBox'}}]	{'FR_AccountOwnerSeniorMilitaryCB': 'SeniorMilitaryCheckBox'}
377655_245312_342259_343218_344924	377655_245312_342259_343218_344924	IF(count(FR_AccountOwnerResidentialZipcode) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_AccountOwnerResidentialZipcode': 'ResidentialZipcode'}
377656_245323	377656_245323	IF Count(FR_BAO_EmploymentStatus) >= 2 THEN\\n    IF(FR_BAO_EmploymentStatus(2) = "RETD") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377657_245324	377657_245324	IF(count(FR_BAO_SourceOfIncome) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfIncome', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfIncome'}}]	{'FR_BAO_SourceOfIncome': 'SourceOfIncome'}
377658_245317_342262_343221_344928	377658_245317_342262_343221_344928	IF Count(FR_AccountOwnerCitizenship) >= 2 THEN\\n    IF FR_AccountOwnerCitizenship(2) <> "US" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerCitizenship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Citzenship'}}]	{'FR_AccountOwnerCitizenship': 'Citzenship'}
377659_245311_342258_343217_344929	377659_245311_342258_343217_344929	IF(count(FR_AccountOwnerResidentialCity) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_AccountOwnerResidentialCity': 'ResidentialCity'}
377660_245308_342255_343214_344932	377660_245308_342255_343214_344932	IF(count(FR_AccountOwnerMailingCity) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingCity'}}]	{'FR_AccountOwnerMailingCity': 'MailingCity'}
377661_245309_342256_343215_344931	377661_245309_342256_343215_344931	IF(count(FR_AccountOwnerMailingZipcode) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingZipcode'}}]	{'FR_AccountOwnerMailingZipcode': 'MailingZipcode'}
377662_245335_342293_343189_343603	377662_245335_342293_343189_343603	IF(count(FR_BAO_EmployerTitle) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentTitle'}}]	{'FR_BAO_EmployerTitle': 'EmploymentTitle'}
377663_245336_342296_343192_343604	377663_245336_342296_343192_343604	IF(count(FR_BAO_EmployerCity) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerCity'}}]	{'FR_BAO_EmployerCity': 'EmployerCity'}
377664_245334_342295_343191_343316_343602	377664_245334_342295_343191_343316_343602	IF(count(FR_BAO_EmployerZipcode) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerZipcode'}}]	{'FR_BAO_EmployerZipcode': 'EmployerZipcode'}
377665_245337_342299_343195_343605	377665_245337_342299_343195_343605	IF(count(FR_BAO_Trade_CompanyNameSymbol) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_CompanyNameSymbol', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'CompanyNameSymbol'}}]	{'FR_BAO_Trade_CompanyNameSymbol': 'CompanyNameSymbol'}
377666_245340_342305_343201_343608	377666_245340_342305_343201_343608	IF(count(FR_BAO_Firm_Name) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Name', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfFirm'}}]	{'FR_BAO_Firm_Name': 'NameOfFirm'}
377667_245341_342306_343202_343609	377667_245341_342306_343202_343609	IF Count(FR_AccountOwnerTIAACREFCB) >= 2 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox'}
377668_245315_342267_343226_344934	377668_245315_342267_343226_344934	IF(count(FR_AccountOwnerBusinessPhone) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerBusinessPhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BusinessPhone'}}]	{'FR_AccountOwnerBusinessPhone': 'BusinessPhone'}
377669_245316_342268_343227_344935	377669_245316_342268_343227_344935	IF(count(FR_AccountOwnerExtension) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Extension'}}]	{'FR_AccountOwnerExtension': 'Extension'}
377670_344914	377670_344914	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerTitle) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerTitle(2), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Title(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Title(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerFirstName) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerFirstName(2), 27, "L")\\n    FOR iA = 1 TO 27\\n            FR_02_FirstName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 27\\n               FR_02_FirstName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerMiddleName) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(2), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Title', 'type': 'String', 'is-array': True}, {'name': 'FR_02_FirstName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Prefix'}}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerFirstName': 'FirstName', 'FR_AccountOwnerTitle': 'Prefix', 'FR_AccountOwnerMiddleName': 'MiddleName'}
377671_344915	377671_344915	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerLastName) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerLastName(2), 29, "L")\\n    FOR iA = 1 TO 29\\n            FR_02_LastName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 29\\n               FR_02_LastName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerSuffix) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSuffix(2), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_LastName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSuffix', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Suffix'}}]	{'FR_AccountOwnerLastName': 'LastName', 'FR_AccountOwnerSuffix': 'Suffix'}
377673	377673	IF Count(FR_AccountOwnerSSN) >= 3 AND COMMON.GO_MI_PCKGE_DLVRY_TYP = "C" THEN\\n        FR_BAO_02_AccountOwnerSSN2 = "false"\\nELSE\\n        FR_BAO_02_AccountOwnerSSN2 = "true"\\nENDIF\\n\\nIF Count(FR_AccountOwnerBirthDate) >= 3 AND COMMON.GO_MI_PCKGE_DLVRY_TYP = "C" THEN\\n        FR_BAO_02_AccountOwner_BirthDate2 = "false"\\nELSE\\n        FR_BAO_02_AccountOwner_BirthDate2 = "true"\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwnerSSN2', 'type': 'String', 'is-array': False}, {'name': 'FR_BAO_02_AccountOwner_BirthDate2', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType', 'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate'}
377674	377674	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(3), 9, "L")\\n    FOR iA = 1 TO 5\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(3), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate'}
377675_245344_342288_343255_344943	377675_245344_342288_343255_344943	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerResidentialState) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerResidentialState(3), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerResidentialState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_AccountOwnerResidentialState': 'ResidentialState'}
377676_245345_342289_343256_344944	377676_245345_342289_343256_344944	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerMailingState) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMailingState(3), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMailingState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingState'}}]	{'FR_AccountOwnerMailingState': 'MailingState'}
377677_230758_342332_343233_343635	377677_230758_342332_343233_343635	DIM iA AS Integer\\n\\nIF(Count(FR_BAO_EmployerState) >= 3) THEN\\n    IRA_fx_LoadArray(FR_BAO_EmployerState(3), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_EmployerState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerState'}}]	{'FR_BAO_EmployerState': 'EmployerState'}
377678_245348_342319_343270_344945	377678_245348_342319_343270_344945	IF Count(FR_AccountOwnerGender) >= 3 THEN\\n    IF(FR_AccountOwnerGender(3) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
377679_245349_342320_343271_344946	377679_245349_342320_343271_344946	IF Count(FR_AccountOwnerGender) >= 3 THEN\\n    IF(FR_AccountOwnerGender(3) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
377681_245351_342313_343264_344948	377681_245351_342313_343264_344948	IF(count(FR_AccountOwnerResidentialStreetAddress) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_AccountOwnerResidentialStreetAddress': 'ResidentialStreetAddress'}
377683_245352_342316_343267_344951	377683_245352_342316_343267_344951	IF Count(FR_AccountOwnerMaritalStatus) >= 3 THEN\\n    IF(FR_AccountOwnerMaritalStatus(3) = "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
377684_245353_342317_343268_344952	377684_245353_342317_343268_344952	IF Count(FR_AccountOwnerMaritalStatus) >= 3 THEN\\n    IF(FR_AccountOwnerMaritalStatus(3) <> "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
377685_245354_342310_343261_344955	377685_245354_342310_343261_344955	IF(count(FR_AccountOwnerMailingStreetAddress) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingStreetAddress'}}]	{'FR_AccountOwnerMailingStreetAddress': 'MailingStreetAddress'}
377686_230759	377686_230759	IF Count(FR_BAO_EmploymentStatus) >= 3 THEN\\n    IF(FR_BAO_EmploymentStatus(3) = "UEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377687_230760	377687_230760	IF(count(FR_BAO_SourceOfIncome) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfIncome', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfIncome'}}]	{'FR_BAO_SourceOfIncome': 'SourceOfIncome'}
377688_230761	377688_230761	IF Count(FR_BAO_EmploymentStatus) >= 3 THEN\\n    IF(FR_BAO_EmploymentStatus(3) = "CONSL") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377690_230763_342342_343238_343639	377690_230763_342342_343238_343639	IF(count(FR_BAO_EmployerAddress) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerStreetAddress'}}]	{'FR_BAO_EmployerAddress': 'EmployerStreetAddress'}
377691_230764_342345_343241_343640	377691_230764_342345_343241_343640	IF Count(FR_AccountOwnerTradeCompanyCB) >= 3 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
377692_230765_342346_343242_343641	377692_230765_342346_343242_343641	IF(count(FR_BAO_Trade_PersonName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfPerson'}}]	{'FR_BAO_Trade_PersonName': 'NameOfPerson'}
377693_230766_342348_343244_343642	377693_230766_342348_343244_343642	IF Count(FR_AccountOwnerTIAACREFCB) >= 3 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 3 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
377694_230767_342349_343245_343643	377694_230767_342349_343245_343643	IF(count(FR_BAO_Firm_Relationship) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_Firm_Relationship': 'RelationshipToEmployee'}
377695_230768_342350_343246_343644	377695_230768_342350_343246_343644	IF(count(FR_BAO_TIAACREF_Relationship) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_TIAACREF_Relationship': 'RelationshipToEmployee'}
377696_230769_342351_343247_343645	377696_230769_342351_343247_343645	IF(count(FR_BAO_Firm_PersonName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_Firm_PersonName': 'NameOfEmployee'}
377697_230770_342352_343248_343646	377697_230770_342352_343248_343646	IF(count(FR_BAO_TIAACREF_PersonName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_TIAACREF_PersonName': 'NameOfEmployee'}
377698_230771_342355_343251_343647	377698_230771_342355_343251_343647	IF Count(FR_AccountOwnerSeniorMilitaryCB) >= 3 THEN\\n    IF(FR_AccountOwnerSeniorMilitaryCB(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerSeniorMilitaryCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SeniorMilitaryCheckBox'}}]	{'FR_AccountOwnerSeniorMilitaryCB': 'SeniorMilitaryCheckBox'}
377699_245355_342315_343266_344949	377699_245355_342315_343266_344949	IF(count(FR_AccountOwnerResidentialZipcode) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_AccountOwnerResidentialZipcode': 'ResidentialZipcode'}
377700_230772	377700_230772	IF Count(FR_BAO_EmploymentStatus) >= 3 THEN\\n    IF(FR_BAO_EmploymentStatus(3) = "RETD") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
377701_245358_342318_343269_344953	377701_245358_342318_343269_344953	IF Count(FR_AccountOwnerCitizenship) >= 3 THEN\\n    IF FR_AccountOwnerCitizenship(3) <> "US" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerCitizenship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Citzenship'}}]	{'FR_AccountOwnerCitizenship': 'Citzenship'}
377702_245359_342314_343265_344954	377702_245359_342314_343265_344954	IF(count(FR_AccountOwnerResidentialCity) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_AccountOwnerResidentialCity': 'ResidentialCity'}
377703_245360_342311_343262_344957	377703_245360_342311_343262_344957	IF(count(FR_AccountOwnerMailingCity) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingCity'}}]	{'FR_AccountOwnerMailingCity': 'MailingCity'}
377704_245361_342312_343263_344956	377704_245361_342312_343263_344956	IF(count(FR_AccountOwnerMailingZipcode) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingZipcode'}}]	{'FR_AccountOwnerMailingZipcode': 'MailingZipcode'}
377705_230773_342341_343237_343648	377705_230773_342341_343237_343648	IF(count(FR_BAO_EmployerTitle) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentTitle'}}]	{'FR_BAO_EmployerTitle': 'EmploymentTitle'}
377706_230774_342344_343240_343649	377706_230774_342344_343240_343649	IF(count(FR_BAO_EmployerCity) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerCity'}}]	{'FR_BAO_EmployerCity': 'EmployerCity'}
377707_230775_342343_343239_343650	377707_230775_342343_343239_343650	IF(count(FR_BAO_EmployerZipcode) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerZipcode'}}]	{'FR_BAO_EmployerZipcode': 'EmployerZipcode'}
377708_230776_342347_343243_343651	377708_230776_342347_343243_343651	IF(count(FR_BAO_Trade_CompanyNameSymbol) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_CompanyNameSymbol', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'CompanyNameSymbol'}}]	{'FR_BAO_Trade_CompanyNameSymbol': 'CompanyNameSymbol'}
377709_230777_342353_343249_343652	377709_230777_342353_343249_343652	IF(count(FR_BAO_Firm_Name) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Name', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfFirm'}}]	{'FR_BAO_Firm_Name': 'NameOfFirm'}
377710_230778_342354_343250_343653	377710_230778_342354_343250_343653	IF Count(FR_AccountOwnerTIAACREFCB) >= 3 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox'}
377711_245356_342323_343274_344959	377711_245356_342323_343274_344959	IF(count(FR_AccountOwnerBusinessPhone) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerBusinessPhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BusinessPhone'}}]	{'FR_AccountOwnerBusinessPhone': 'BusinessPhone'}
377712_245357_342324_343275_344960	377712_245357_342324_343275_344960	IF(count(FR_AccountOwnerExtension) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Extension'}}]	{'FR_AccountOwnerExtension': 'Extension'}
377713_344939	377713_344939	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerTitle) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerTitle(3), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Title(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Title(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerFirstName) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerFirstName(3), 27, "L")\\n    FOR iA = 1 TO 27\\n            FR_02_FirstName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 27\\n               FR_02_FirstName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerMiddleName) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(3), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Title', 'type': 'String', 'is-array': True}, {'name': 'FR_02_FirstName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Prefix'}}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerFirstName': 'FirstName', 'FR_AccountOwnerTitle': 'Prefix', 'FR_AccountOwnerMiddleName': 'MiddleName'}
377714_344940	377714_344940	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerLastName) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerLastName(3), 29, "L")\\n    FOR iA = 1 TO 29\\n            FR_02_LastName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 29\\n               FR_02_LastName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerSuffix) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSuffix(3), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_LastName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSuffix', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Suffix'}}]	{'FR_AccountOwnerLastName': 'LastName', 'FR_AccountOwnerSuffix': 'Suffix'}
377527_376639_377307	377527_376639_377307	FR_BAO_InvestmentObjective = 'CAPR'	[{'name': 'FR_BAO_InvestmentObjective', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'InvestmentObjective'}}]	{'FR_BAO_InvestmentObjective': 'InvestmentObjective'}
377528_376644_377336	377528_376644_377336	FR_BAO_InvestmentObjective = 'INCM'	[{'name': 'FR_BAO_InvestmentObjective', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'InvestmentObjective'}}]	{'FR_BAO_InvestmentObjective': 'InvestmentObjective'}
377529_376645_377308	377529_376645_377308	FR_BAO_InvestmentObjective = 'GRTH'	[{'name': 'FR_BAO_InvestmentObjective', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'InvestmentObjective'}}]	{'FR_BAO_InvestmentObjective': 'InvestmentObjective'}
377530_376646_377309	377530_376646_377309	FR_BAO_InvestmentObjective = 'SPEC'	[{'name': 'FR_BAO_InvestmentObjective', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'InvestmentObjective'}}]	{'FR_BAO_InvestmentObjective': 'InvestmentObjective'}
377531	377531	IF(Count(FR_AccountOwnerInvestmentProfile) >= 1) THEN\\n     IF(FR_AccountOwnerInvestmentProfile(1) = "NONE") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerInvestmentProfile', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'InvestmentProfile'}}]	{'FR_AccountOwnerInvestmentProfile': 'InvestmentProfile'}
377532	377532	IF(Count(FR_AccountOwnerInvestmentProfile) >= 1) THEN\\n     IF(FR_AccountOwnerInvestmentProfile(1) = "LIMITED") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerInvestmentProfile', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'InvestmentProfile'}}]	{'FR_AccountOwnerInvestmentProfile': 'InvestmentProfile'}
377533	377533	IF(Count(FR_AccountOwnerInvestmentProfile) >= 1) THEN\\n     IF(FR_AccountOwnerInvestmentProfile(1) = "GOOD") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerInvestmentProfile', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'InvestmentProfile'}}]	{'FR_AccountOwnerInvestmentProfile': 'InvestmentProfile'}
377534	377534	IF(Count(FR_AccountOwnerInvestmentProfile) >= 1) THEN\\n     IF(FR_AccountOwnerInvestmentProfile(1) = "EXTENSIVE") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerInvestmentProfile', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'InvestmentProfile'}}]	{'FR_AccountOwnerInvestmentProfile': 'InvestmentProfile'}
377535_341653_230747_376635_342509_343447_340058_345049_377310_339271	377535_341653_230747_376635_342509_343447_340058_345049_377310_339271	FR_BAO_AnnualIncome = 'UNDER24999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
377536_341658_230748_376640_342510_343448_340063_345050_377331_339276	377536_341658_230748_376640_342510_343448_340063_345050_377331_339276	FR_BAO_AnnualIncome = 'FROM25000TO49999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
377537_230749_376647_377332	377537_230749_376647_377332	FR_BAO_AnnualIncome = 'FROM50000TO99999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
377538_230750_376636_377327	377538_230750_376636_377327	FR_BAO_AnnualIncome = 'FROM100000TO249999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
377539_341663_230738_376641_342516_343454_340068_345056_377340_339281	377539_341663_230738_376641_342516_343454_340068_345056_377340_339281	FR_BAO_AnnualIncome = 'OVER250000'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
377540_341656_230751_376637_342520_343458_340061_345060_377328_339274	377540_341656_230751_376637_342520_343458_340061_345060_377328_339274	FR_BAO_NetWorth = 'UNDER49999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
377541_341664_230752_376642_342521_343459_340069_345061_377337_339282	377541_341664_230752_376642_342521_343459_340069_345061_377337_339282	FR_BAO_NetWorth = 'FROM50000TO99999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
377542_230753_376648_377338	377542_230753_376648_377338	FR_BAO_NetWorth = 'FROM100000TO249999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
377543_230754_376638_377329	377543_230754_376638_377329	FR_BAO_NetWorth = 'FROM250000TOMILLION'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
377544_230755_376643_377339	377544_230755_376643_377339	FR_BAO_NetWorth = 'OVERMILLION'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
377545_377330	377545_377330	FR_BAO_TaxBraket = 'LWTB'	[{'name': 'FR_BAO_TaxBraket', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TaxBraket'}}]	{'FR_BAO_TaxBraket': 'TaxBraket'}
377546_377335	377546_377335	FR_BAO_TaxBraket = 'MDTB'	[{'name': 'FR_BAO_TaxBraket', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TaxBraket'}}]	{'FR_BAO_TaxBraket': 'TaxBraket'}
377547_377334	377547_377334	FR_BAO_TaxBraket = 'HITB'	[{'name': 'FR_BAO_TaxBraket', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TaxBraket'}}]	{'FR_BAO_TaxBraket': 'TaxBraket'}
377548_377333	377548_377333	FR_BAO_TaxBraket = 'TPTB'	[{'name': 'FR_BAO_TaxBraket', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TaxBraket'}}]	{'FR_BAO_TaxBraket': 'TaxBraket'}
377549_230739_343461_345041	377549_230739_343461_345041	IF(Contains(FR_BAO_SourceOfFunds, "Income from Earnings") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377550_230745_343462_345047	377550_230745_343462_345047	IF(Contains(FR_BAO_SourceOfFunds, "Investment Proceeds") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377551_230756_343463_345063	377551_230756_343463_345063	IF(Contains(FR_BAO_SourceOfFunds, "Gift") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377552_230746_343464_345048	377552_230746_343464_345048	IF(Contains(FR_BAO_SourceOfFunds, "Sale of Business") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377553_230740_343465_345042	377553_230740_343465_345042	IF(Contains(FR_BAO_SourceOfFunds, "Legal Settlement") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377554_230743_343466_345045	377554_230743_343466_345045	IF(Contains(FR_BAO_SourceOfFunds, "Pension/IRA Retirement Savings") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377555_230757_343467_345064	377555_230757_343467_345064	IF(Contains(FR_BAO_SourceOfFunds, "Spouse/Parent") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377556_230741_343468_345043	377556_230741_343468_345043	IF(Contains(FR_BAO_SourceOfFunds, "Inheritance") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377557_230742_343469_345044	377557_230742_343469_345044	IF(Contains(FR_BAO_SourceOfFunds, "Insurance Proceeds") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377558_230744_343470_345046	377558_230744_343470_345046	IF(Contains(FR_BAO_SourceOfFunds, "Other") >=1)THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_SourceOfFunds', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SourceOfFunds'}}]	{'FR_BAO_SourceOfFunds': 'SourceOfFunds'}
377426_377458_377465	377426_377458_377465	FR_BAO_Liquid = 'Y'	[{'name': 'FR_BAO_Liquid', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'Liquid'}}]	{'FR_BAO_Liquid': 'Liquid'}
377431_377459_377466_377467_377242	377431_377459_377466_377467_377242	FR_BAO_DreyfusGovCashManageServiceShares = 'Y'	[{'name': 'FR_BAO_DreyfusGovCashManageServiceShares', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DreyfusGovCashManageServiceShares'}}]	{'FR_BAO_DreyfusGovCashManageServiceShares': 'DreyfusGovCashManageServiceShares'}
377432_377460_377468_377243	377432_377460_377468_377243	FR_BAO_DreyfusGovSecCashManageInvestor = 'Y'	[{'name': 'FR_BAO_DreyfusGovSecCashManageInvestor', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DreyfusGovSecCashManageInvestor'}}]	{'FR_BAO_DreyfusGovSecCashManageInvestor': 'DreyfusGovSecCashManageInvestor'}
377433_377461_377469_377240	377433_377461_377469_377240	FR_BAO_DreyfusGovCashManageInvester = 'Y'	[{'name': 'FR_BAO_DreyfusGovCashManageInvester', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DreyfusGovCashManageInvester'}}]	{'FR_BAO_DreyfusGovCashManageInvester': 'DreyfusGovCashManageInvester'}
377434_377462_377470_377244	377434_377462_377470_377244	FR_BAO_FederatedHermesGovObligationsCash = 'Y'	[{'name': 'FR_BAO_FederatedHermesGovObligationsCash', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FederatedHermesGovObligationsCash'}}]	{'FR_BAO_FederatedHermesGovObligationsCash': 'FederatedHermesGovObligationsCash'}
377435_377463_377471_377245	377435_377463_377471_377245	FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash = 'Y'	[{'name': 'FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FederatedHermesTrustUSTreasuryOblicationsCash'}}]	{'FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash': 'FederatedHermesTrustUSTreasuryOblicationsCash'}
377438_366684	377438_366684	IF(count(FR_AccountOwnerName) >= 1 AND FR_BAO_AccountType <> "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
377439	377439	IF(count(FR_AccountOwnerName) >= 1 AND FR_BAO_AccountType = "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
377440_366686	377440_366686	IF(count(FR_AccountOwnerName) >= 2 AND FR_BAO_AccountType <> "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
377441_366688	377441_366688	IF(count(FR_AccountOwnerName) >= 3 AND FR_BAO_AccountType <> "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
342094	342094	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to *NON** BAO MANAGED V2 PACKAGE\\nEXCLUDE\\nELSE\\nIF(FR_BAO_FormNumber = "F11015") THEN\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11015" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"\\n   FR_02_FormId = "F11015 (08/23)"\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
341859_377722	341859_377722	IF Count(FR_PrimaryBeneficiaryName) >= 1 AND Count(FR_PrimaryBeneficiaryRelationship) >= 1 THEN\\n  IF FR_PrimaryBeneficiaryName(1) > ""  AND \\n      FR_PrimaryBeneficiaryRelationship(1) <> "ESTATE" AND \\n      FR_PrimaryBeneficiaryRelationship(1) <> "TRUST"  AND \\n      FR_PrimaryBeneficiaryRelationship(1) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_BeneficiaryFlag1 = "true"\\n  ELSE\\n    FR_BAO_02_BeneficiaryFlag1 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_BeneficiaryFlag1 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_BeneficiaryFlag1', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
341860_377723	341860_377723	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(1) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
341863_341103	341863_341103	IF(Count(FR_AccountOwnerName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
341866	341866	IF(Count(FR_PrimaryBeneficiaryName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341867_341680_340096_377725_339317	341867_341680_340096_377725_339317	FR_BAO_02_BeneficiaryFlag1 = 'true'	[{'name': 'FR_BAO_02_BeneficiaryFlag1', 'type': 'String', 'is-array': False}]	{}
341868_341869_341870_341871_341872_341873_341874_341875_341876_341681_341682_341683_341684_341685_341686_341687_341688_341689_341108_341109_341110_341111_341112_341113_341114_341115_341116_340097_340098_340099_340100_340101_340102_340103_340104_340105_377726_377727_377728_377729_377730_377731_377732_377733_377734_339320_339321_339322_339323_339324_339325_339326_339327_339328	341868_341869_341870_341871_341872_341873_341874_341875_341876_341681_341682_341683_341684_341685_341686_341687_341688_341689_341108_341109_341110_341111_341112_341113_341114_341115_341116_340097_340098_340099_340100_340101_340102_340103_340104_340105_377726_377727_377728_377729_377730_377731_377732_377733_377734_339320_339321_339322_339323_339324_339325_339326_339327_339328	IF(Count(FR_PrimaryBeneficiarySSN) >= 1) THEN\\n     IF(FR_PrimaryBeneficiarySSN(1) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN'}
341877_341117	341877_341117	IF(Count(FR_PrimaryBeneficiaryRelationship) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341878_341707_341118_340137_377738_339330	341878_341707_341118_340137_377738_339330	IF Count(FR_PrimaryBeneficiaryGender) >= 1 THEN\\n    IF(FR_PrimaryBeneficiaryGender(1) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341879_341708_341119_340138_377739_339331	341879_341708_341119_340138_377739_339331	IF Count(FR_PrimaryBeneficiaryGender) >= 1 THEN\\n    IF(FR_PrimaryBeneficiaryGender(1) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341897_341691_341138_340107_377736_339318	341897_341691_341138_340107_377736_339318	IF Count(FR_PrimaryBeneficiaryLDPS) >= 1 THEN\\n    IF(FR_PrimaryBeneficiaryLDPS(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
341898	341898	IF Count(FR_PrimaryBeneficiaryName) >= 3 AND Count(FR_PrimaryBeneficiaryRelationship) >= 3 THEN\\n  IF FR_PrimaryBeneficiaryName(3) > ""  AND \\n      FR_PrimaryBeneficiaryRelationship(3) <> "ESTATE" AND \\n      FR_PrimaryBeneficiaryRelationship(3) <> "TRUST"  AND \\n      FR_PrimaryBeneficiaryRelationship(3) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_BeneficiaryFlag3 = "true"\\n  ELSE\\n    FR_BAO_02_BeneficiaryFlag3 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_BeneficiaryFlag3 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_BeneficiaryFlag3', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
341899_377759	341899_377759	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 3) THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(3) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
341900_377760	341900_377760	IF Count(FR_PrimaryBeneficiaryName) >= 4 AND Count(FR_PrimaryBeneficiaryRelationship) >= 4 THEN\\n  IF FR_PrimaryBeneficiaryName(4) > ""  AND \\n      FR_PrimaryBeneficiaryRelationship(4) <> "ESTATE" AND \\n      FR_PrimaryBeneficiaryRelationship(4) <> "TRUST"  AND \\n      FR_PrimaryBeneficiaryRelationship(4) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_BeneficiaryFlag4 = "true"\\n  ELSE\\n    FR_BAO_02_BeneficiaryFlag4 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_BeneficiaryFlag4 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_BeneficiaryFlag4', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
341901_377761	341901_377761	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 4) THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(4), "mm/dd/yyyy"),10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(4) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
341904	341904	IF(Count(FR_PrimaryBeneficiaryName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341905_377778	341905_377778	FR_BAO_02_BeneficiaryFlag3 = 'true'	[{'name': 'FR_BAO_02_BeneficiaryFlag3', 'type': 'String', 'is-array': False}]	{}
341906_341907_341908_341909_341910_341911_341912_341913_341914_341752_341753_341754_341755_341756_341757_341758_341759_341760_341147_341148_341149_341150_341151_341152_341153_341154_341155_340951_340952_340953_340954_340955_340956_340957_340958_340959_377779_377780_377781_377782_377783_377784_377785_377786_377787_339472_339473_339474_339475_339476_339477_339478_339479_339480	341906_341907_341908_341909_341910_341911_341912_341913_341914_341752_341753_341754_341755_341756_341757_341758_341759_341760_341147_341148_341149_341150_341151_341152_341153_341154_341155_340951_340952_340953_340954_340955_340956_340957_340958_340959_377779_377780_377781_377782_377783_377784_377785_377786_377787_339472_339473_339474_339475_339476_339477_339478_339479_339480	IF(Count(FR_PrimaryBeneficiarySSN) >= 3) THEN\\n     IF(FR_PrimaryBeneficiarySSN(3) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN'}
341915	341915	IF(Count(FR_PrimaryBeneficiaryRelationship) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341916_341763_341157_340962_377792_339484	341916_341763_341157_340962_377792_339484	IF Count(FR_PrimaryBeneficiaryGender) >= 3 THEN\\n    IF(FR_PrimaryBeneficiaryGender(3) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341917_341764_341158_340963_339485	341917_341764_341158_340963_339485	IF Count(FR_PrimaryBeneficiaryGender) >= 3 THEN\\n    IF(FR_PrimaryBeneficiaryGender(3) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341934	341934	IF(Count(FR_PrimaryBeneficiaryName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341935_377764	341935_377764	FR_BAO_02_BeneficiaryFlag4 = 'true'	[{'name': 'FR_BAO_02_BeneficiaryFlag4', 'type': 'String', 'is-array': False}]	{}
341936_341937_341938_341939_341940_341941_341942_341943_341944_341717_341718_341719_341720_341721_341722_341723_341724_341725_341177_341178_341179_341180_341181_341182_341183_341184_341185_340720_340721_340722_340723_340724_340725_340726_340727_340728_377765_377766_377767_377768_377769_377770_377771_377772_377773_339489_339490_339491_339492_339493_339494_339495_339496_339497	341936_341937_341938_341939_341940_341941_341942_341943_341944_341717_341718_341719_341720_341721_341722_341723_341724_341725_341177_341178_341179_341180_341181_341182_341183_341184_341185_340720_340721_340722_340723_340724_340725_340726_340727_340728_377765_377766_377767_377768_377769_377770_377771_377772_377773_339489_339490_339491_339492_339493_339494_339495_339496_339497	IF(Count(FR_PrimaryBeneficiarySSN) >= 4) THEN\\n     IF(FR_PrimaryBeneficiarySSN(4) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN'}
341945	341945	IF(Count(FR_PrimaryBeneficiaryRelationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341946_341729_341187_340746_377790_339499	341946_341729_341187_340746_377790_339499	IF Count(FR_PrimaryBeneficiaryGender) >= 4 THEN\\n    IF(FR_PrimaryBeneficiaryGender(4) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341947_341730_341188_340747_339500	341947_341730_341188_340747_339500	IF Count(FR_PrimaryBeneficiaryGender) >= 4 THEN\\n    IF(FR_PrimaryBeneficiaryGender(4) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341968_341762_341208_340961_377789_339482	341968_341762_341208_340961_377789_339482	IF Count(FR_PrimaryBeneficiaryLDPS) >= 3 THEN\\n    IF(FR_PrimaryBeneficiaryLDPS(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
341970_341746_341209_340763_377775_339501	341970_341746_341209_340763_377775_339501	IF Count(FR_PrimaryBeneficiaryLDPS) >= 4 THEN\\n    IF(FR_PrimaryBeneficiaryLDPS(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
341950_377740	341950_377740	IF Count(FR_PrimaryBeneficiaryName) >= 2 AND Count(FR_PrimaryBeneficiaryRelationship) >= 2 THEN\\n  IF FR_PrimaryBeneficiaryName(2) > ""  AND \\n      FR_PrimaryBeneficiaryRelationship(2) <> "ESTATE" AND \\n      FR_PrimaryBeneficiaryRelationship(2) <> "TRUST"  AND \\n      FR_PrimaryBeneficiaryRelationship(2) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_BeneficiaryFlag2 = "true"\\n  ELSE\\n    FR_BAO_02_BeneficiaryFlag2 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_BeneficiaryFlag2 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_BeneficiaryFlag2', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
341951_377741	341951_377741	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 2) THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(2) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
341952_341193	341952_341193	IF(Count(FR_PrimaryBeneficiaryName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341953_377743	341953_377743	FR_BAO_02_BeneficiaryFlag2 = 'true'	[{'name': 'FR_BAO_02_BeneficiaryFlag2', 'type': 'String', 'is-array': False}]	{}
341954_341955_341956_341957_341958_341959_341960_341961_341962_341696_341697_341698_341699_341700_341701_341702_341703_341704_341195_341196_341197_341198_341199_341200_341201_341202_341203_340126_340127_340128_340129_340130_340131_340132_340133_340134_377744_377745_377746_377747_377748_377749_377750_377751_377752_339623_339624_339625_339626_339627_339628_339629_339630_339631	341954_341955_341956_341957_341958_341959_341960_341961_341962_341696_341697_341698_341699_341700_341701_341702_341703_341704_341195_341196_341197_341198_341199_341200_341201_341202_341203_340126_340127_340128_340129_340130_340131_340132_340133_340134_377744_377745_377746_377747_377748_377749_377750_377751_377752_339623_339624_339625_339626_339627_339628_339629_339630_339631	IF(Count(FR_PrimaryBeneficiarySSN) >= 2) THEN\\n     IF(FR_PrimaryBeneficiarySSN(2) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN'}
341963_341204	341963_341204	IF(Count(FR_PrimaryBeneficiaryRelationship) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341964_341709_341205_340139_377756_339633	341964_341709_341205_340139_377756_339633	IF Count(FR_PrimaryBeneficiaryGender) >= 2 THEN\\n    IF(FR_PrimaryBeneficiaryGender(2) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341965_341710_341206_340140_339634	341965_341710_341206_340140_339634	IF Count(FR_PrimaryBeneficiaryGender) >= 2 THEN\\n    IF(FR_PrimaryBeneficiaryGender(2) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
341966_341706_341207_340136_377755_339622	341966_341706_341207_340136_377755_339622	IF Count(FR_PrimaryBeneficiaryLDPS) >= 2 THEN\\n    IF(FR_PrimaryBeneficiaryLDPS(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
341989_377812	341989_377812	IF Count(FR_ContingentBeneficiaryName) >= 2 AND Count(FR_ContingentBeneficiaryRelationship) >= 2 THEN\\n  IF FR_ContingentBeneficiaryName(2) > ""  AND \\n      FR_ContingentBeneficiaryRelationship(2) <> "ESTATE" AND \\n      FR_ContingentBeneficiaryRelationship(2) <> "TRUST"  AND \\n      FR_ContingentBeneficiaryRelationship(2) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_ContingencyFlag2 = "true"\\n  ELSE\\n    FR_BAO_02_ContingencyFlag2 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_ContingencyFlag2 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_ContingencyFlag2', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
341990	341990	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 2) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      IF(FR_ContingentBeneficiaryRelationship(2) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
341995	341995	IF(Count(FR_ContingentBeneficiaryName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
341996_377833	341996_377833	FR_BAO_02_ContingencyFlag2 = 'true'	[{'name': 'FR_BAO_02_ContingencyFlag2', 'type': 'String', 'is-array': False}]	{}
341997_341998_341999_342000_342001_342002_342003_342004_342005_341807_341808_341809_341810_341811_341812_341813_341814_341815_341218_341219_341220_341221_341222_341223_341224_341225_341226_341006_341007_341008_341009_341010_341011_341012_341013_341014_377834_377835_377836_377837_377838_377839_377840_377841_377842_339731_339732_339733_339734_339735_339736_339737_339738_339739	341997_341998_341999_342000_342001_342002_342003_342004_342005_341807_341808_341809_341810_341811_341812_341813_341814_341815_341218_341219_341220_341221_341222_341223_341224_341225_341226_341006_341007_341008_341009_341010_341011_341012_341013_341014_377834_377835_377836_377837_377838_377839_377840_377841_377842_339731_339732_339733_339734_339735_339736_339737_339738_339739	IF(Count(FR_ContingentBeneficiarySSN) >= 2) THEN\\n     IF(FR_ContingentBeneficiarySSN(2) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN'}
342006	342006	IF(Count(FR_ContingentBeneficiaryRelationship) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
342007_341818_341228_341017_339743	342007_341818_341228_341017_339743	IF Count(FR_ContingentBeneficiaryGender) >= 2 THEN\\n    IF(FR_ContingentBeneficiaryGender(2) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342008_341819_341229_341018_339744	342008_341819_341229_341018_339744	IF Count(FR_ContingentBeneficiaryGender) >= 2 THEN\\n    IF(FR_ContingentBeneficiaryGender(2) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342041_341817_341280_341016_377816	342041_341817_341280_341016_377816	IF Count(FR_ContingentBeneficiaryLDPS) >= 2 THEN\\n    IF(FR_ContingentBeneficiaryLDPS(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_ContingentBeneficiaryLDPS': 'BeneficiaryLDPS'}
342042	342042	IF Count(FR_ContingentBeneficiaryName) >= 1 AND Count(FR_ContingentBeneficiaryRelationship) >= 1 THEN\\n  IF FR_ContingentBeneficiaryName(1) > ""  AND \\n      FR_ContingentBeneficiaryRelationship(1) <> "ESTATE" AND \\n      FR_ContingentBeneficiaryRelationship(1) <> "TRUST"  AND \\n      FR_ContingentBeneficiaryRelationship(1) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_ContingencyFlag1 = "true"\\n  ELSE\\n    FR_BAO_02_ContingencyFlag1 = "false"\\n  ENDIF\\nELSE\\n   FR_BAO_02_ContingencyFlag1 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_ContingencyFlag1', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
342043_377795	342043_377795	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_ContingentBeneficiaryRelationship(1) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
342044	342044	IF(Count(FR_ContingentBeneficiaryName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
342045_377797	342045_377797	FR_BAO_02_ContingencyFlag1 = 'true'	[{'name': 'FR_BAO_02_ContingencyFlag1', 'type': 'String', 'is-array': False}]	{}
342046_342047_342048_342049_342050_342051_342052_342053_342054_341734_341735_341736_341737_341738_341739_341740_341741_341742_341266_341267_341268_341269_341270_341271_341272_341273_341274_340751_340752_340753_340754_340755_340756_340757_340758_340759_377798_377799_377800_377801_377802_377803_377804_377805_377806_339787_339788_339789_339790_339791_339792_339793_339794_339795	342046_342047_342048_342049_342050_342051_342052_342053_342054_341734_341735_341736_341737_341738_341739_341740_341741_341742_341266_341267_341268_341269_341270_341271_341272_341273_341274_340751_340752_340753_340754_340755_340756_340757_340758_340759_377798_377799_377800_377801_377802_377803_377804_377805_377806_339787_339788_339789_339790_339791_339792_339793_339794_339795	IF(Count(FR_ContingentBeneficiarySSN) >= 1) THEN\\n     IF(FR_ContingentBeneficiarySSN(1) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN'}
342055	342055	IF(Count(FR_ContingentBeneficiaryRelationship) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
342056_341744_341276_340761_339797	342056_341744_341276_340761_339797	IF Count(FR_ContingentBeneficiaryGender) >= 1 THEN\\n    IF(FR_ContingentBeneficiaryGender(1) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342057_341745_341277_340762_339798	342057_341745_341277_340762_339798	IF Count(FR_ContingentBeneficiaryGender) >= 1 THEN\\n    IF(FR_ContingentBeneficiaryGender(1) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342058_341727_341278_340730_377808_339799	342058_341727_341278_340730_377808_339799	IF Count(FR_ContingentBeneficiaryLDPS) >= 1 THEN\\n    IF(FR_ContingentBeneficiaryLDPS(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_ContingentBeneficiaryLDPS': 'BeneficiaryLDPS'}
342131_377814	342131_377814	IF Count(FR_ContingentBeneficiaryName) >= 3 AND Count(FR_ContingentBeneficiaryRelationship) >= 3 THEN\\n  IF FR_ContingentBeneficiaryName(3) > ""  AND \\n      FR_ContingentBeneficiaryRelationship(3) <> "ESTATE" AND \\n      FR_ContingentBeneficiaryRelationship(3) <> "TRUST"  AND \\n      FR_ContingentBeneficiaryRelationship(3) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_ContingencyFlag3 = "true"\\n  ELSE\\n    FR_BAO_02_ContingencyFlag3 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_ContingencyFlag3 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_ContingencyFlag3', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
342132	342132	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 3) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      IF(FR_ContingentBeneficiaryRelationship(3) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
342133_377848	342133_377848	IF Count(FR_ContingentBeneficiaryName) >= 4 AND Count(FR_ContingentBeneficiaryRelationship) >= 4 THEN\\n  IF FR_ContingentBeneficiaryName(4) > ""  AND \\n      FR_ContingentBeneficiaryRelationship(4) <> "ESTATE" AND \\n      FR_ContingentBeneficiaryRelationship(4) <> "TRUST"  AND \\n      FR_ContingentBeneficiaryRelationship(4) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_ContingencyFlag4 = "true"\\n  ELSE\\n    FR_BAO_02_ContingencyFlag4 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_ContingencyFlag4 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_ContingencyFlag4', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
342134	342134	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 4) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(4), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      IF(FR_ContingentBeneficiaryRelationship(4) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
342135	342135	IF(Count(FR_ContingentBeneficiaryName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
342136_377851	342136_377851	FR_BAO_02_ContingencyFlag4 = 'true'	[{'name': 'FR_BAO_02_ContingencyFlag4', 'type': 'String', 'is-array': False}]	{}
342137_342138_342139_342140_342141_342142_342143_342144_342145_341787_341788_341789_341790_341791_341792_341793_341794_341795_341328_341329_341330_341331_341332_341333_341334_341335_341336_340985_340986_340987_340988_340989_340990_340991_340992_340993_377852_377853_377854_377855_377856_377857_377858_377859_377860_339952_339953_339954_339955_339956_339957_339958_339959_339960	342137_342138_342139_342140_342141_342142_342143_342144_342145_341787_341788_341789_341790_341791_341792_341793_341794_341795_341328_341329_341330_341331_341332_341333_341334_341335_341336_340985_340986_340987_340988_340989_340990_340991_340992_340993_377852_377853_377854_377855_377856_377857_377858_377859_377860_339952_339953_339954_339955_339956_339957_339958_339959_339960	IF(Count(FR_ContingentBeneficiarySSN) >= 4) THEN\\n     IF(FR_ContingentBeneficiarySSN(4) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN'}
342146	342146	IF(Count(FR_ContingentBeneficiaryRelationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
342147_341800_341338_340998_339963	342147_341800_341338_340998_339963	IF Count(FR_ContingentBeneficiaryGender) >= 4 THEN\\n    IF(FR_ContingentBeneficiaryGender(4) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342148_341801_341339_340999_339964	342148_341801_341339_340999_339964	IF Count(FR_ContingentBeneficiaryGender) >= 4 THEN\\n    IF(FR_ContingentBeneficiaryGender(4) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342150	342150	IF(Count(FR_ContingentBeneficiaryName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
342151_377819	342151_377819	FR_BAO_02_ContingencyFlag3 = 'true'	[{'name': 'FR_BAO_02_ContingencyFlag3', 'type': 'String', 'is-array': False}]	{}
342152_342153_342154_342155_342156_342157_342158_342159_342160_341772_341773_341774_341775_341776_341777_341778_341779_341780_341342_341343_341344_341345_341346_341347_341348_341349_341350_340970_340971_340972_340973_340974_340975_340976_340977_340978_377820_377821_377822_377823_377824_377825_377826_377827_377828_339748_339749_339750_339751_339752_339753_339754_339755_339756	342152_342153_342154_342155_342156_342157_342158_342159_342160_341772_341773_341774_341775_341776_341777_341778_341779_341780_341342_341343_341344_341345_341346_341347_341348_341349_341350_340970_340971_340972_340973_340974_340975_340976_340977_340978_377820_377821_377822_377823_377824_377825_377826_377827_377828_339748_339749_339750_339751_339752_339753_339754_339755_339756	IF(Count(FR_ContingentBeneficiarySSN) >= 3) THEN\\n     IF(FR_ContingentBeneficiarySSN(3) <> "") THEN\\n          INCLUDE\\n     ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN'}
342161	342161	IF(Count(FR_ContingentBeneficiaryRelationship) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
342162_341798_341352_340996_339758	342162_341798_341352_340996_339758	IF Count(FR_ContingentBeneficiaryGender) >= 3 THEN\\n    IF(FR_ContingentBeneficiaryGender(3) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342163_341799_341353_340997_339759	342163_341799_341353_340997_339759	IF Count(FR_ContingentBeneficiaryGender) >= 3 THEN\\n    IF(FR_ContingentBeneficiaryGender(3) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
342164_341782_341354_340980_377830_339760	342164_341782_341354_340980_377830_339760	IF Count(FR_ContingentBeneficiaryLDPS) >= 3 THEN\\n    IF(FR_ContingentBeneficiaryLDPS(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_ContingentBeneficiaryLDPS': 'BeneficiaryLDPS'}
342166_341797_341355_340995_377862_339962	342166_341797_341355_340995_377862_339962	IF Count(FR_ContingentBeneficiaryLDPS) >= 4 THEN\\n    IF(FR_ContingentBeneficiaryLDPS(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_ContingentBeneficiaryLDPS': 'BeneficiaryLDPS'}
342167_342169_342171_341283_341285	342167_342169_342171_341283_341285	IF  COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  AND FR_BAO_02_SpousalWaiver = "Y" THEN\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF	[{'name': 'FR_BAO_02_SpousalWaiver', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'SpousalWaiver'}}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_BAO_02_SpousalWaiver': 'SpousalWaiver', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
342168_342170_342172_341282_341284_341286	342168_342170_342172_341282_341284_341286	FR_BAO_02_SpousalWaiver = 'Y'	[{'name': 'FR_BAO_02_SpousalWaiver', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'SpousalWaiver'}}]	{'FR_BAO_02_SpousalWaiver': 'SpousalWaiver'}
342527	342527	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to **NON** BAO MANAGED V2 PACKAGE\\nEXCLUDE\\nELSE\\nIF(FR_BAO_FormNumber = "F11208") THEN\\n   FR_02_FormId = "F11208 (08/23)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11208" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"\\n  INCLUDE\\nELSE\\nEXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
343968_340010	343968_340010	DIM iA AS Integer\\nIF(FR_BAO_IraOption = "SEP IRA") THEN\\n\	IF (trim(FR_BAO_EmployerTaxID) <> "") THEN\\n\	\	IRA_fx_LoadArray(FR_BAO_EmployerTaxID, 9, "L")\\n\	\	FOR iA = 1 TO 9\\n\	\	\	FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED(iA) = FR_02_TempArray(iA)\\n\	\	NEXT iA\\n\	ELSEIF (Count(FR_AccountOwnerSSN) >= 1) THEN\\n\	\	IF(FR_AccountOwnerSSN(1) <> "") AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n\	\	\	IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n\	\	\	FOR iA = 1 TO 9\\n\	\	\	\	FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED(iA) = FR_02_TempArray(iA)\\n\	\	\	NEXT iA\\n\	\	ELSE\\n\	\	\	FOR iA = 1 TO 9\\n\	\	\	\	FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED(iA) = ""\\n\	\	\	NEXT iA\\n\	\	ENDIF\\n\	ELSE\\n\	\	FOR iA = 1 TO 9\\n\	\	\	FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED(iA) = ""\\n\	\	NEXT iA\\n\	ENDIF\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_SEPIRA_TIN_SSN', 'type': 'String', 'is-array': False}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}, {'name': 'FR_BAO_EmployerTaxID', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'EmployerTaxID'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_IraOption': 'IraOption', 'FR_BAO_EmployerTaxID': 'EmployerTaxID', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
343971_342188_343101_340013_343519_339132	343971_342188_343101_340013_343519_339132	IF(count(FR_AccountOwnerSSN) >= 1 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
343972_340014	343972_340014	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 1)   AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
343977_341679_341695_341716_341733_341751_341771_341786_341806_252664_252667_252678_252681_252692_252695_252706_252709_252720_252723_252736_252739_252750_252753_252764_252767_341107_341194_341146_341176_341217_341265_341327_341341_342195_342207_342208_342266_342322_342370_342415_363590_343108_343225_343273_343302_343369_343417_343435_343445_340019_340095_340125_340719_340750_340950_340969_340984_341005_343525_344922_344947_344972_344997_345022_339139_339140_339316_339621_339471_339488_339786_339730_339747	343977_341679_341695_341716_341733_341751_341771_341786_341806_252664_252667_252678_252681_252692_252695_252706_252709_252720_252723_252736_252739_252750_252753_252764_252767_341107_341194_341146_341176_341217_341265_341327_341341_342195_342207_342208_342266_342322_342370_342415_363590_343108_343225_343273_343302_343369_343417_343435_343445_340019_340095_340125_340719_340750_340950_340969_340984_341005_343525_344922_344947_344972_344997_345022_339139_339140_339316_339621_339471_339488_339786_339730_339747_339951	FR_BAO_HighlightSSN_Field = 'YES'	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}]	{}
343980_376553_340022_377284	343980_376553_340022_377284	FR_BAO_IraOption = 'Traditional/Rollover IRA'	[{'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}]	{'FR_BAO_IraOption': 'IraOption'}
343981_376554_340023_377285	343981_376554_340023_377285	FR_BAO_IraOption = 'Roth IRA'	[{'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}]	{'FR_BAO_IraOption': 'IraOption'}
343991_376555_340033_377286	343991_376555_340033_377286	FR_BAO_IraOption = 'SEP IRA'	[{'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}]	{'FR_BAO_IraOption': 'IraOption'}
343993_342224_343126_340035_343544_339154	343993_342224_343126_340035_343544_339154	IF Count(FR_AccountOwner_PhoneType) >= 1 THEN\\nIF(FR_AccountOwner_PhoneType(1) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343994_340036_343545_339155	343994_340036_343545_339155	IF Count(FR_AccountOwner_PhoneType) >= 1 THEN\\nIF(FR_AccountOwner_PhoneType(1) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343995_342226_343128_340037_343546_339156	343995_342226_343128_340037_343546_339156	IF Count(FR_AccountOwner_PhoneType) >= 1 THEN\\nIF(FR_AccountOwner_PhoneType(1) = "Business") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
341634_340039	341634_340039	IF Count(FR_BAO_EmploymentStatus) >= 1 THEN\\n    IF((FR_BAO_EmploymentStatus(1) = "UEMP") OR (FR_BAO_EmploymentStatus(1) = "RETD"))THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
341645_376621_340050_377320_339263	341645_376621_340050_377320_339263	IF Count(FR_AccountOwnerTIAACREFCB) >= 1 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\n\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 1 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
341652_342514_343452_340057_345054_339270	341652_342514_343452_340057_345054_339270	FR_BAO_NetWorth = 'OVER2500000'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
341654_342512_343450_340059_345052_339272	341654_342512_343450_340059_345052_339272	FR_BAO_AnnualIncome = 'FROM75000TO99999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
341655_342515_343453_340060_345055_339273	341655_342515_343453_340060_345055_339273	FR_BAO_AnnualIncome = 'FROM200000TO249999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
341657_342517_343455_340062_345057_339275	341657_342517_343455_340062_345057_339275	FR_BAO_NetWorth = 'FROM200000TO499999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
341659_342511_343449_340064_345051_339277	341659_342511_343449_340064_345051_339277	FR_BAO_AnnualIncome = 'FROM50000TO74999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
341660_342513_343451_340065_345053_339278	341660_342513_343451_340065_345053_339278	FR_BAO_AnnualIncome = 'FROM150000TO199999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
341661_342519_343457_340066_345059_339279	341661_342519_343457_340066_345059_339279	FR_BAO_NetWorth = 'FROM1000000TO2499999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
341662_342518_343456_340067_345058_339280	341662_342518_343456_340067_345058_339280	FR_BAO_NetWorth = 'FROM500000TO999999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
341665_342522_343460_340070_345062_339283	341665_342522_343460_340070_345062_339283	FR_BAO_NetWorth = 'FROM100000TO199999'	[{'name': 'FR_BAO_NetWorth', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'NetWorth'}}]	{'FR_BAO_NetWorth': 'NetWorth'}
341666_342508_343446_340071_345040_339284	341666_342508_343446_340071_345040_339284	FR_BAO_AnnualIncome = 'FROM100000TO149999'	[{'name': 'FR_BAO_AnnualIncome', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AnnualIncome'}}]	{'FR_BAO_AnnualIncome': 'AnnualIncome'}
341668_180847_342503_343472_340072_343811_340079	341668_180847_342503_343472_340072_343811_340079	FR_BAO_VoteProxy = 'Y'	[{'name': 'FR_BAO_VoteProxy', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'VoteProxy'}}]	{'FR_BAO_VoteProxy': 'VoteProxy'}
341669_340073_343812	341669_340073_343812	IF(FR_BAO_ConfirmSuppressionStatus_Ind <> "Y") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_ConfirmSuppressionStatus_Ind', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ConfirmSuppressionStatus'}}]	{'FR_BAO_ConfirmSuppressionStatus_Ind': 'ConfirmSuppressionStatus'}
341670_340074_343813_340080	341670_340074_343813_340080	FR_BAO_ConfirmSuppressionStatus_Ind = 'Y'	[{'name': 'FR_BAO_ConfirmSuppressionStatus_Ind', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ConfirmSuppressionStatus'}}]	{'FR_BAO_ConfirmSuppressionStatus_Ind': 'ConfirmSuppressionStatus'}
341672_180849_340075_343814	341672_180849_340075_343814	FR_BAO_SweepSelectionSweepAccount = 'Y'	[{'name': 'FR_BAO_SweepSelectionSweepAccount', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'SweepAccount'}}]	{'FR_BAO_SweepSelectionSweepAccount': 'SweepAccount'}
341674_252655_252711_341099_340088_339313	341674_252655_252711_341099_340088_339313	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_PrimaryBeneficiarySSN) >= 1 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_PrimaryBeneficiary_BeneficiaryType)) >= 1 THEN\\nIF FR_PrimaryBeneficiary_BeneficiaryType(1) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341675_340089	341675_340089	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 1)  AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341676_340090	341676_340090	FR_BAO_HighlightSSN_Field = "NO"\\nIF(count(FR_PrimaryBeneficiarySSN) >= 2 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_PrimaryBeneficiary_BeneficiaryType)) >= 2 THEN\\nIF FR_PrimaryBeneficiary_BeneficiaryType(2) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341677_340091_339618	341677_340091_339618	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 2) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341678_340094_377724_377762	341678_340094_377724_377762	IF(count(FR_PrimaryBeneficiaryPercentage) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
341690_340106_377735_339329	341690_340106_377735_339329	IF(count(FR_PrimaryBeneficiaryRelationship) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341692_341106_340108_377737_339319	341692_341106_340108_377737_339319	IF(count(FR_PrimaryBeneficiaryName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341693_340123_377742	341693_340123_377742	IF(count(FR_PrimaryBeneficiaryPercentage) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
341694_340124_377754_339620	341694_340124_377754_339620	IF(count(FR_PrimaryBeneficiaryName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341705_340135_377753_339632	341705_340135_377753_339632	IF(count(FR_PrimaryBeneficiaryRelationship) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341711_252697_252755_341141_340712_339468	341711_252697_252755_341141_340712_339468	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_PrimaryBeneficiarySSN) >= 4 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_PrimaryBeneficiary_BeneficiaryType)) >= 4 THEN\\nIF FR_PrimaryBeneficiary_BeneficiaryType(4) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341712_340713_339469	341712_340713_339469	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 4)  AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(4), "mm/dd/yyyy"),10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341713_341262_340714_339782	341713_341262_340714_339782	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= 1 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= 1 THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(1) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341714_340715_339783	341714_340715_339783	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341715_340718	341715_340718	IF(count(FR_PrimaryBeneficiaryPercentage) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
341726_341186_340729_377774_339481_339498	341726_341186_340729_377774_339481_339498	IF(count(FR_PrimaryBeneficiaryRelationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341728_341175_340731_377763_339487	341728_341175_340731_377763_339487	IF(count(FR_PrimaryBeneficiaryName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341731_340748_377796	341731_340748_377796	IF(count(FR_ContingentBeneficiaryPercentage) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage'}
341732_341783_341264_340749_340981_377809_339785	341732_341783_341264_340749_340981_377809_339785	IF(count(FR_ContingentBeneficiaryName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
341743_341781_341275_340760_340979_377807_339796	341743_341781_341275_340760_340979_377807_339796	IF(count(FR_ContingentBeneficiaryRelationship) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
341747_340946	341747_340946	FR_BAO_HighlightSSN_Field = "NO"\\nIF(count(FR_PrimaryBeneficiarySSN) >= 3 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_PrimaryBeneficiary_BeneficiaryType)) >= 3 THEN\\nIF FR_PrimaryBeneficiary_BeneficiaryType(3) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341748_340947	341748_340947	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341749_340948_377776	341749_340948_377776	IF(count(FR_PrimaryBeneficiaryPercentage) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
341750_341145_340949_377777_339483	341750_341145_340949_377777_339483	IF(count(FR_PrimaryBeneficiaryName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName'}
341761_341156_340960_377788	341761_341156_340960_377788	IF(count(FR_PrimaryBeneficiaryRelationship) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
341766_341322_340964_339727	341766_341322_340964_339727	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= 3 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= 3 THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(3) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341767_340965_339728	341767_340965_339728	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341768_340966	341768_340966	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= 4 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= 4 THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(4) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341769_340967_339948	341769_340967_339948	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 4) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(4), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341770_340968_377817_339745	341770_340968_377817_339745	IF(count(FR_ContingentBeneficiaryPercentage) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage'}
341784_340982_377850_339949	341784_340982_377850_339949	IF(count(FR_ContingentBeneficiaryPercentage) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage'}
341785_341805_341216_340983_341004_377832_339742	341785_341805_341216_340983_341004_377832_339742	IF(count(FR_ContingentBeneficiaryName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
341796_341816_341227_340994_341015_377843_339740	341796_341816_341227_340994_341015_377843_339740	IF(count(FR_ContingentBeneficiaryRelationship) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
341802_341210_341001_339725	341802_341210_341001_339725	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= 2 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= 2 THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(2) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341803_341002_339726	341803_341002_339726	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 2) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341804_341003_377831_339729	341804_341003_377831_339729	IF(count(FR_ContingentBeneficiaryPercentage) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage'}
232830	232830	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to **NON** BAO MANAGED V2 PACKAGE\\nEXCLUDE\\nELSE\\n\\nIF(FR_BAO_FormNumber = "F11207") THEN\\n   FR_02_FormId = "F11207 (3/17)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11207" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0317"\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
290283	290283	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustSSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustSSN, 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_TrustSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_TrustSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_TrustEffectiveDate) >= 1) THEN\\n    IRA_fx_LoadArray(Format(FR_BAO_TrustEffectiveDate,"mmddyyyy"), 8, "L")\\n    FOR iA = 1 TO 8\\n            FR_BAO_02_TrustEffectiveDate(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustEffectiveDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_TrustSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustEffectiveDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'TrustEffectiveDate'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_BAO_TrustEffectiveDate': 'TrustEffectiveDate', 'FR_BAO_TrustSSN': 'TrustSSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
290287_343520	290287_343520	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(1), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
245281_342229_343204	245281_342229_343204	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerMiddleName) >= 2) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(2), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerMiddleName': 'MiddleName'}
245282_342231_343206	245282_342231_343206	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 2) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(2), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
245305_342252_343211	245305_342252_343211	IF(count(FR_AccountOwnerFirstName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_AccountOwnerFirstName': 'FirstName'}
245306_342253_343212	245306_342253_343212	IF(count(FR_AccountOwnerLastName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_AccountOwnerLastName': 'LastName'}
245342_342285_343252	245342_342285_343252	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerMiddleName) >= 3) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(3), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerMiddleName': 'MiddleName'}
245343	245343	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(3), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n         FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
245346_342308_343259	245346_342308_343259	IF(count(FR_AccountOwnerFirstName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_AccountOwnerFirstName': 'FirstName'}
245347_342309_343260	245347_342309_343260	IF(count(FR_AccountOwnerLastName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_AccountOwnerLastName': 'LastName'}
180841	180841	FR_BAO_InitialTransactionsTransferFunds = 'Y'	[{'name': 'FR_BAO_InitialTransactionsTransferFunds', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TransferFunds'}}]	{'FR_BAO_InitialTransactionsTransferFunds': 'TransferFunds'}
180842	180842	FR_BAO_InitialTransactionsFullTransfer = 'Y'	[{'name': 'FR_BAO_InitialTransactionsFullTransfer', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FullTransfer'}}]	{'FR_BAO_InitialTransactionsFullTransfer': 'FullTransfer'}
180843	180843	IF Count(FR_BAO_InitialTransactionsSharesDescription) >= 1 THEN\\n    IF FR_BAO_InitialTransactionsSharesDescription(1) <> "" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_BAO_InitialTransactionsSharesDescription', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SharesDescription'}}]	{'FR_BAO_InitialTransactionsSharesDescription': 'SharesDescription'}
180844	180844	FR_BAO_SweepSelectionPrime = 'Y'	[{'name': 'FR_BAO_SweepSelectionPrime', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'Prime'}}]	{'FR_BAO_SweepSelectionPrime': 'Prime'}
180845	180845	FR_BAO_InitialTransactionsDeliveringAccountClosed = 'Y'	[{'name': 'FR_BAO_InitialTransactionsDeliveringAccountClosed', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveringAccountClosed'}}]	{'FR_BAO_InitialTransactionsDeliveringAccountClosed': 'DeliveringAccountClosed'}
180846	180846	FR_BAO_InitialTransactionsPartialTransfer = 'Y'	[{'name': 'FR_BAO_InitialTransactionsPartialTransfer', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'PartialTransfer'}}]	{'FR_BAO_InitialTransactionsPartialTransfer': 'PartialTransfer'}
180848	180848	FR_BAO_SweepSelectionCash = 'Y'	[{'name': 'FR_BAO_SweepSelectionCash', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'Cash'}}]	{'FR_BAO_SweepSelectionCash': 'Cash'}
180850	180850	IF Count(FR_BAO_InitialTransactionsSharesDescription) >= 3 THEN\\n    IF FR_BAO_InitialTransactionsSharesDescription(3) <> "" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_BAO_InitialTransactionsSharesDescription', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SharesDescription'}}]	{'FR_BAO_InitialTransactionsSharesDescription': 'SharesDescription'}
180851	180851	IF Count(FR_BAO_InitialTransactionsSharesDescription) >= 2 THEN\\n    IF FR_BAO_InitialTransactionsSharesDescription(2) <> "" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_BAO_InitialTransactionsSharesDescription', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SharesDescription'}}]	{'FR_BAO_InitialTransactionsSharesDescription': 'SharesDescription'}
180852	180852	IF Count(FR_BAO_InitialTransactionsSharesQuantity) >= 1 THEN\\n    IF (Len(FR_BAO_InitialTransactionsSharesQuantity(1)) > 0) THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_BAO_InitialTransactionsSharesQuantity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SharesQuantity'}}]	{'FR_BAO_InitialTransactionsSharesQuantity': 'SharesQuantity'}
180853	180853	IF Count(FR_BAO_InitialTransactionsSharesQuantity) >= 2 THEN\\n    IF (Len(FR_BAO_InitialTransactionsSharesQuantity(2)) > 0) THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_BAO_InitialTransactionsSharesQuantity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SharesQuantity'}}]	{'FR_BAO_InitialTransactionsSharesQuantity': 'SharesQuantity'}
180854	180854	IF Count(FR_BAO_InitialTransactionsSharesQuantity) >= 3 THEN\\n    IF (Len(FR_BAO_InitialTransactionsSharesQuantity(3)) > 0) THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_BAO_InitialTransactionsSharesQuantity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SharesQuantity'}}]	{'FR_BAO_InitialTransactionsSharesQuantity': 'SharesQuantity'}
230779_290325_363717	230779_290325_363717	IF(count(FR_AccountOwnerName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
230780_290324_363716	230780_290324_363716	IF(count(FR_AccountOwnerName) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
230781_290323_363715_377872	230781_290323_363715_377872	IF(count(FR_AccountOwnerName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
237086	237086	FR_BAO_FormNumber = 'CITIODD'	[{'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}]	{'FR_BAO_FormNumber': 'FormNumber'}
376656	376656	IF(FR_BAO_FormNumber = "F11143") THEN\\n   FR_02_WPID= "TBRISD"\\n   FR_02_FormId = "F11143 (10/24)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11143" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "1024"\\n  INCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_WPID', 'type': 'String', 'is-array': False}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}]	{'FR_BAO_FormNumber': 'FormNumber'}
376558_377275_331068	376558_377275_331068	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = ""\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate'}
376560_377279	376560_377279	IF Count(FR_AccountOwnerGender) >= 1 THEN\\n    IF(FR_AccountOwnerGender(1) <> "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
376563	376563	FR_BAO_PhoneType = 'Mobile'	[{'name': 'FR_BAO_PhoneType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_BAO_PhoneType': 'PhoneType'}
376564	376564	FR_BAO_PhoneType = 'Home'	[{'name': 'FR_BAO_PhoneType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_BAO_PhoneType': 'PhoneType'}
376565	376565	FR_BAO_PhoneType = 'Business'	[{'name': 'FR_BAO_PhoneType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_BAO_PhoneType': 'PhoneType'}
345067	345067	FR_02_CurrentForm(SYS_SubDocInDocument) = "Passthrough" \\nFR_02_CurrentVersionDate(SYS_SubDocInDocument) = "1111"\\n\\nIF Instr(1,"A10605,P0511225",FR_BAO_FormNumber)<> 0 THEN\\nINCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}]	{'FR_BAO_FormNumber': 'FormNumber'}
345785	345785	FR_LetterType = 'A13198'	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
345786	345786	IF(SYS_PagePrintedValue = SYS_PageTotalPrinted) THEN\\n     INCLUDE\\nENDIF	[{'name': 'SYS_PageTotalPrinted', 'type': 'Integer', 'is-array': False}, {'name': 'SYS_PagePrintedValue', 'type': 'Integer', 'is-array': False}]	{}
345787	345787	IF(SYS_PagePrintedValue = SYS_PageTotalPrinted AND SYS_PageInDocument <> SYS_PageTotalInDocument) THEN\\n     INCLUDE\\nENDIF	[{'name': 'SYS_PageTotalPrinted', 'type': 'Integer', 'is-array': False}, {'name': 'SYS_PagePrintedValue', 'type': 'Integer', 'is-array': False}, {'name': 'SYS_PageInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'SYS_PageTotalInDocument', 'type': 'Integer', 'is-array': False}]	{}
218432	218432	IF(FR_LetterType = "A13573NI" OR FR_LetterType = "A13573IR") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_LetterType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'LetterID'}}]	{'FR_LetterType': 'LetterID'}
248650	248650	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\nIF(FR_BAO_FormNumber = "F11004") THEN\\n   FR_02_FormId = "F11004 (06/19)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11004" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0619"\\n  INCLUDE\\nENDIF\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
252654_252710	252654_252710	IF(count(FR_PrimaryBeneficiaryName) >= 1) THEN\\n      FR_PrimaryBeneficiaryName_Display(1) = FR_PrimaryBeneficiaryName(1)\\nELSE\\n\	 FR_PrimaryBeneficiaryName_Display(1) = ""\	\\nENDIF\\n\\nDIM iA AS INTEGER\\n/*\\nIF(Count(FR_PrimaryBeneficiaryPercentage) >= 1) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiaryPercentage(1), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n*/\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_PrimaryBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252656_252712	252656_252712	DIM iA AS Integer\\nIF(count(FR_PrimaryBeneficiarySSN) >= 1) and FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_PrimaryBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_PrimaryBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(1), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\n\\nIF(count(FR_PrimaryBeneficiary_PhoneNumber) >= 1) THEN\\n      FR_PrimaryBeneficiary_PhoneNumber_Display(1) = FR_PrimaryBeneficiary_PhoneNumber(1)\\nELSE\\n\	 FR_PrimaryBeneficiary_PhoneNumber_Display(1) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_PhoneNumber': 'BeneficiaryContactPhoneNumber'}
252657_252713	252657_252713	DIM iA AS Integer\\n\\nIF(count(FR_PrimaryBeneficiary_StreetAddress) >= 1) THEN\\n     FR_PrimaryBeneficiary_StreetAddress_Display(1) = FR_PrimaryBeneficiary_StreetAddress(1)\\nELSE\\n\	 FR_PrimaryBeneficiary_StreetAddress_Display(1) = ""\	\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_City) >= 1) THEN\\n     FR_PrimaryBeneficiary_City_Display(1) = FR_PrimaryBeneficiary_City(1)\\nELSE\\n\	 FR_PrimaryBeneficiary_City_Display(1) = ""\	\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiary_State) >= 1) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiary_State(1), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_zipcode) >= 1) THEN\\n     FR_PrimaryBeneficiary_zipcode_Display(1) = FR_PrimaryBeneficiary_zipcode(1)\\nELSE\\n\	 FR_PrimaryBeneficiary_zipcode_Display(1) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_PrimaryBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_PrimaryBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_PrimaryBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_PrimaryBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_PrimaryBeneficiary_City': 'BeneficiaryCity', 'FR_PrimaryBeneficiary_State': 'BeneficiaryState', 'FR_PrimaryBeneficiary_zipcode': 'BeneficiaryZipcode'}
252659_252660_252715_252716	252659_252660_252715_252716	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"A")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryName_Display(1) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(1) = ""\	\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252661_252717	252661_252717	FR_BAO_HighlightSSN_Field = "NO"\\n\\nDIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"A")\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX AND INDEX <> 0 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= INDEX THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(INDEX) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType', 'FR_ContingentBeneficiary_Category': 'Category'}
252662_252718	252662_252718	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"A")\\n\\nDIM iA AS Integer\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX) and FR_BAO_DeliveryMethod = "E-Signature-Agent" AND (INDEX <> 0) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(INDEX), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ContingentBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_ContingentBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiaryGender) >= INDEX) AND ( INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryGender_Display(1) = FR_ContingentBeneficiaryGender(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryGender_Display(1) = ""\	\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= INDEX) AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(INDEX), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryPercentage) >= INDEX) AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(FR_ContingentBeneficiaryPercentage(INDEX), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_ContingentBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_ContingentBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiaryContactPhoneNumber) >= INDEX) AND ( INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryContactPhoneNumber_Display(1) = FR_ContingentBeneficiaryContactPhoneNumber(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryContactPhoneNumber_Display(1) = ""\	\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_ContingentBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiaryGender_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryPercentage_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_ContingentBeneficiaryGender': 'BeneficiaryGender', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiaryContactPhoneNumber': 'BeneficiaryContactPhoneNumber'}
252663_252719	252663_252719	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"A")\\n\\nDIM iA AS Integer\\n\\nIF(count(FR_ContingentBeneficiary_StreetAddress) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_StreetAddress_Display(1) = FR_ContingentBeneficiary_StreetAddress( INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_StreetAddress_Display(1) = ""\	\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_City) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_City_Display(1) = FR_ContingentBeneficiary_City( INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_City_Display(1) = ""\	\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiary_State) >= INDEX) AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(FR_ContingentBeneficiary_State( INDEX), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_zipcode) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_zipcode_Display(1) = FR_ContingentBeneficiary_zipcode( INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_zipcode_Display(1) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_ContingentBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_ContingentBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_ContingentBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_ContingentBeneficiary_City': 'BeneficiaryCity', 'FR_ContingentBeneficiary_State': 'BeneficiaryState', 'FR_ContingentBeneficiary_zipcode': 'BeneficiaryZipcode'}
252665_252721	252665_252721	IF COUNT(FR_PrimaryBeneficiaryLDPS) >= 1 THEN\\n\\nIF TRIM(FR_PrimaryBeneficiaryLDPS(1)) = "Y" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
252666	252666	IF TRIM(FR_ContingentBeneficiaryName_Display(1)) <> "" THEN\\n     INCLUDE\\nELSE\\nEXCLUDE\\nEND IF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254131	254131	IF COUNT(FR_PrimaryBeneficiaryPercentage) >=1 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252668	252668	IF(count(FR_PrimaryBeneficiaryName) >= 2) THEN\\n      FR_PrimaryBeneficiaryName_Display(2) = FR_PrimaryBeneficiaryName(2)\\nELSE\\n\	 FR_PrimaryBeneficiaryName_Display(2) = ""\	\\nENDIF\\n\\nDIM iA AS INTEGER\\n\\nIF(count(FR_PrimaryBeneficiaryRelationship) >= 2) THEN\\n      FR_PrimaryBeneficiaryRelationship_Display(2) = FR_PrimaryBeneficiaryRelationship(2)\\nELSE\\n\	  FR_PrimaryBeneficiaryRelationship_Display(2) = ""\	\\nENDIF\\n\\n/*\\nIF(Count(FR_PrimaryBeneficiaryPercentage) >= 2) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiaryPercentage(2), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n*/\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_PrimaryBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiaryRelationship_Display', 'type': 'String', 'is-array': True}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
252669_252727_341191_339617	252669_252727_341191_339617	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_PrimaryBeneficiarySSN) >= 2 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_PrimaryBeneficiary_BeneficiaryType)) >= 2 THEN\\nIF FR_PrimaryBeneficiary_BeneficiaryType(2) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_BeneficiaryType': 'BeneficiaryType'}
252670_252728	252670_252728	DIM iA AS Integer\\nIF(count(FR_PrimaryBeneficiarySSN) >=2) and FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_PrimaryBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_PrimaryBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(2), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\n\\nIF(count(FR_PrimaryBeneficiary_PhoneNumber) >= 2) THEN\\n      FR_PrimaryBeneficiary_PhoneNumber_Display(2) = FR_PrimaryBeneficiary_PhoneNumber(2)\\nELSE\\n\	 FR_PrimaryBeneficiary_PhoneNumber_Display(2) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_PhoneNumber': 'BeneficiaryContactPhoneNumber'}
252671_252729	252671_252729	DIM iA AS Integer\\n\\nIF(count(FR_PrimaryBeneficiary_StreetAddress) >= 2) THEN\\n     FR_PrimaryBeneficiary_StreetAddress_Display(2) = FR_PrimaryBeneficiary_StreetAddress(2)\\nELSE\\n\	 FR_PrimaryBeneficiary_StreetAddress_Display(2) = ""\	\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_City) >= 2) THEN\\n     FR_PrimaryBeneficiary_City_Display(2) = FR_PrimaryBeneficiary_City(2)\\nELSE\\n\	 FR_PrimaryBeneficiary_City_Display(2) = ""\	\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiary_State) >= 2) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiary_State(2), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_zipcode) >= 2) THEN\\n     FR_PrimaryBeneficiary_zipcode_Display(2) = FR_PrimaryBeneficiary_zipcode(2)\\nELSE\\n\	 FR_PrimaryBeneficiary_zipcode_Display(2) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_PrimaryBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_PrimaryBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_PrimaryBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_PrimaryBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_PrimaryBeneficiary_City': 'BeneficiaryCity', 'FR_PrimaryBeneficiary_State': 'BeneficiaryState', 'FR_PrimaryBeneficiary_zipcode': 'BeneficiaryZipcode'}
252673_252731	252673_252731	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"B")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryName_Display(2) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(2) = ""\	\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252674_252732	252674_252732	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"B")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryName_Display(2) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(2) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252675_252733	252675_252733	FR_BAO_HighlightSSN_Field = "NO"\\nDIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"B")\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX AND INDEX <> 0 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= INDEX THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(INDEX) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType', 'FR_ContingentBeneficiary_Category': 'Category'}
252676_252734	252676_252734	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"B")\\n\\n\\nDIM iA AS Integer\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX) and FR_BAO_DeliveryMethod = "E-Signature-Agent" AND (INDEX <> 0) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(INDEX), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ContingentBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_ContingentBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= INDEX) AND (INDEX <> 0) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(INDEX), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\n\\nIF(count(FR_ContingentBeneficiaryContactPhoneNumber) >= INDEX) AND (INDEX <> 0) THEN\\n      FR_ContingentBeneficiaryContactPhoneNumber_Display(2) = FR_ContingentBeneficiaryContactPhoneNumber(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryContactPhoneNumber_Display(2) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiaryContactPhoneNumber': 'BeneficiaryContactPhoneNumber'}
252677_252735	252677_252735	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"B")\\n\\nDIM iA AS Integer\\n\\nIF(count(FR_ContingentBeneficiary_StreetAddress) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_StreetAddress_Display(2) = FR_ContingentBeneficiary_StreetAddress(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_StreetAddress_Display(2) = ""\	\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_City) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_City_Display(2) = FR_ContingentBeneficiary_City(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_City_Display(2) = ""\	\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiary_State) >= INDEX) AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(FR_ContingentBeneficiary_State(INDEX), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_zipcode) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_zipcode_Display(2) = FR_ContingentBeneficiary_zipcode(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_zipcode_Display(2) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_ContingentBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_ContingentBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_ContingentBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_ContingentBeneficiary_City': 'BeneficiaryCity', 'FR_ContingentBeneficiary_State': 'BeneficiaryState', 'FR_ContingentBeneficiary_zipcode': 'BeneficiaryZipcode'}
252679_252737	252679_252737	IF COUNT(FR_PrimaryBeneficiaryLDPS) >= 2 THEN\\n\\nIF TRIM(FR_PrimaryBeneficiaryLDPS(2)) = "Y" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
252680	252680	IF TRIM(FR_ContingentBeneficiaryName_Display(2)) <> "" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nEND IF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254132	254132	IF COUNT(FR_PrimaryBeneficiaryPercentage) >=2 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252682	252682	IF(count(FR_PrimaryBeneficiaryName) >= 3) THEN\\n      FR_PrimaryBeneficiaryName_Display(3) = FR_PrimaryBeneficiaryName(3)\\nELSE\\n\	 FR_PrimaryBeneficiaryName_Display(3) = ""\	\\nENDIF\\n\\n/*\\nDIM iA AS integer\\nIF(Count(FR_PrimaryBeneficiaryPercentage) >= 3) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiaryPercentage(3), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n*/\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_PrimaryBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252683_252741_341139_339466	252683_252741_341139_339466	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_PrimaryBeneficiarySSN) >= 3 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_PrimaryBeneficiary_BeneficiaryType)) >= 3 THEN\\nIF FR_PrimaryBeneficiary_BeneficiaryType(3) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_BeneficiaryType': 'BeneficiaryType'}
252684_252742	252684_252742	DIM iA AS Integer\\nIF(count(FR_PrimaryBeneficiarySSN) >= 3) and FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_PrimaryBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_PrimaryBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(3), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\n\\nIF(count(FR_PrimaryBeneficiary_PhoneNumber) >= 3) THEN\\n      FR_PrimaryBeneficiary_PhoneNumber_Display(3) = FR_PrimaryBeneficiary_PhoneNumber(3)\\nELSE\\n\	 FR_PrimaryBeneficiary_PhoneNumber_Display(3) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_PhoneNumber': 'BeneficiaryContactPhoneNumber'}
252685_252743	252685_252743	DIM iA AS Integer\\n\\nIF(count(FR_PrimaryBeneficiary_StreetAddress) >= 3) THEN\\n     FR_PrimaryBeneficiary_StreetAddress_Display(3) = FR_PrimaryBeneficiary_StreetAddress(3)\\nELSE\\n\	 FR_PrimaryBeneficiary_StreetAddress_Display(3) = ""\	\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_City) >= 3) THEN\\n     FR_PrimaryBeneficiary_City_Display(3) = FR_PrimaryBeneficiary_City(3)\\nELSE\\n\	 FR_PrimaryBeneficiary_City_Display(3)= ""\	\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiary_State) >= 3) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiary_State(3), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_zipcode) >= 3) THEN\\n     FR_PrimaryBeneficiary_zipcode_Display(3) = FR_PrimaryBeneficiary_zipcode(3)\\nELSE\\n\	 FR_PrimaryBeneficiary_zipcode_Display(3) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_PrimaryBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_PrimaryBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_PrimaryBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_PrimaryBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_PrimaryBeneficiary_City': 'BeneficiaryCity', 'FR_PrimaryBeneficiary_State': 'BeneficiaryState', 'FR_PrimaryBeneficiary_zipcode': 'BeneficiaryZipcode'}
252687_252745	252687_252745	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"C")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryName_Display(3) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(3) = ""\	\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252688_252746	252688_252746	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"C")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0) THEN\\n      FR_ContingentBeneficiaryName_Display(3) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(3) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252689_252747	252689_252747	FR_BAO_HighlightSSN_Field = "NO"\\nDIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"C")\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX AND INDEX <> 0 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= INDEX THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(INDEX) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType', 'FR_ContingentBeneficiary_Category': 'Category'}
252690_252748	252690_252748	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"C")\\n\\n\\nDIM iA AS Integer\\nIF(count(FR_ContingentBeneficiarySSN) >=INDEX) and  FR_BAO_DeliveryMethod = "E-Signature-Agent"  AND (INDEX <> 0 )THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(INDEX), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ContingentBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_ContingentBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= INDEX)  AND (INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(INDEX), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\n\\nIF(count(FR_ContingentBeneficiaryContactPhoneNumber) >= INDEX)  AND (INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryContactPhoneNumber_Display(3) = FR_ContingentBeneficiaryContactPhoneNumber(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryContactPhoneNumber_Display(3) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiaryContactPhoneNumber': 'BeneficiaryContactPhoneNumber'}
252691_252749	252691_252749	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"C")\\n\\n\\nDIM iA AS Integer\\n\\nIF(count(FR_ContingentBeneficiary_StreetAddress) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_StreetAddress_Display(3) = FR_ContingentBeneficiary_StreetAddress(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_StreetAddress_Display(3) = ""\	\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_City) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_City_Display(3) = FR_ContingentBeneficiary_City(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_City_Display(3) = ""\	\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiary_State) >= INDEX) AND ( INDEX <> 0 )  THEN\\n   IRA_fx_LoadArray(FR_ContingentBeneficiary_State(INDEX), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_zipcode) >= INDEX) AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_zipcode_Display(3) = FR_ContingentBeneficiary_zipcode(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_zipcode_Display(3) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_ContingentBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_ContingentBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_ContingentBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_ContingentBeneficiary_City': 'BeneficiaryCity', 'FR_ContingentBeneficiary_State': 'BeneficiaryState', 'FR_ContingentBeneficiary_zipcode': 'BeneficiaryZipcode'}
252693_252751	252693_252751	IF COUNT(FR_PrimaryBeneficiaryLDPS) >= 3 THEN\\n\\nIF TRIM(FR_PrimaryBeneficiaryLDPS(3)) = "Y" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
252694	252694	IF TRIM(FR_ContingentBeneficiaryName_Display(3)) <> "" THEN\\nINCLUDE\	\\nELSE\\nEXCLUDE\\nEND IF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254133	254133	IF COUNT(FR_PrimaryBeneficiaryPercentage) >=3 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252696_252754	252696_252754	IF(count(FR_PrimaryBeneficiaryName) >= 4) THEN\\n      FR_PrimaryBeneficiaryName_Display(4) = FR_PrimaryBeneficiaryName(4)\\nELSE\\n\	 FR_PrimaryBeneficiaryName_Display(4) = ""\	\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiaryRelationship) >= 4) THEN\\n      FR_PrimaryBeneficiaryRelationship_Display(4) = FR_PrimaryBeneficiaryRelationship(4)\\nELSE\\n\	  FR_PrimaryBeneficiaryRelationship_Display(4) = ""\	\\nENDIF\\n\\nDIM iA AS INTEGER\\n/*\\nIF(Count(FR_PrimaryBeneficiaryPercentage) >= 4) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiaryPercentage(4), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n*/\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_PrimaryBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiaryRelationship_Display', 'type': 'String', 'is-array': True}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
252698_252756	252698_252756	DIM iA AS Integer\\nIF(count(FR_PrimaryBeneficiarySSN) >= 4) and FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_PrimaryBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_PrimaryBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(4), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_PrimaryBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_PhoneNumber) >= 4) THEN\\n      FR_PrimaryBeneficiary_PhoneNumber_Display(4) = FR_PrimaryBeneficiary_PhoneNumber(4)\\nELSE\\n\	 FR_PrimaryBeneficiary_PhoneNumber_Display(4) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_PrimaryBeneficiary_PhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_PrimaryBeneficiary_PhoneNumber': 'BeneficiaryContactPhoneNumber'}
252699_252757	252699_252757	DIM iA AS Integer\\n\\nIF(count(FR_PrimaryBeneficiary_StreetAddress) >= 4) THEN\\n     FR_PrimaryBeneficiary_StreetAddress_Display(4) = FR_PrimaryBeneficiary_StreetAddress(4)\\nELSE\\n\	 FR_PrimaryBeneficiary_StreetAddress_Display(4) = ""\	\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_City) >= 4) THEN\\n     FR_PrimaryBeneficiary_City_Display(4) = FR_PrimaryBeneficiary_City(4)\\nELSE\\n\	 FR_PrimaryBeneficiary_City_Display(4) = ""\	\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiary_State) >= 4) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiary_State(4), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_PrimaryBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_PrimaryBeneficiary_zipcode) >= 4) THEN\\n     FR_PrimaryBeneficiary_zipcode_Display(4) = FR_PrimaryBeneficiary_zipcode(4)\\nELSE\\n\	 FR_PrimaryBeneficiary_zipcode_Display(4) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_PrimaryBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_PrimaryBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_PrimaryBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_PrimaryBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_PrimaryBeneficiary_City': 'BeneficiaryCity', 'FR_PrimaryBeneficiary_State': 'BeneficiaryState', 'FR_PrimaryBeneficiary_zipcode': 'BeneficiaryZipcode'}
252701	252701	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"D")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiaryName_Display(4) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(4) = ""\	\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252702_252760	252702_252760	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"D")\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND ( INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryName_Display(4) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(4) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
252703_252761	252703_252761	FR_BAO_HighlightSSN_Field = "NO"\\n\\nDIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"D")\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX AND INDEX <> 0 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= INDEX THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(INDEX) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType', 'FR_ContingentBeneficiary_Category': 'Category'}
252704_252762	252704_252762	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"D")\\n\\n\\nDIM iA AS Integer\\nIF(count(FR_ContingentBeneficiarySSN) >= INDEX) and FR_BAO_DeliveryMethod = "E-Signature-Agent" AND ( INDEX <> 0 ) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(INDEX), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ContingentBeneficiarySSN_Display(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n     FOR iA = 1 TO 9\\n             FR_ContingentBeneficiarySSN_Display(iA) = ""\\n     NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiaryGender) >= INDEX) AND ( INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryGender_Display(4) = FR_ContingentBeneficiaryGender(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryGender_Display(4) = ""\	\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= INDEX) AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(INDEX), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 8\\n      FR_ContingentBeneficiaryBirthDate_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryPercentage) >= INDEX) AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(FR_ContingentBeneficiaryPercentage(INDEX), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_ContingentBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_ContingentBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiaryContactPhoneNumber) >= INDEX) AND ( INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryContactPhoneNumber_Display(4) = FR_ContingentBeneficiaryContactPhoneNumber(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryContactPhoneNumber_Display(4) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_ContingentBeneficiaryBirthDate_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiaryGender_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryPercentage_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryContactPhoneNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryContactPhoneNumber'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_ContingentBeneficiaryGender': 'BeneficiaryGender', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiaryContactPhoneNumber': 'BeneficiaryContactPhoneNumber'}
252705_252763	252705_252763	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"D")\\n\\n\\nDIM iA AS Integer\\n\\nIF(count(FR_ContingentBeneficiary_StreetAddress) >= INDEX)  AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_StreetAddress_Display(4) = FR_ContingentBeneficiary_StreetAddress(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_StreetAddress_Display(4) = ""\	\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_City) >= INDEX)  AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_City_Display(4) = FR_ContingentBeneficiary_City(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_City_Display(4) = ""\	\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiary_State) >= INDEX)  AND ( INDEX <> 0 ) THEN\\n   IRA_fx_LoadArray(FR_ContingentBeneficiary_State(INDEX), 2, "L")\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 2\\n      FR_ContingentBeneficiary_State_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n\\nIF(count(FR_ContingentBeneficiary_zipcode) >= INDEX)  AND ( INDEX <> 0 ) THEN\\n     FR_ContingentBeneficiary_zipcode_Display(4) = FR_ContingentBeneficiary_zipcode(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiary_zipcode_Display(4) = ""\	\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiary_City_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_State_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_StreetAddress_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_zipcode_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}, {'name': 'FR_ContingentBeneficiary_StreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryStreetAddress'}}, {'name': 'FR_ContingentBeneficiary_City', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryCity'}}, {'name': 'FR_ContingentBeneficiary_State', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryState'}}, {'name': 'FR_ContingentBeneficiary_zipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryZipcode'}}]	{'FR_ContingentBeneficiary_Category': 'Category', 'FR_ContingentBeneficiary_StreetAddress': 'BeneficiaryStreetAddress', 'FR_ContingentBeneficiary_City': 'BeneficiaryCity', 'FR_ContingentBeneficiary_State': 'BeneficiaryState', 'FR_ContingentBeneficiary_zipcode': 'BeneficiaryZipcode'}
252707_252765	252707_252765	IF COUNT(FR_PrimaryBeneficiaryLDPS) >= 4 THEN\\n\\nIF TRIM(FR_PrimaryBeneficiaryLDPS(4)) = "Y" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF\\n\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_PrimaryBeneficiaryLDPS': 'BeneficiaryLDPS'}
252708_252766	252708_252766	IF TRIM(FR_ContingentBeneficiaryName_Display(4)) <> "" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254134	254134	IF COUNT(FR_PrimaryBeneficiaryPercentage) >=4 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
248998	248998	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\nIF(FR_BAO_FormNumber = "F11007") THEN\\n   FR_02_FormId = "F11007 (06/19)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11007" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0619"\\n  INCLUDE\\nENDIF\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
252722	252722	IF TRIM(FR_ContingentBeneficiaryName_Display(1)) <> "" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nEND IF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254135	254135	IF COUNT(FR_PrimaryBeneficiaryPercentage) >= 1 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252724	252724	FR_JointTenantAccount = 'RightsOfSurvivorship'	[{'name': 'FR_JointTenantAccount', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'JointTenantAccountType'}}]	{'FR_JointTenantAccount': 'JointTenantAccountType'}
252725	252725	FR_JointTenantAccount = 'Entirety'	[{'name': 'FR_JointTenantAccount', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'JointTenantAccountType'}}]	{'FR_JointTenantAccount': 'JointTenantAccountType'}
252726	252726	IF(count(FR_PrimaryBeneficiaryName) >= 2) THEN\\n      FR_PrimaryBeneficiaryName_Display(2) = FR_PrimaryBeneficiaryName(2)\\nELSE\\n\	 FR_PrimaryBeneficiaryName_Display(2) = ""\	\\nENDIF\\n\\nDIM iA AS INTEGER\\n\\nIF(count(FR_PrimaryBeneficiaryRelationship) >= 2) THEN\\n      FR_PrimaryBeneficiaryRelationship_Display(2) = FR_PrimaryBeneficiaryRelationship(2)\\nELSE\\n\	  FR_PrimaryBeneficiaryRelationship_Display(2) = ""\	\\nENDIF\\n/*\\nIF(Count(FR_PrimaryBeneficiaryPercentage) >= 2) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiaryPercentage(2), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n*/\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_PrimaryBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiaryRelationship_Display', 'type': 'String', 'is-array': True}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship'}
252738	252738	IF TRIM(FR_ContingentBeneficiaryName_Display(2)) <> "" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254136	254136	IF COUNT(FR_PrimaryBeneficiaryPercentage) >= 2 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252740	252740	IF(count(FR_PrimaryBeneficiaryName) >= 3) THEN\\n      FR_PrimaryBeneficiaryName_Display(3) = FR_PrimaryBeneficiaryName(3)\\nELSE\\n\	 FR_PrimaryBeneficiaryName_Display(3) = ""\	\\nENDIF\\n/*\\nDIM iA AS integer\\nIF(Count(FR_PrimaryBeneficiaryPercentage) >= 3) THEN\\n   IRA_fx_LoadArray(FR_PrimaryBeneficiaryPercentage(3), 3, "R")\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n   FOR iA = 1 TO 3\\n      FR_PrimaryBeneficiaryPercentage_Display(iA) = ""\\n   NEXT iA\\nENDIF\\n*/\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}, {'name': 'FR_PrimaryBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252752	252752	IF TRIM(FR_ContingentBeneficiaryName_Display(3)) <> "" THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
254137	254137	IF COUNT(FR_PrimaryBeneficiaryPercentage) >= 3 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
252759	252759	DIM INDEX AS INTEGER\\nINDEX=Contains(FR_ContingentBeneficiary_Category,"D")\\n\\n\\nIF(count(FR_ContingentBeneficiaryName) >= INDEX) AND (INDEX <> 0 ) THEN\\n      FR_ContingentBeneficiaryName_Display(4) = FR_ContingentBeneficiaryName(INDEX)\\nELSE\\n\	 FR_ContingentBeneficiaryName_Display(4) = ""\	\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryName_Display', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiary_Category', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Category'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiary_Category': 'Category'}
254138	254138	IF COUNT(FR_PrimaryBeneficiaryPercentage) >= 4 THEN\\nINCLUDE\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
341061	341061	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\n\\nIF(FR_BAO_FormNumber = "F11015") THEN\\n   FR_02_FormId = "F11015 (08/23)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11015" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nELSE\\n EXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
341100	341100	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 1) AND (FR_BAO_DeliveryMethod = "E-Signature-Agent") THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(1) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341192	341192	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 2) AND (FR_BAO_DeliveryMethod = "E-Signature-Agent") THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(2) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341140	341140	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(3) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341142	341142	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 4) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(4), "mm/dd/yyyy"),10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_PrimaryBeneficiaryRelationship(4) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341211	341211	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 2) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      IF(FR_ContingentBeneficiaryRelationship(2) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341263	341263	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_ContingentBeneficiaryRelationship(1) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341323	341323	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      IF(FR_ContingentBeneficiaryRelationship(3) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341324_339947	341324_339947	FR_BAO_HighlightSSN_Field = "NO"\\n\\nIF(count(FR_ContingentBeneficiarySSN) >= 4 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n\\nIF(count(FR_ContingentBeneficiary_BeneficiaryType)) >= 4 THEN\\nIF FR_ContingentBeneficiary_BeneficiaryType(4) = "Organization" THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nENDIF\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\n\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_ContingentBeneficiary_BeneficiaryType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryType'}}]	{'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_ContingentBeneficiary_BeneficiaryType': 'BeneficiaryType'}
341325	341325	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 4) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(4), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n      IF(FR_ContingentBeneficiaryRelationship(4) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)    \\n     ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
341326_377863	341326_377863	IF(count(FR_ContingentBeneficiaryName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
341337_377861	341337_377861	IF(count(FR_ContingentBeneficiaryRelationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
341340_377818_339746	341340_377818_339746	IF(count(FR_ContingentBeneficiaryName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
341351_377829_339757	341351_377829_339757	IF(count(FR_ContingentBeneficiaryRelationship) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
341281	341281	IF  FR_BAO_DeliveryMethod <> "E-Signature-Agent"  AND FR_BAO_02_SpousalWaiver = "Y" THEN\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF	[{'name': 'FR_BAO_02_SpousalWaiver', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'SpousalWaiver'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_BAO_02_SpousalWaiver': 'SpousalWaiver', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342530	342530	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\nIF(FR_BAO_FormNumber = "F40334") THEN\\n   FR_02_FormId = "F40334 (08/23)"\\n   FR_02_CurrentForm((SYS_SubDocInDocument) ) = "F40334" \\n   FR_02_CurrentVersionDate((SYS_SubDocInDocument) ) = "0823"\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
342181_342182_342868_342869	342181_342182_342868_342869	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustAccountName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustAccountName, 68, "L")\\n    FOR iA = 1 TO 68\\n            FR_BAO_02_TrustAccountName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 68\\n               FR_BAO_02_TrustAccountName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustAccountName', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustAccountName', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustAccountName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}]	{'FR_BAO_TrustAccountName': 'TrustAccountName'}
342183_342184	342183_342184	IF(Len(FR_BAO_TrustSSN) >= 1 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_BAO_TrustSSN': 'TrustSSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342185	342185	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustSSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustSSN, 9, "L")\\n    FOR iA = 1 TO 9\\n          FR_BAO_02_TrustSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_TrustSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_TrustEffectiveDate) >= 1) THEN\\n    IRA_fx_LoadArray(Format(FR_BAO_TrustEffectiveDate,"mmddyyyy"), 8, "L")\\n    FOR iA = 1 TO 8\\n           FR_BAO_02_TrustEffectiveDate(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustEffectiveDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_TrustSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustEffectiveDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'TrustEffectiveDate'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_BAO_TrustEffectiveDate': 'TrustEffectiveDate', 'FR_BAO_TrustSSN': 'TrustSSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342189	342189	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n          FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342218_339143	342218_339143	FR_BAO_Decendent_PlanType = 'Traditional IRA'	[{'name': 'FR_BAO_Decendent_PlanType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentPlanType'}}]	{'FR_BAO_Decendent_PlanType': 'DecendentPlanType'}
342219_343122	342219_343122	FR_BAO_TypeOfTrust = 'Family'	[{'name': 'FR_BAO_TypeOfTrust', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TypeOfTrust'}}]	{'FR_BAO_TypeOfTrust': 'TypeOfTrust'}
342220_339153	342220_339153	FR_BAO_Decendent_PlanType = 'Roth IRA'	[{'name': 'FR_BAO_Decendent_PlanType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentPlanType'}}]	{'FR_BAO_Decendent_PlanType': 'DecendentPlanType'}
342221_343121	342221_343121	FR_BAO_TypeOfTrust = 'Charitable'	[{'name': 'FR_BAO_TypeOfTrust', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TypeOfTrust'}}]	{'FR_BAO_TypeOfTrust': 'TypeOfTrust'}
342222_343123	342222_343123	FR_BAO_TypeOfTrust = 'Irrevocable'	[{'name': 'FR_BAO_TypeOfTrust', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TypeOfTrust'}}]	{'FR_BAO_TypeOfTrust': 'TypeOfTrust'}
342223_343125	342223_343125	FR_BAO_TypeOfTrust = 'Testamentary'	[{'name': 'FR_BAO_TypeOfTrust', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TypeOfTrust'}}]	{'FR_BAO_TypeOfTrust': 'TypeOfTrust'}
342225_343127	342225_343127	IF Count(FR_AccountOwner_PhoneType) >=1 THEN\\nIF(FR_AccountOwner_PhoneType(1) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342230_343205_344916	342230_343205_344916	IF(count(FR_AccountOwnerSSN) >= 2 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342234_343132_343553	342234_343132_343553	IF Count(FR_BAO_EmploymentStatus) >= 1 THEN\\n    IF((FR_BAO_EmploymentStatus(1) = "UEMP")  OR (FR_BAO_EmploymentStatus(1) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342265_343224_344933	342265_343224_344933	IF(count(FR_AccountOwnerEmail) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerEmail', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmailAddress'}}]	{'FR_AccountOwnerEmail': 'EmailAddress'}
342269_343228_344936	342269_343228_344936	IF Count(FR_AccountOwner_PhoneType) >= 2 THEN\\nIF(FR_AccountOwner_PhoneType(2) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342270_343229	342270_343229	IF Count(FR_AccountOwner_PhoneType) >=2 THEN\\nIF(FR_AccountOwner_PhoneType(2) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342271_343230_344938	342271_343230_344938	IF Count(FR_AccountOwner_PhoneType) >= 2 THEN\\nIF(FR_AccountOwner_PhoneType(2) = "Business") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342286_343253_344941	342286_343253_344941	IF(count(FR_AccountOwnerSSN) >= 3 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342287_343254	342287_343254	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(3), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342290_343186	342290_343186	IF Count(FR_BAO_EmploymentStatus) >= 2 THEN\\n    IF((FR_BAO_EmploymentStatus(2) = "UEMP")  OR (FR_BAO_EmploymentStatus(2) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342321_343272_344958	342321_343272_344958	IF(count(FR_AccountOwnerEmail) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerEmail', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmailAddress'}}]	{'FR_AccountOwnerEmail': 'EmailAddress'}
342325_343276	342325_343276	IF Count(FR_AccountOwner_PhoneType) >=3 THEN\\nIF(FR_AccountOwner_PhoneType(3) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342326_343277	342326_343277	IF Count(FR_AccountOwner_PhoneType) >=3 THEN\\nIF(FR_AccountOwner_PhoneType(3) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342327_343278_344963	342327_343278_344963	IF Count(FR_AccountOwner_PhoneType) >= 3 THEN\\nIF(FR_AccountOwner_PhoneType(3) = "Business") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342333_343283	342333_343283	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerMiddleName) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(4), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerMiddleName': 'MiddleName'}
342334_343284_344966	342334_343284_344966	IF(count(FR_AccountOwnerSSN) >= 4 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342335_343285	342335_343285	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 4) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(4), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342336_343286_344968	342336_343286_344968	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerResidentialState) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerResidentialState(4), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerResidentialState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_AccountOwnerResidentialState': 'ResidentialState'}
342337_343287_344969	342337_343287_344969	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerMailingState) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMailingState(4), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMailingState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingState'}}]	{'FR_AccountOwnerMailingState': 'MailingState'}
342338_343234	342338_343234	IF Count(FR_BAO_EmploymentStatus) >= 3 THEN\\n    IF((FR_BAO_EmploymentStatus(3) = "UEMP")  OR (FR_BAO_EmploymentStatus(3) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342339_343235	342339_343235	IF Count(FR_BAO_EmploymentStatus) >=3 THEN\\n    IF(FR_BAO_EmploymentStatus(3) = "EMPL" OR FR_BAO_EmploymentStatus(3) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342340_343236_343638	342340_343236_343638	IF(count(FR_BAO_EmployerName) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerName'}}]	{'FR_BAO_EmployerName': 'EmployerName'}
342356_343288	342356_343288	IF(count(FR_AccountOwnerFirstName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_AccountOwnerFirstName': 'FirstName'}
342357_343289	342357_343289	IF(count(FR_AccountOwnerLastName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_AccountOwnerLastName': 'LastName'}
342358_343290_344980	342358_343290_344980	IF(count(FR_AccountOwnerMailingStreetAddress) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingStreetAddress'}}]	{'FR_AccountOwnerMailingStreetAddress': 'MailingStreetAddress'}
342359_343291_344982	342359_343291_344982	IF(count(FR_AccountOwnerMailingCity) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingCity'}}]	{'FR_AccountOwnerMailingCity': 'MailingCity'}
342360_343292_344981	342360_343292_344981	IF(count(FR_AccountOwnerMailingZipcode) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingZipcode'}}]	{'FR_AccountOwnerMailingZipcode': 'MailingZipcode'}
342361_343293_344973	342361_343293_344973	IF(count(FR_AccountOwnerResidentialStreetAddress) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_AccountOwnerResidentialStreetAddress': 'ResidentialStreetAddress'}
342362_343294_344979	342362_343294_344979	IF(count(FR_AccountOwnerResidentialCity) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_AccountOwnerResidentialCity': 'ResidentialCity'}
342363_343295_344974	342363_343295_344974	IF(count(FR_AccountOwnerResidentialZipcode) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_AccountOwnerResidentialZipcode': 'ResidentialZipcode'}
342364_343296_344976	342364_343296_344976	IF Count(FR_AccountOwnerMaritalStatus) >= 4 THEN\\n    IF(FR_AccountOwnerMaritalStatus(4) = "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
342365_343297_344977	342365_343297_344977	IF Count(FR_AccountOwnerMaritalStatus) >= 4 THEN\\n    IF(FR_AccountOwnerMaritalStatus(4) <> "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
342366_343298_344978	342366_343298_344978	IF Count(FR_AccountOwnerCitizenship) >= 4 THEN\\n    IF FR_AccountOwnerCitizenship(4) <> "US" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerCitizenship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Citzenship'}}]	{'FR_AccountOwnerCitizenship': 'Citzenship'}
342367_343299_344970	342367_343299_344970	IF Count(FR_AccountOwnerGender) >= 4 THEN\\n    IF(FR_AccountOwnerGender(4) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
342368_343300_344971	342368_343300_344971	IF Count(FR_AccountOwnerGender) >= 4 THEN\\n    IF(FR_AccountOwnerGender(4) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
342369_343301_344983	342369_343301_344983	IF(count(FR_AccountOwnerEmail) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerEmail', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmailAddress'}}]	{'FR_AccountOwnerEmail': 'EmailAddress'}
342371_343303_344984	342371_343303_344984	IF(count(FR_AccountOwnerBusinessPhone) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerBusinessPhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BusinessPhone'}}]	{'FR_AccountOwnerBusinessPhone': 'BusinessPhone'}
342372_343304_344985	342372_343304_344985	IF(count(FR_AccountOwnerExtension) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Extension'}}]	{'FR_AccountOwnerExtension': 'Extension'}
342373_343305	342373_343305	IF Count(FR_AccountOwner_PhoneType) >=4 THEN\\nIF(FR_AccountOwner_PhoneType(4) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342374_343306	342374_343306	IF Count(FR_AccountOwner_PhoneType) >=4 THEN\\nIF(FR_AccountOwner_PhoneType(4) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342375_343307_344988	342375_343307_344988	IF Count(FR_AccountOwner_PhoneType) >= 4 THEN\\nIF(FR_AccountOwner_PhoneType(4) = "Business") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342377_343310_343679	342377_343310_343679	DIM iA AS Integer\\n\\nIF(Count(FR_BAO_EmployerState) >= 4) THEN\\n    IRA_fx_LoadArray(FR_BAO_EmployerState(4), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_EmployerState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerState'}}]	{'FR_BAO_EmployerState': 'EmployerState'}
342378_343350	342378_343350	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerMiddleName) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(5), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerMiddleName': 'MiddleName'}
342379_343351_344991	342379_343351_344991	IF(count(FR_AccountOwnerSSN) >= 5 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342380_343352	342380_343352	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 5) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(5), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 5) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(5), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
342381_343353_344993	342381_343353_344993	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerResidentialState) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerResidentialState(5), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerResidentialState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_AccountOwnerResidentialState': 'ResidentialState'}
342382_343354_344994	342382_343354_344994	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerMailingState) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMailingState(5), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMailingState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingState'}}]	{'FR_AccountOwnerMailingState': 'MailingState'}
342383_343311	342383_343311	IF Count(FR_BAO_EmploymentStatus) >= 4 THEN\\n    IF((FR_BAO_EmploymentStatus(4) = "UEMP")  OR (FR_BAO_EmploymentStatus(4) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342384_343312_343681	342384_343312_343681	IF Count(FR_BAO_EmploymentStatus) >= 4 THEN\\n    IF(FR_BAO_EmploymentStatus(4) = "EMPL" OR FR_BAO_EmploymentStatus(4) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342385_343313_343682	342385_343313_343682	IF(count(FR_BAO_EmployerName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerName'}}]	{'FR_BAO_EmployerName': 'EmployerName'}
342386_343314_343692	342386_343314_343692	IF(count(FR_BAO_EmployerTitle) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentTitle'}}]	{'FR_BAO_EmployerTitle': 'EmploymentTitle'}
342387_343315_343683	342387_343315_343683	IF(count(FR_BAO_EmployerAddress) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerStreetAddress'}}]	{'FR_BAO_EmployerAddress': 'EmployerStreetAddress'}
342388_343694	342388_343694	IF(count(FR_BAO_EmployerZipcode) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerZipcode'}}]	{'FR_BAO_EmployerZipcode': 'EmployerZipcode'}
342389_343317_343693	342389_343317_343693	IF(count(FR_BAO_EmployerCity) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerCity'}}]	{'FR_BAO_EmployerCity': 'EmployerCity'}
342390_343318_343684	342390_343318_343684	IF Count(FR_AccountOwnerTradeCompanyCB) >= 4 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
342391_343319_343685	342391_343319_343685	IF(count(FR_BAO_Trade_PersonName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfPerson'}}]	{'FR_BAO_Trade_PersonName': 'NameOfPerson'}
342392_343320_343695	342392_343320_343695	IF(count(FR_BAO_Trade_CompanyNameSymbol) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_CompanyNameSymbol', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'CompanyNameSymbol'}}]	{'FR_BAO_Trade_CompanyNameSymbol': 'CompanyNameSymbol'}
342393_343321_343686	342393_343321_343686	IF Count(FR_AccountOwnerTIAACREFCB) >= 4 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 4 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
342394_343322_343687	342394_343322_343687	IF(count(FR_BAO_Firm_Relationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_Firm_Relationship': 'RelationshipToEmployee'}
342395_343323_343688	342395_343323_343688	IF(count(FR_BAO_TIAACREF_Relationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_TIAACREF_Relationship': 'RelationshipToEmployee'}
342396_343324_343689	342396_343324_343689	IF(count(FR_BAO_Firm_PersonName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_Firm_PersonName': 'NameOfEmployee'}
342397_343325_343690	342397_343325_343690	IF(count(FR_BAO_TIAACREF_PersonName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_TIAACREF_PersonName': 'NameOfEmployee'}
342398_343326_343696	342398_343326_343696	IF(count(FR_BAO_Firm_Name) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Name', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfFirm'}}]	{'FR_BAO_Firm_Name': 'NameOfFirm'}
342399_343327_343697	342399_343327_343697	IF Count(FR_AccountOwnerTIAACREFCB) >= 4 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox'}
342400_343328_343691	342400_343328_343691	IF Count(FR_AccountOwnerSeniorMilitaryCB) >= 4 THEN\\n    IF(FR_AccountOwnerSeniorMilitaryCB(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerSeniorMilitaryCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SeniorMilitaryCheckBox'}}]	{'FR_AccountOwnerSeniorMilitaryCB': 'SeniorMilitaryCheckBox'}
342401	342401	IF(count(FR_AccountOwnerFirstName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_AccountOwnerFirstName': 'FirstName'}
342402_343356	342402_343356	IF(count(FR_AccountOwnerLastName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_AccountOwnerLastName': 'LastName'}
342403_343357_345005	342403_343357_345005	IF(count(FR_AccountOwnerMailingStreetAddress) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingStreetAddress'}}]	{'FR_AccountOwnerMailingStreetAddress': 'MailingStreetAddress'}
342404_343358_345007	342404_343358_345007	IF(count(FR_AccountOwnerMailingCity) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingCity'}}]	{'FR_AccountOwnerMailingCity': 'MailingCity'}
342405_343359_345006	342405_343359_345006	IF(count(FR_AccountOwnerMailingZipcode) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingZipcode'}}]	{'FR_AccountOwnerMailingZipcode': 'MailingZipcode'}
342406_343360_344998	342406_343360_344998	IF(count(FR_AccountOwnerResidentialStreetAddress) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_AccountOwnerResidentialStreetAddress': 'ResidentialStreetAddress'}
342407_343361_345004	342407_343361_345004	IF(count(FR_AccountOwnerResidentialCity) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_AccountOwnerResidentialCity': 'ResidentialCity'}
342408_343362_344999	342408_343362_344999	IF(count(FR_AccountOwnerResidentialZipcode) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_AccountOwnerResidentialZipcode': 'ResidentialZipcode'}
342409_343363_345001	342409_343363_345001	IF Count(FR_AccountOwnerMaritalStatus) >= 5 THEN\\n    IF(FR_AccountOwnerMaritalStatus(5) = "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
342410_343364_345002	342410_343364_345002	IF Count(FR_AccountOwnerMaritalStatus) >= 5 THEN\\n    IF(FR_AccountOwnerMaritalStatus(5) <> "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
342411_343365_345003	342411_343365_345003	IF Count(FR_AccountOwnerCitizenship) >= 5 THEN\\n    IF FR_AccountOwnerCitizenship(5) <> "US" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerCitizenship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Citzenship'}}]	{'FR_AccountOwnerCitizenship': 'Citzenship'}
342412_343366_344995	342412_343366_344995	IF Count(FR_AccountOwnerGender) >= 5 THEN\\n    IF(FR_AccountOwnerGender(5) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
342413_343367_344996	342413_343367_344996	IF Count(FR_AccountOwnerGender) >= 5 THEN\\n    IF(FR_AccountOwnerGender(5) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
342414_343368_345008	342414_343368_345008	IF(count(FR_AccountOwnerEmail) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerEmail', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmailAddress'}}]	{'FR_AccountOwnerEmail': 'EmailAddress'}
342416_343370_345009	342416_343370_345009	IF(count(FR_AccountOwnerBusinessPhone) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerBusinessPhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BusinessPhone'}}]	{'FR_AccountOwnerBusinessPhone': 'BusinessPhone'}
342417_343371_345010	342417_343371_345010	IF(count(FR_AccountOwnerExtension) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Extension'}}]	{'FR_AccountOwnerExtension': 'Extension'}
342418_343372	342418_343372	IF Count(FR_AccountOwner_PhoneType) >=5 THEN\\nIF(FR_AccountOwner_PhoneType(5) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342419_343373	342419_343373	IF Count(FR_AccountOwner_PhoneType) >=5 THEN\\nIF(FR_AccountOwner_PhoneType(5) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342420_343374_345013	342420_343374_345013	IF Count(FR_AccountOwner_PhoneType) >= 5 THEN\\nIF(FR_AccountOwner_PhoneType(5) = "Business") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
363552_343331_343723	363552_343331_343723	DIM iA AS Integer\\n\\nIF(Count(FR_BAO_EmployerState) >= 5) THEN\\n    IRA_fx_LoadArray(FR_BAO_EmployerState(5), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_EmployerState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerState'}}]	{'FR_BAO_EmployerState': 'EmployerState'}
363553_343398	363553_343398	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerMiddleName) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(6), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerMiddleName': 'MiddleName'}
363554_343399_345016	363554_343399_345016	IF(count(FR_AccountOwnerSSN) >= 6 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
363555_343400	363555_343400	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 6) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(6), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 6) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(6), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
363556_343401_345018	363556_343401_345018	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerResidentialState) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerResidentialState(6), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerResidentialState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_AccountOwnerResidentialState': 'ResidentialState'}
363557_343402_345019	363557_343402_345019	DIM iA AS Integer\\n\\nIF(Count(FR_AccountOwnerMailingState) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMailingState(6), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerMailingState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingState'}}]	{'FR_AccountOwnerMailingState': 'MailingState'}
363558_343332	363558_343332	IF Count(FR_BAO_EmploymentStatus) >= 5 THEN\\n    IF((FR_BAO_EmploymentStatus(5) = "UEMP")  OR (FR_BAO_EmploymentStatus(5) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
363559_343333_343725	363559_343333_343725	IF Count(FR_BAO_EmploymentStatus) >= 5 THEN\\n    IF(FR_BAO_EmploymentStatus(5) = "EMPL" OR FR_BAO_EmploymentStatus(5) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
363560_343334_343726	363560_343334_343726	IF(count(FR_BAO_EmployerName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerName'}}]	{'FR_BAO_EmployerName': 'EmployerName'}
363561_343335_343736	363561_343335_343736	IF(count(FR_BAO_EmployerTitle) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentTitle'}}]	{'FR_BAO_EmployerTitle': 'EmploymentTitle'}
363562_343336_343727	363562_343336_343727	IF(count(FR_BAO_EmployerAddress) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerStreetAddress'}}]	{'FR_BAO_EmployerAddress': 'EmployerStreetAddress'}
363563_343337_343738	363563_343337_343738	IF(count(FR_BAO_EmployerZipcode) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerZipcode'}}]	{'FR_BAO_EmployerZipcode': 'EmployerZipcode'}
363564_343338_343737	363564_343338_343737	IF(count(FR_BAO_EmployerCity) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerCity'}}]	{'FR_BAO_EmployerCity': 'EmployerCity'}
363565_343339_343728	363565_343339_343728	IF Count(FR_AccountOwnerTradeCompanyCB) >= 5 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(5) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
363566_343340_343729	363566_343340_343729	IF(count(FR_BAO_Trade_PersonName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfPerson'}}]	{'FR_BAO_Trade_PersonName': 'NameOfPerson'}
363567_343341_343739	363567_343341_343739	IF(count(FR_BAO_Trade_CompanyNameSymbol) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_CompanyNameSymbol', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'CompanyNameSymbol'}}]	{'FR_BAO_Trade_CompanyNameSymbol': 'CompanyNameSymbol'}
363568_343342_343730	363568_343342_343730	IF Count(FR_AccountOwnerTIAACREFCB) >= 5 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(5) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 5 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(5) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
363569_343343_343731	363569_343343_343731	IF(count(FR_BAO_Firm_Relationship) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_Firm_Relationship': 'RelationshipToEmployee'}
363570_343344_343732	363570_343344_343732	IF(count(FR_BAO_TIAACREF_Relationship) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_TIAACREF_Relationship': 'RelationshipToEmployee'}
363571_343345_343733	363571_343345_343733	IF(count(FR_BAO_Firm_PersonName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_Firm_PersonName': 'NameOfEmployee'}
363572_343346_343734	363572_343346_343734	IF(count(FR_BAO_TIAACREF_PersonName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_TIAACREF_PersonName': 'NameOfEmployee'}
363573_343347_343355_343740	363573_343347_343355_343740	IF(count(FR_BAO_Firm_Name) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Name', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfFirm'}}]	{'FR_BAO_Firm_Name': 'NameOfFirm'}
363574_343348_343741	363574_343348_343741	IF Count(FR_AccountOwnerTIAACREFCB) >= 5 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(5) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox'}
363575_343349_343735	363575_343349_343735	IF Count(FR_AccountOwnerSeniorMilitaryCB) >= 5 THEN\\n    IF(FR_AccountOwnerSeniorMilitaryCB(5) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerSeniorMilitaryCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SeniorMilitaryCheckBox'}}]	{'FR_AccountOwnerSeniorMilitaryCB': 'SeniorMilitaryCheckBox'}
363576_343403	363576_343403	IF(count(FR_AccountOwnerFirstName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_AccountOwnerFirstName': 'FirstName'}
363577_343404	363577_343404	IF(count(FR_AccountOwnerLastName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_AccountOwnerLastName': 'LastName'}
363578_343405_345030	363578_343405_345030	IF(count(FR_AccountOwnerMailingStreetAddress) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingStreetAddress'}}]	{'FR_AccountOwnerMailingStreetAddress': 'MailingStreetAddress'}
363579_343406_345032	363579_343406_345032	IF(count(FR_AccountOwnerMailingCity) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingCity'}}]	{'FR_AccountOwnerMailingCity': 'MailingCity'}
363580_343407_345031	363580_343407_345031	IF(count(FR_AccountOwnerMailingZipcode) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerMailingZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MailingZipcode'}}]	{'FR_AccountOwnerMailingZipcode': 'MailingZipcode'}
363581_345023	363581_345023	IF(count(FR_AccountOwnerResidentialStreetAddress) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_AccountOwnerResidentialStreetAddress': 'ResidentialStreetAddress'}
363582_343408_343409_345029	363582_343408_343409_345029	IF(count(FR_AccountOwnerResidentialCity) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_AccountOwnerResidentialCity': 'ResidentialCity'}
363583_343410_345024	363583_343410_345024	IF(count(FR_AccountOwnerResidentialZipcode) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerResidentialZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_AccountOwnerResidentialZipcode': 'ResidentialZipcode'}
363584_343411_345026	363584_343411_345026	IF Count(FR_AccountOwnerMaritalStatus) >= 6 THEN\\n    IF(FR_AccountOwnerMaritalStatus(6) = "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
363585_343412_345027	363585_343412_345027	IF Count(FR_AccountOwnerMaritalStatus) >= 6 THEN\\n    IF(FR_AccountOwnerMaritalStatus(6) <> "Married") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerMaritalStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MaritalStatus'}}]	{'FR_AccountOwnerMaritalStatus': 'MaritalStatus'}
363586_343413_345028	363586_343413_345028	IF Count(FR_AccountOwnerCitizenship) >= 6 THEN\\n    IF FR_AccountOwnerCitizenship(6) <> "US" THEN\\n       INCLUDE\\n    ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerCitizenship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Citzenship'}}]	{'FR_AccountOwnerCitizenship': 'Citzenship'}
363587_343414_345020	363587_343414_345020	IF Count(FR_AccountOwnerGender) >= 6 THEN\\n    IF(FR_AccountOwnerGender(6) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
363588_343415_345021	363588_343415_345021	IF Count(FR_AccountOwnerGender) >= 6 THEN\\n    IF(FR_AccountOwnerGender(6) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Gender'}}]	{'FR_AccountOwnerGender': 'Gender'}
363589_343416_345033	363589_343416_345033	IF(count(FR_AccountOwnerEmail) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerEmail', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmailAddress'}}]	{'FR_AccountOwnerEmail': 'EmailAddress'}
363591_343418_345034	363591_343418_345034	IF(count(FR_AccountOwnerBusinessPhone) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerBusinessPhone', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BusinessPhone'}}]	{'FR_AccountOwnerBusinessPhone': 'BusinessPhone'}
363592_343419_345035	363592_343419_345035	IF(count(FR_AccountOwnerExtension) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerExtension', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Extension'}}]	{'FR_AccountOwnerExtension': 'Extension'}
363593_343420	363593_343420	IF Count(FR_AccountOwner_PhoneType) >=6 THEN\\nIF(FR_AccountOwner_PhoneType(6) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
363594_343421	363594_343421	IF Count(FR_AccountOwner_PhoneType) >=6 THEN\\nIF(FR_AccountOwner_PhoneType(6) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
363595_343422_345038	363595_343422_345038	IF Count(FR_AccountOwner_PhoneType) >= 6 THEN\\nIF(FR_AccountOwner_PhoneType(6) = "Business") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
342466_343379_343767	342466_343379_343767	DIM iA AS Integer\\n\\nIF(Count(FR_BAO_EmployerState) >= 6) THEN\\n    IRA_fx_LoadArray(FR_BAO_EmployerState(6), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_EmployerState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerState'}}]	{'FR_BAO_EmployerState': 'EmployerState'}
342467_377474_340082	342467_377474_340082	DIM iA AS Integer\\n\\nIF(Len(FR_BAO_DecendentMiddleName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_DecendentMiddleName, 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_DecendentMiddleName', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentMiddleName'}}]	{'FR_BAO_DecendentMiddleName': 'DecendentMiddleName'}
342468	342468	DIM iA AS Integer\\n\\nIF(Len(FR_BAO_DecendentBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_BAO_DecendentBirthDate, "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n         FR_BAO_02_Decendent_BirthDate_display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Decendent_BirthDate_display(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_DecendentDeathDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_BAO_DecendentDeathDate, "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Decendent_DeathDate_display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n              FR_BAO_02_Decendent_DeathDate_display(iA) = ""\\n      NEXT iA\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Decendent_BirthDate_display', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Decendent_DeathDate_display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_DecendentBirthDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentBirthDate'}}, {'name': 'FR_BAO_DecendentDeathDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentDeathDate'}}]	{'FR_BAO_DecendentBirthDate': 'DecendentBirthDate', 'FR_BAO_DecendentDeathDate': 'DecendentDeathDate'}
342469_343380	342469_343380	IF Count(FR_BAO_EmploymentStatus) >=6 THEN\\n    IF((FR_BAO_EmploymentStatus(6) = "UEMP")  OR (FR_BAO_EmploymentStatus(6) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342470_343381_343769	342470_343381_343769	IF Count(FR_BAO_EmploymentStatus) >= 6 THEN\\n    IF(FR_BAO_EmploymentStatus(6) = "EMPL" OR FR_BAO_EmploymentStatus(6) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
342471_343382_343770	342471_343382_343770	IF(count(FR_BAO_EmployerName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerName'}}]	{'FR_BAO_EmployerName': 'EmployerName'}
342472_343383_343780	342472_343383_343780	IF(count(FR_BAO_EmployerTitle) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentTitle'}}]	{'FR_BAO_EmployerTitle': 'EmploymentTitle'}
342473_343384_343771	342473_343384_343771	IF(count(FR_BAO_EmployerAddress) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerStreetAddress'}}]	{'FR_BAO_EmployerAddress': 'EmployerStreetAddress'}
342474_343385_343782	342474_343385_343782	IF(count(FR_BAO_EmployerZipcode) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerZipcode', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerZipcode'}}]	{'FR_BAO_EmployerZipcode': 'EmployerZipcode'}
342475_343386_343781	342475_343386_343781	IF(count(FR_BAO_EmployerCity) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_EmployerCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmployerCity'}}]	{'FR_BAO_EmployerCity': 'EmployerCity'}
342476_343387	342476_343387	IF Count(FR_AccountOwnerTradeCompanyCB) >=6 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
342477_343388_343773	342477_343388_343773	IF(count(FR_BAO_Trade_PersonName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfPerson'}}]	{'FR_BAO_Trade_PersonName': 'NameOfPerson'}
342478_343389_343783	342478_343389_343783	IF(count(FR_BAO_Trade_CompanyNameSymbol) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Trade_CompanyNameSymbol', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'CompanyNameSymbol'}}]	{'FR_BAO_Trade_CompanyNameSymbol': 'CompanyNameSymbol'}
342479_343390_343774	342479_343390_343774	IF Count(FR_AccountOwnerTIAACREFCB) >= 6 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF\\nIF Count(FR_AccountOwnerMemberFirmCB) >= 6 THEN\\n    IF(FR_AccountOwnerMemberFirmCB(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}, {'name': 'FR_AccountOwnerMemberFirmCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirmCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox', 'FR_AccountOwnerMemberFirmCB': 'FirmCheckBox'}
342480_343391_343775	342480_343391_343775	IF(count(FR_BAO_Firm_Relationship) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_Firm_Relationship': 'RelationshipToEmployee'}
342481_343392_343776	342481_343392_343776	IF(count(FR_BAO_TIAACREF_Relationship) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_Relationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'RelationshipToEmployee'}}]	{'FR_BAO_TIAACREF_Relationship': 'RelationshipToEmployee'}
342482_343393_343777	342482_343393_343777	IF(count(FR_BAO_Firm_PersonName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_Firm_PersonName': 'NameOfEmployee'}
342483_343394_343778	342483_343394_343778	IF(count(FR_BAO_TIAACREF_PersonName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_TIAACREF_PersonName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfEmployee'}}]	{'FR_BAO_TIAACREF_PersonName': 'NameOfEmployee'}
342484_343395_343784	342484_343395_343784	IF(count(FR_BAO_Firm_Name) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_Firm_Name', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'NameOfFirm'}}]	{'FR_BAO_Firm_Name': 'NameOfFirm'}
342485_343396_343785	342485_343396_343785	IF Count(FR_AccountOwnerTIAACREFCB) >= 6 THEN\\n    IF(FR_AccountOwnerTIAACREFCB(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTIAACREFCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TIAACREFCheckBox'}}]	{'FR_AccountOwnerTIAACREFCB': 'TIAACREFCheckBox'}
342486_343397_343779	342486_343397_343779	IF Count(FR_AccountOwnerSeniorMilitaryCB) >= 6 THEN\\n    IF(FR_AccountOwnerSeniorMilitaryCB(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerSeniorMilitaryCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SeniorMilitaryCheckBox'}}]	{'FR_AccountOwnerSeniorMilitaryCB': 'SeniorMilitaryCheckBox'}
342504_343473_340081	342504_343473_340081	FR_BAO_ConfirmSuppressionStatus_Ind = 'N'	[{'name': 'FR_BAO_ConfirmSuppressionStatus_Ind', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ConfirmSuppressionStatus'}}]	{'FR_BAO_ConfirmSuppressionStatus_Ind': 'ConfirmSuppressionStatus'}
342505_343474	342505_343474	IF(FR_BAO_ConfirmSuppressionStatus_Ind <> "N") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_ConfirmSuppressionStatus_Ind', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ConfirmSuppressionStatus'}}]	{'FR_BAO_ConfirmSuppressionStatus_Ind': 'ConfirmSuppressionStatus'}
342524_343477	342524_343477	FR_BAO_Trust_DelegateToAppointedAgent = 'Y'	[{'name': 'FR_BAO_Trust_DelegateToAppointedAgent', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DelegateToAppointedAgent'}}]	{'FR_BAO_Trust_DelegateToAppointedAgent': 'DelegateToAppointedAgent'}
342525_343478	342525_343478	FR_BAO_Trust_DelegateToOutsideProfessional = 'Y'	[{'name': 'FR_BAO_Trust_DelegateToOutsideProfessional', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DelegateToOutsideProfessional'}}]	{'FR_BAO_Trust_DelegateToOutsideProfessional': 'DelegateToOutsideProfessional'}
345265	345265	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\n\\nIF(FR_BAO_FormNumber = "F40285") THEN\\n   FR_02_FormId = "F40285 (08/23)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F40285" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"\\n   INCLUDE\\nELSE\\n   EXCLUDE\	\\nENDIF\\n\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
342870	342870	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustSSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustSSN, 9, "L")\\n    FOR iA = 1 TO 9\\n          FR_BAO_02_TrustSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_TrustSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_TrustEffectiveDate) >= 1) THEN\\n    IRA_fx_LoadArray(Format(FR_BAO_TrustEffectiveDate,"mmddyyyy"), 8, "L")\\n    FOR iA = 1 TO 8\\n           FR_BAO_02_TrustEffectiveDate(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\n\\nIF(Len(FR_BAO_Trust_AmmendmentDate) >= 1) THEN\\n    IRA_fx_LoadArray(Format(FR_BAO_Trust_AmmendmentDate,"mmddyyyy"), 8, "L")\\n    FOR iA = 1 TO 8\\n           FR_BAO_02_Trust_AmmendmentDate(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_Trust_AmmendmentDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustEffectiveDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_TrustSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Trust_AmmendmentDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustEffectiveDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'TrustEffectiveDate'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}, {'name': 'FR_BAO_Trust_AmmendmentDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'TrustAmmendmentDate'}}]	{'FR_BAO_TrustEffectiveDate': 'TrustEffectiveDate', 'FR_BAO_TrustSSN': 'TrustSSN', 'FR_BAO_Trust_AmmendmentDate': 'TrustAmmendmentDate'}
343102_339133	343102_339133	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
343120	343120	FR_BAO_TypeOfTrust = 'Revocable'	[{'name': 'FR_BAO_TypeOfTrust', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TypeOfTrust'}}]	{'FR_BAO_TypeOfTrust': 'TypeOfTrust'}
343124	343124	FR_BAO_TypeOfTrust = 'Living'	[{'name': 'FR_BAO_TypeOfTrust', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TypeOfTrust'}}]	{'FR_BAO_TypeOfTrust': 'TypeOfTrust'}
343129	343129	IF Count(FR_AccountOwner_GrantorType) >= 1 THEN\\n    IF(FR_AccountOwner_GrantorType(1) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343130	343130	IF Count(FR_AccountOwner_GrantorType) >= 1 THEN\\n    IF(FR_AccountOwner_GrantorType(1) <> "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343209	343209	IF Count(FR_AccountOwner_GrantorType) >= 2 THEN\\n    IF(FR_AccountOwner_GrantorType(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343210	343210	IF Count(FR_AccountOwner_GrantorType) >= 2 THEN\\n    IF(FR_AccountOwner_GrantorType(2) <> "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343257	343257	IF Count(FR_AccountOwner_GrantorType) >= 3 THEN\\n    IF(FR_AccountOwner_GrantorType(3) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343258	343258	IF Count(FR_AccountOwner_GrantorType) >= 3 THEN\\n    IF(FR_AccountOwner_GrantorType(3) <> "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343308	343308	IF Count(FR_AccountOwner_GrantorType) >= 4 THEN\\n    IF(FR_AccountOwner_GrantorType(4) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343309	343309	IF Count(FR_AccountOwner_GrantorType) >= 4 THEN\\n    IF(FR_AccountOwner_GrantorType(4) <> "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343375	343375	IF Count(FR_AccountOwner_GrantorType) >= 5 THEN\\n    IF(FR_AccountOwner_GrantorType(5) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343376	343376	IF Count(FR_AccountOwner_GrantorType) >= 5 THEN\\n    IF(FR_AccountOwner_GrantorType(5) <> "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343423	343423	IF Count(FR_AccountOwner_GrantorType) >= 6 THEN\\n    IF(FR_AccountOwner_GrantorType(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343424	343424	IF Count(FR_AccountOwner_GrantorType) >= 6 THEN\\n    IF(FR_AccountOwner_GrantorType(6) <> "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwner_GrantorType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'GrantorInd'}}]	{'FR_AccountOwner_GrantorType': 'GrantorInd'}
343426	343426	DIM iA AS Integer\\nIF(Count(FR_Grantor_Middlename) >= 1) THEN\\n    IRA_fx_LoadArray(FR_Grantor_Middlename(1), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_Grantor_Middlename', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_Grantor_Middlename': 'MiddleName'}
343427	343427	IF(count(FR_Grantor_SSN) >= 1 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_Grantor_SSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}]	{'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_Grantor_SSN': 'SSN'}
343428	343428	DIM iA AS Integer\\nIF(Count(FR_Grantor_SSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_Grantor_SSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_Grantor_BirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_Grantor_BirthDate(1), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_Grantor_SSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_Grantor_BirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_Grantor_SSN': 'SSN', 'FR_Grantor_BirthDate': 'BirthDate'}
343429	343429	DIM iA AS Integer\\n\\nIF(Count(FR_Grantor_ResState) >= 1) THEN\\n    IRA_fx_LoadArray(FR_Grantor_ResState(1), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_Grantor_ResState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_Grantor_ResState': 'ResidentialState'}
343430	343430	IF(count(FR_Grantor_FirstName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_Grantor_FirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_Grantor_FirstName': 'FirstName'}
343431	343431	IF(count(FR_Grantor_LastName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_Grantor_LastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_Grantor_LastName': 'LastName'}
343432	343432	IF(count(FR_Grantor_ResAddress) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_Grantor_ResAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_Grantor_ResAddress': 'ResidentialStreetAddress'}
343433	343433	IF(count(FR_Grantor_ResCity) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_Grantor_ResCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_Grantor_ResCity': 'ResidentialCity'}
343434	343434	IF(count(FR_Grantor_ResZip) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_Grantor_ResZip', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_Grantor_ResZip': 'ResidentialZipcode'}
343436	343436	DIM iA AS Integer\\nIF(Count(FR_JointGrantor_MiddleName) >= 1) THEN\\n    IRA_fx_LoadArray(FR_JointGrantor_MiddleName(1), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_JointGrantor_MiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_JointGrantor_MiddleName': 'MiddleName'}
343437	343437	IF(count(FR_JointGrantor_SSN) >= 1 AND FR_BAO_DeliveryMethod <> "E-Signature-Agent") THEN\\n     FR_BAO_HighlightSSN_Field = "YES"\\nELSE\\n     FR_BAO_HighlightSSN_Field = "NO"\\nENDIF\\nINCLUDE	[{'name': 'FR_BAO_HighlightSSN_Field', 'type': 'String', 'is-array': False}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_JointGrantor_SSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}]	{'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_JointGrantor_SSN': 'SSN'}
343438	343438	DIM iA AS Integer\\nIF(Count(FR_JointGrantor_SSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_JointGrantor_SSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_JointGrantor_Birthdate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_JointGrantor_Birthdate(1), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n            FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}, {'name': 'FR_JointGrantor_SSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_JointGrantor_Birthdate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}]	{'FR_BAO_DeliveryMethod': 'DeliveryMethod', 'FR_JointGrantor_SSN': 'SSN', 'FR_JointGrantor_Birthdate': 'BirthDate'}
343439	343439	DIM iA AS Integer\\n\\nIF(Count(FR_JointGrantor_ResState) >= 1) THEN\\n    IRA_fx_LoadArray(FR_JointGrantor_ResState(1), 2, "L")\\n    FOR iA = 1 TO 2\\n            FR_BAO_02_State(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 2\\n               FR_BAO_02_State(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_State', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_JointGrantor_ResState', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialState'}}]	{'FR_JointGrantor_ResState': 'ResidentialState'}
343440	343440	IF(count(FR_JointGrantor_FirstName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_JointGrantor_FirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}]	{'FR_JointGrantor_FirstName': 'FirstName'}
343441	343441	IF(count(FR_JointGrantor_LastName) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_JointGrantor_LastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}]	{'FR_JointGrantor_LastName': 'LastName'}
343442	343442	IF(count(FR_JointGrantor_ResStreetAddress) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_JointGrantor_ResStreetAddress', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialStreetAddress'}}]	{'FR_JointGrantor_ResStreetAddress': 'ResidentialStreetAddress'}
343443	343443	IF(count(FR_JointGrantor_ResCity) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_JointGrantor_ResCity', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialCity'}}]	{'FR_JointGrantor_ResCity': 'ResidentialCity'}
343444	343444	IF(count(FR_JointGrantor_ResZip) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_JointGrantor_ResZip', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'ResidentialZipcode'}}]	{'FR_JointGrantor_ResZip': 'ResidentialZipcode'}
363714	363714	IF(count(FR_AccountOwnerName) >= 6) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
363718	363718	IF(count(FR_AccountOwnerName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
363719	363719	IF(count(FR_AccountOwnerName) >= 5) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}]	{'FR_AccountOwnerName': 'FullName'}
342528	342528	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN\\n\\nIF(FR_BAO_FormNumber = "F11208") THEN\\n   FR_02_FormId = "F11208 (08/23)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11208" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"   \\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
377241	377241	IF((COMMON.GO_DR_DOC_CODE = "BAO_MANAGED" OR COMMON.GO_DR_DOC_CODE = "BAO_SD_RETAIL_PKG" OR COMMON.GO_DR_DOC_CODE = "BAO_SD_IRA_PKG" OR COMMON.GO_DR_DOC_CODE = "BAO_V2E") AND (FR_BAO_DreyfusGovCashManageInvester <> "Y" AND FR_BAO_DreyfusGovCashManageServiceShares <> "Y" AND FR_BAO_DreyfusGovSecCashManageInvestor <> "Y" AND FR_BAO_FederatedHermesGovObligationsCash <> "Y" AND FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash <> "Y")) THEN\\n     INCLUDE\\nENDIF	[{'name': 'GO_DR_DOC_CODE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DocCode'}}, {'name': 'FR_BAO_DreyfusGovCashManageInvester', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DreyfusGovCashManageInvester'}}, {'name': 'FR_BAO_DreyfusGovCashManageServiceShares', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DreyfusGovCashManageServiceShares'}}, {'name': 'FR_BAO_DreyfusGovSecCashManageInvestor', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DreyfusGovSecCashManageInvestor'}}, {'name': 'FR_BAO_FederatedHermesGovObligationsCash', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FederatedHermesGovObligationsCash'}}, {'name': 'FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FederatedHermesTrustUSTreasuryOblicationsCash'}}]	{'GO_DR_DOC_CODE': 'DocCode', 'FR_BAO_DreyfusGovCashManageInvester': 'DreyfusGovCashManageInvester', 'FR_BAO_DreyfusGovCashManageServiceShares': 'DreyfusGovCashManageServiceShares', 'FR_BAO_DreyfusGovSecCashManageInvestor': 'DreyfusGovSecCashManageInvestor', 'FR_BAO_FederatedHermesGovObligationsCash': 'FederatedHermesGovObligationsCash', 'FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash': 'FederatedHermesTrustUSTreasuryOblicationsCash'}
377239	377239	IF((COMMON.GO_DR_DOC_CODE = "BAO_MANAGED" OR \\n    COMMON.GO_DR_DOC_CODE = "BAO_SD_IRA_PKG" OR \\n    COMMON.GO_DR_DOC_CODE = "BAO_SD_RETAIL_PKG" OR \\n    COMMON.GO_DR_DOC_CODE = "BAO_V2E") AND FR_BAO_Liquid = "") OR\\n   (FR_BAO_Liquid = "Y") THEN\\n     INCLUDE\\nENDIF	[{'name': 'GO_DR_DOC_CODE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DocCode'}}, {'name': 'FR_BAO_Liquid', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'Liquid'}}]	{'GO_DR_DOC_CODE': 'DocCode', 'FR_BAO_Liquid': 'Liquid'}
345294	345294	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\n\\nIF(FR_BAO_FormNumber = "F11207") THEN\\n   FR_02_FormId = "F11207 (08/23)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11207" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
344917	344917	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 2) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(2), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(2), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
344937	344937	IF Count(FR_AccountOwner_PhoneType) >= 2 THEN\\nIF(FR_AccountOwner_PhoneType(2) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343592	343592	IF Count(FR_BAO_EmploymentStatus) >= 2 THEN\\n    IF ((FR_BAO_EmploymentStatus(2) = "UEMP") OR (FR_BAO_EmploymentStatus(2) = "RETD")) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
344942	344942	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 3) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(3), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
344961	344961	IF Count(FR_AccountOwner_PhoneType) >= 3 THEN\\nIF(FR_AccountOwner_PhoneType(3) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
344962	344962	IF Count(FR_AccountOwner_PhoneType) >= 3 THEN\\nIF(FR_AccountOwner_PhoneType(3) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343636	343636	IF Count(FR_BAO_EmploymentStatus) >= 3 THEN\\n    IF ((FR_BAO_EmploymentStatus(3) = "UEMP") OR (FR_BAO_EmploymentStatus(3) = "RETD") ) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
343637	343637	IF Count(FR_BAO_EmploymentStatus) >= 3 THEN\\n    IF(FR_BAO_EmploymentStatus(3) = "EMPL" OR FR_BAO_EmploymentStatus(3) = "SEMP") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
344964	344964	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerTitle) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerTitle(4), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Title(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Title(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerFirstName) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerFirstName(4), 27, "L")\\n    FOR iA = 1 TO 27\\n            FR_02_FirstName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 27\\n               FR_02_FirstName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerMiddleName) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(4), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Title', 'type': 'String', 'is-array': True}, {'name': 'FR_02_FirstName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Prefix'}}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerFirstName': 'FirstName', 'FR_AccountOwnerTitle': 'Prefix', 'FR_AccountOwnerMiddleName': 'MiddleName'}
344965	344965	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerLastName) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerLastName(4), 29, "L")\\n    FOR iA = 1 TO 29\\n            FR_02_LastName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 29\\n               FR_02_LastName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerSuffix) >= 4) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSuffix(4), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_LastName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSuffix', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Suffix'}}]	{'FR_AccountOwnerLastName': 'LastName', 'FR_AccountOwnerSuffix': 'Suffix'}
344967	344967	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 4) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(4), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(4), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
344986	344986	IF Count(FR_AccountOwner_PhoneType) >= 4 THEN\\nIF(FR_AccountOwner_PhoneType(4) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
344987	344987	IF Count(FR_AccountOwner_PhoneType) >= 4 THEN\\nIF(FR_AccountOwner_PhoneType(4) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343680	343680	IF Count(FR_BAO_EmploymentStatus) >= 4 THEN\\n    IF ((FR_BAO_EmploymentStatus(4) = "UEMP") OR (FR_BAO_EmploymentStatus(4) = "RETD") ) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
344989	344989	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerTitle) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerTitle(5), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Title(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Title(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerFirstName) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerFirstName(5), 27, "L")\\n    FOR iA = 1 TO 27\\n            FR_02_FirstName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 27\\n               FR_02_FirstName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerMiddleName) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(5), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Title', 'type': 'String', 'is-array': True}, {'name': 'FR_02_FirstName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Prefix'}}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerFirstName': 'FirstName', 'FR_AccountOwnerTitle': 'Prefix', 'FR_AccountOwnerMiddleName': 'MiddleName'}
344990	344990	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerLastName) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerLastName(5), 29, "L")\\n    FOR iA = 1 TO 29\\n            FR_02_LastName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 29\\n               FR_02_LastName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerSuffix) >= 5) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSuffix(5), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_LastName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSuffix', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Suffix'}}]	{'FR_AccountOwnerLastName': 'LastName', 'FR_AccountOwnerSuffix': 'Suffix'}
344992	344992	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 5) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(5), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 5) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(5), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
345011	345011	IF Count(FR_AccountOwner_PhoneType) >= 5 THEN\\nIF(FR_AccountOwner_PhoneType(5) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
345012	345012	IF Count(FR_AccountOwner_PhoneType) >= 5 THEN\\nIF(FR_AccountOwner_PhoneType(5) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343724	343724	IF Count(FR_BAO_EmploymentStatus) >= 5 THEN\\n    IF ((FR_BAO_EmploymentStatus(5) = "UEMP") OR (FR_BAO_EmploymentStatus(5) = "RETD") ) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
345014	345014	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerTitle) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerTitle(6), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Title(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Title(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerFirstName) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerFirstName(6), 27, "L")\\n    FOR iA = 1 TO 27\\n            FR_02_FirstName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 27\\n               FR_02_FirstName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerMiddleName) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerMiddleName(6), 1, "L")\\n    FOR iA = 1 TO 1\\n            FR_02_MiddleName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 1\\n               FR_02_MiddleName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Title', 'type': 'String', 'is-array': True}, {'name': 'FR_02_FirstName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_MiddleName', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerFirstName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FirstName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerTitle', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Prefix'}}, {'name': 'FR_AccountOwnerMiddleName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'MiddleName'}}]	{'FR_AccountOwnerFirstName': 'FirstName', 'FR_AccountOwnerTitle': 'Prefix', 'FR_AccountOwnerMiddleName': 'MiddleName'}
345015	345015	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerLastName) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerLastName(6), 29, "L")\\n    FOR iA = 1 TO 29\\n            FR_02_LastName(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 29\\n               FR_02_LastName(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerSuffix) >= 6) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSuffix(6), 3, "L")\\n    FOR iA = 1 TO 3\\n            FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 3\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_LastName', 'type': 'String', 'is-array': True}, {'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerLastName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'LastName'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSuffix', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'Suffix'}}]	{'FR_AccountOwnerLastName': 'LastName', 'FR_AccountOwnerSuffix': 'Suffix'}
345017	345017	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerSSN) >= 6) AND FR_BAO_DeliveryMethod = "E-Signature-Agent"  THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerSSN(6), 9, "L")\\n    FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_AccountOwnerBirthDate) >= 6) THEN\\n   IRA_fx_LoadArray(Format(FR_AccountOwnerBirthDate(6), "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_AccountOwnerBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BirthDate'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_AccountOwnerBirthDate': 'BirthDate', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
345036	345036	IF Count(FR_AccountOwner_PhoneType) >= 6 THEN\\nIF(FR_AccountOwner_PhoneType(6) = "Mobile") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
345037	345037	IF Count(FR_AccountOwner_PhoneType) >= 6 THEN\\nIF(FR_AccountOwner_PhoneType(6) = "Home") THEN\\n     INCLUDE\\nENDIF\\nENDIF	[{'name': 'FR_AccountOwner_PhoneType', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'PhoneType'}}]	{'FR_AccountOwner_PhoneType': 'PhoneType'}
343768	343768	IF Count(FR_BAO_EmploymentStatus) >= 6 THEN\\n    IF ((FR_BAO_EmploymentStatus(6) = "UEMP") OR (FR_BAO_EmploymentStatus(6) = "RETD") ) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
343772	343772	IF Count(FR_AccountOwnerTradeCompanyCB) >= 6 THEN\\n    IF(FR_AccountOwnerTradeCompanyCB(6) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_AccountOwnerTradeCompanyCB', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'TradeCompanyCheckBox'}}]	{'FR_AccountOwnerTradeCompanyCB': 'TradeCompanyCheckBox'}
366685	366685	IF(count(FR_AccountOwnerName) >= 2 AND FR_BAO_AccountType = "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
366687	366687	IF(count(FR_AccountOwnerName) >= 3 AND FR_BAO_AccountType = "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
366689	366689	IF(count(FR_AccountOwnerName) >= 4 AND FR_BAO_AccountType <> "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
366690	366690	IF(count(FR_AccountOwnerName) >= 5 AND FR_BAO_AccountType <> "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
366691	366691	IF(count(FR_AccountOwnerName) >= 6 AND FR_BAO_AccountType <> "Custodial") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_AccountOwnerName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'FullName'}}, {'name': 'FR_BAO_AccountType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'AccountType'}}]	{'FR_AccountOwnerName': 'FullName', 'FR_BAO_AccountType': 'AccountType'}
377270	377270	IF(FR_BAO_FormNumber = "F11543") THEN\\n    FR_02_WPID= "TBRISI/BRKENRRET"\\n    FR_02_FormId = "F11543 (10/24)"\\n    FR_02_CurrentForm(SYS_SubDocInDocument) = "F11543" \\n    FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "1024" \\n\\n   INCLUDE\\n\\n      IF (BAO_IRAOption_2 = "Inherited Traditional IRA" OR BAO_IRAOption_2 = "Inherited Roth IRA") THEN\\n         BAO_Captiva_2D_Barcode_flag = "N"\\n      ELSE\\n\\n         BAO_Captiva_2D_Barcode_flag = "Y"\\n      ENDIF\\nENDIF	[{'name': 'BAO_Captiva_2D_Barcode_flag', 'type': 'String', 'is-array': False}, {'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_WPID', 'type': 'String', 'is-array': False}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'BAO_IRAOption_2', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'BAO_IRAOption_2': 'IraOption'}
377271	377271	IF(FR_BAO_IraOption = "SEP IRA") THEN\\n\	IF(FR_BAO_EmployerTaxID <> FR_AccountOwnerSSN(1)) THEN\\n\	\	FR_BAO_02_SEPIRA_TIN_SSN = FR_BAO_EmployerTaxID\\n\	ELSEIF (Count(FR_AccountOwnerSSN) >= 1) THEN\\n\	\	IF(FR_AccountOwnerSSN(1) <> "") THEN\\n\	\	\	FR_BAO_02_SEPIRA_TIN_SSN = "XXXXX" & Mid(FR_AccountOwnerSSN(1),6,4)\\n\	\	ELSE\\n\	\	\	FR_BAO_02_SEPIRA_TIN_SSN = ""\\n\	\	ENDIF\\n\	ELSE\\n\	\	FR_BAO_02_SEPIRA_TIN_SSN = ""\\n\	ENDIF\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_SEPIRA_TIN_SSN', 'type': 'String', 'is-array': False}, {'name': 'FR_AccountOwnerSSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'SSN'}}, {'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}, {'name': 'FR_BAO_EmployerTaxID', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'EmployerTaxID'}}]	{'FR_AccountOwnerSSN': 'SSN', 'FR_BAO_IraOption': 'IraOption', 'FR_BAO_EmployerTaxID': 'EmployerTaxID'}
377873	377873	FR_BAO_IraOption = 'Inherited Roth IRA'	[{'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}]	{'FR_BAO_IraOption': 'IraOption'}
377301	377301	FR_BAO_IraOption = 'Inherited Traditional IRA'	[{'name': 'FR_BAO_IraOption', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'IraOption'}}]	{'FR_BAO_IraOption': 'IraOption'}
377475	377475	DIM iA AS Integer\\nIF(Len(FR_BAO_DecendentSSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_DecendentSSN, 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_BAO_02_AccountOwnerSSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_BAO_02_AccountOwnerSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_DecendentBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_BAO_DecendentBirthDate, "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_AccountOwner_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_DecendentDeathDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_BAO_DecendentDeathDate, "mmddyyyy"), 8, "L")\\n   FOR iA = 1 TO 8\\n      FR_02_Suffix(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_02_Suffix(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_02_Suffix', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwnerSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_AccountOwner_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_DecendentBirthDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentBirthDate'}}, {'name': 'FR_BAO_DecendentDeathDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentDeathDate'}}, {'name': 'FR_BAO_DecendentSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentSSN'}}]	{'FR_BAO_DecendentBirthDate': 'DecendentBirthDate', 'FR_BAO_DecendentDeathDate': 'DecendentDeathDate', 'FR_BAO_DecendentSSN': 'DecendentSSN'}
377476	377476	DIM iA AS Integer\\nIF(Len(FR_BAO_TrustSSN) >= 1) THEN\\n    IRA_fx_LoadArray(FR_BAO_TrustSSN, 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_BAO_02_TrustSSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_BAO_02_TrustSSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_TrustSSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_TrustEffectiveDate) >= 1) THEN\\n    IRA_fx_LoadArray(Format(FR_BAO_TrustEffectiveDate,"mmddyyyy"), 8, "L")\\n    FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 8\\n               FR_BAO_02_TrustEffectiveDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_TrustEffectiveDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_TrustSSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustEffectiveDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'TrustEffectiveDate'}}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_TrustSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustSSN'}}]	{'FR_BAO_TrustEffectiveDate': 'TrustEffectiveDate', 'FR_BAO_TrustSSN': 'TrustSSN'}
377477_377478_377479_377480_377481_377482_377483_377484_377485	377477_377478_377479_377480_377481_377482_377483_377484_377485	IF(FR_BAO_DecendentSSN <> "") THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_BAO_DecendentSSN', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentSSN'}}]	{'FR_BAO_DecendentSSN': 'DecendentSSN'}
377757	377757	IF Count(FR_PrimaryBeneficiaryGender) >=2 THEN\\n    IF(FR_PrimaryBeneficiaryGender(2) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
377758	377758	IF Count(FR_PrimaryBeneficiaryName) >= 3 AND Count(FR_PrimaryBeneficiaryRelationship) >= 3 THEN\\n  IF FR_PrimaryBeneficiaryName(3) > ""  AND \\n      FR_PrimaryBeneficiaryRelationship(3) <> "ESTATE" AND \\n      FR_PrimaryBeneficiaryRelationship(3) <> "TRUST"  AND \\n      FR_PrimaryBeneficiaryRelationship(3) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_BeneficiaryFlag3 = "true"\\n  ELSE\\n    FR_BAO_02_BeneficiaryFlag3 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_BeneficiaryFlag3= "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_PrimaryBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_PrimaryBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_BeneficiaryFlag3', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_PrimaryBeneficiaryName': 'BeneficiaryName', 'FR_PrimaryBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
377791	377791	IF Count(FR_PrimaryBeneficiaryGender) >=4 THEN\\n    IF(FR_PrimaryBeneficiaryGender(4) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
377793	377793	IF Count(FR_PrimaryBeneficiaryGender) >=3 THEN\\n    IF(FR_PrimaryBeneficiaryGender(3) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_PrimaryBeneficiaryGender': 'BeneficiaryGender'}
377794	377794	IF Count(FR_ContingentBeneficiaryName) >= 1 AND Count(FR_ContingentBeneficiaryRelationship) >= 1 THEN\\n  IF FR_ContingentBeneficiaryName(1) > ""  AND \\n      FR_ContingentBeneficiaryRelationship(1) <> "ESTATE" AND \\n      FR_ContingentBeneficiaryRelationship(1) <> "TRUST"  AND \\n      FR_ContingentBeneficiaryRelationship(1) <> "OTHER ENTITY"  AND \\n      COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  THEN\\n    FR_BAO_02_ContingencyFlag1 = "true"\\n  ELSE\\n    FR_BAO_02_ContingencyFlag1 = "false"\\n  ENDIF\\nELSE\\n  FR_BAO_02_ContingencyFlag1 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_BAO_02_ContingencyFlag1', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName', 'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
377810	377810	IF Count(FR_ContingentBeneficiaryGender) >=1 THEN\\n    IF(FR_ContingentBeneficiaryGender(1) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377811	377811	IF Count(FR_ContingentBeneficiaryGender) >=1 THEN\\n    IF(FR_ContingentBeneficiaryGender(1) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377813	377813	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 2) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(2), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 2) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(2), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_ContingentBeneficiaryRelationship(2) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
377815	377815	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 3) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(3), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_ContingentBeneficiaryRelationship(3) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
377844	377844	IF Count(FR_ContingentBeneficiaryGender) >=2 THEN\\n    IF(FR_ContingentBeneficiaryGender(2) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377845	377845	IF Count(FR_ContingentBeneficiaryGender) >=2 THEN\\n    IF(FR_ContingentBeneficiaryGender(2) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377846	377846	IF Count(FR_ContingentBeneficiaryGender) >=3 THEN\\n    IF(FR_ContingentBeneficiaryGender(3) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377847	377847	IF Count(FR_ContingentBeneficiaryGender) >=3 THEN\\n    IF(FR_ContingentBeneficiaryGender(3) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377849	377849	DIM iA AS Integer\\nIF(Count(FR_ContingentBeneficiarySSN) >= 4) THEN\\n    IRA_fx_LoadArray(FR_ContingentBeneficiarySSN(4), 9, "L")\\n    FOR iA = 1 TO 5\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = "X"\\n    NEXT iA\\n    FOR iA = 6 TO 9\\n            FR_ER_02_ContingentBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_ContingentBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_ContingentBeneficiaryBirthDate) >= 4) THEN\\n   IRA_fx_LoadArray(Format(FR_ContingentBeneficiaryBirthDate(4), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n       IF(FR_ContingentBeneficiaryRelationship(4) = "TRUST") THEN\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n      ELSE\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      ENDIF\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}, {'name': 'FR_ER_02_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_ContingentBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_ContingentBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship', 'FR_ContingentBeneficiarySSN': 'BeneficiarySSN', 'FR_ContingentBeneficiaryBirthDate': 'BeneficiaryDateOfBirth'}
377864	377864	IF Count(FR_ContingentBeneficiaryGender) >=4 THEN\\n    IF(FR_ContingentBeneficiaryGender(4) = "Male") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377865	377865	IF Count(FR_ContingentBeneficiaryGender) >=4 THEN\\n    IF(FR_ContingentBeneficiaryGender(4) = "Female") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryGender', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryGender'}}]	{'FR_ContingentBeneficiaryGender': 'BeneficiaryGender'}
377868	377868	IF  COMMON.GO_MI_PCKGE_DLVRY_TYP = "C"  AND FR_BAO_02_SpousalWaiver = "Y" THEN\\n  FR_BAO_02_SpousalWaiverFlag1 = "true"\\nELSE\\n  FR_BAO_02_SpousalWaiverFlag1 = "false"\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_SpousalWaiver', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'SpousalWaiver'}}, {'name': 'FR_BAO_02_SpousalWaiverFlag1', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_DLVRY_TYP', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryType'}}]	{'FR_BAO_02_SpousalWaiver': 'SpousalWaiver', 'GO_MI_PCKGE_DLVRY_TYP': 'DeliveryType'}
377869_377870_377871	377869_377870_377871	FR_BAO_02_SpousalWaiverFlag1 = 'true'	[{'name': 'FR_BAO_02_SpousalWaiver', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'SpousalWaiver'}}, {'name': 'FR_BAO_02_SpousalWaiverFlag1', 'type': 'String', 'is-array': False}]	{'FR_BAO_02_SpousalWaiver': 'SpousalWaiver'}
331062	331062	IF(FR_BAO_FormNumber = "F11071") THEN\\n   FR_02_WPID= "TBRMIP"\\n   FR_02_FormId = "F11071 (10/19)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F11071" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "1019"\\n  INCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_WPID', 'type': 'String', 'is-array': False}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}]	{'FR_BAO_FormNumber': 'FormNumber'}
331075	331075	FR_BAO_TrustType = 'Revocable'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331498	331498	FR_BAO_TrustType = 'Charitable'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331499	331499	FR_BAO_TrustType = 'Family'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331500	331500	FR_BAO_TrustType = 'Irrevocable'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331501	331501	FR_BAO_TrustType = 'Irrevocable Living'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331502	331502	FR_BAO_TrustType = 'Living'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331503	331503	FR_BAO_TrustType = 'Testamentary'	[{'name': 'FR_BAO_TrustType', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'TrustType'}}]	{'FR_BAO_TrustType': 'TrustType'}
331525	331525	DIM iA AS Integer\\nIF(Count(FR_AccountOwnerBrokerageAccountNumber) >= 1) THEN\\n    IRA_fx_LoadArray(FR_AccountOwnerBrokerageAccountNumber(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_BAO_02_AccountOwnerBrokerageAccountNumber(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\n\\n    INCLUDE\\n\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_BAO_02_AccountOwnerBrokerageAccountNumber(iA) = ""\\n      NEXT iA\\n\\n      EXCLUDE\\n\\nENDIF	[{'name': 'FR_BAO_02_AccountOwnerBrokerageAccountNumber', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_AccountOwnerBrokerageAccountNumber', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BrokerageAccountNumber'}}]	{'FR_AccountOwnerBrokerageAccountNumber': 'BrokerageAccountNumber'}
338805	338805	IF COMMON.GO_MI_PCKGE_CDE = "AccountOpeningRS" THEN //Form exclusive to BAO MANAGED V2 PACKAGE\\n\\nIF(FR_BAO_FormNumber = "F40031") THEN\\n   FR_02_FormId = "F40031 (08/23)"\\n   FR_02_CurrentForm(SYS_SubDocInDocument) = "F40031" \\n   FR_02_CurrentVersionDate(SYS_SubDocInDocument) = "0823"\\n  INCLUDE\\nELSE\\n  EXCLUDE\\nENDIF\\n\\nELSE\\nEXCLUDE\\nENDIF	[{'name': 'FR_02_CurrentForm', 'type': 'String', 'is-array': True}, {'name': 'SYS_SubDocInDocument', 'type': 'Integer', 'is-array': False}, {'name': 'FR_02_CurrentVersionDate', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_FormNumber', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'FormNumber'}}, {'name': 'FR_02_FormId', 'type': 'String', 'is-array': False}, {'name': 'GO_MI_PCKGE_CDE', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'ApplicationId'}}]	{'FR_BAO_FormNumber': 'FormNumber', 'GO_MI_PCKGE_CDE': 'ApplicationId'}
339252	339252	IF Count(FR_BAO_EmploymentStatus) >= 1 THEN\\n    IF(FR_BAO_EmploymentStatus(1) = "UEMP" OR FR_BAO_EmploymentStatus(1) = "RETD" ) THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_BAO_EmploymentStatus', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'EmploymentStatus'}}]	{'FR_BAO_EmploymentStatus': 'EmploymentStatus'}
340083	340083	DIM iA AS Integer\\n\\nIF(Len(FR_BAO_DecendentBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_BAO_DecendentBirthDate, "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n         FR_BAO_02_Decendent_BirthDate_display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Decendent_BirthDate_display(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Len(FR_BAO_DecendentDeathDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_BAO_DecendentDeathDate, "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Decendent_DeathDate_display(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n              FR_BAO_02_Decendent_DeathDate_display(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_BAO_02_Decendent_BirthDate_display', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Decendent_DeathDate_display', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_DecendentBirthDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentBirthDate'}}, {'name': 'FR_BAO_DecendentDeathDate', 'type': 'Date', 'is-array': False, 'xml-schema': {'tag-name': 'DecendentDeathDate'}}]	{'FR_BAO_DecendentBirthDate': 'DecendentBirthDate', 'FR_BAO_DecendentDeathDate': 'DecendentDeathDate'}
339314	339314	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 1) AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(1), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 1) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(1), "mm/dd/yyyy"), 10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
339315	339315	IF(Count(FR_PrimaryBeneficiaryPercentage) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
339619	339619	IF(Count(FR_PrimaryBeneficiaryPercentage) >= 2) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
339467	339467	DIM iA AS Integer\\nIF(Count(FR_PrimaryBeneficiarySSN) >= 3)  AND FR_BAO_DeliveryMethod = "E-Signature-Agent" THEN\\n    IRA_fx_LoadArray(FR_PrimaryBeneficiarySSN(3), 9, "L")\\n    FOR iA = 1 TO 9\\n            FR_ER_02_PrimaryBeneficiarySSN(iA) = FR_02_TempArray(iA)\\n    NEXT iA\\nELSE\\n       FOR iA = 1 TO 9\\n               FR_ER_02_PrimaryBeneficiarySSN(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nIF(Count(FR_PrimaryBeneficiaryBirthDate) >= 3) THEN\\n   IRA_fx_LoadArray(Format(FR_PrimaryBeneficiaryBirthDate(3), "mm/dd/yyyy"),10, "L")\\n   FOR iA = 1 TO 10\\n           FR_BAO_02_Beneficiary_BirthDate(iA) = FR_02_TempArray(iA)\\n   NEXT iA\\nELSE\\n       FOR iA = 1 TO 10\\n               FR_BAO_02_Beneficiary_BirthDate(iA) = ""\\n      NEXT iA\\nENDIF\\n\\nINCLUDE	[{'name': 'FR_ER_02_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True}, {'name': 'FR_BAO_02_Beneficiary_BirthDate', 'type': 'String', 'is-array': True}, {'name': 'FR_02_TempArray', 'type': 'String', 'is-array': True}, {'name': 'FR_PrimaryBeneficiarySSN', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiarySSN'}}, {'name': 'FR_PrimaryBeneficiaryBirthDate', 'type': 'Date', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryDateOfBirth'}}, {'name': 'FR_BAO_DeliveryMethod', 'type': 'String', 'is-array': False, 'xml-schema': {'tag-name': 'DeliveryMethod'}}]	{'FR_PrimaryBeneficiarySSN': 'BeneficiarySSN', 'FR_PrimaryBeneficiaryBirthDate': 'BeneficiaryDateOfBirth', 'FR_BAO_DeliveryMethod': 'DeliveryMethod'}
339470	339470	IF(Count(FR_PrimaryBeneficiaryPercentage) >= 3) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
339486	339486	IF(Count(FR_PrimaryBeneficiaryPercentage) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_PrimaryBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_PrimaryBeneficiaryPercentage': 'BeneficiaryPercentage'}
339784	339784	IF(Count(FR_ContingentBeneficiaryPercentage) >= 1) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryPercentage', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryPercentage'}}]	{'FR_ContingentBeneficiaryPercentage': 'BeneficiaryPercentage'}
339741	339741	IF Count(FR_ContingentBeneficiaryLDPS) >=2 THEN\\n    IF(FR_ContingentBeneficiaryLDPS(2) = "Y") THEN\\n       INCLUDE\\n   ENDIF\\nENDIF	[{'name': 'FR_ContingentBeneficiaryLDPS', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryLDPS'}}]	{'FR_ContingentBeneficiaryLDPS': 'BeneficiaryLDPS'}
339950	339950	IF(Count (FR_ContingentBeneficiaryName) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryName', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryName'}}]	{'FR_ContingentBeneficiaryName': 'BeneficiaryName'}
339961	339961	IF(Count (FR_ContingentBeneficiaryRelationship) >= 4) THEN\\n     INCLUDE\\nENDIF	[{'name': 'FR_ContingentBeneficiaryRelationship', 'type': 'String', 'is-array': True, 'xml-schema': {'tag-name': 'BeneficiaryRelationship'}}]	{'FR_ContingentBeneficiaryRelationship': 'BeneficiaryRelationship'}
