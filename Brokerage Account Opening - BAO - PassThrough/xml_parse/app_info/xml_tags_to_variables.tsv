Tag	VarOI	Name	VarType	VarSampleValue	Area	Start	Length	ImpliedDigits	Format	Action
BatchCounter	761	GO_DR_BATCH_COUNTER	Integer		Tag Value	0	0	0	General Number	Leave As Is
RequestUserId	757	GO_DR_RQST_USER_ID	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
RequestUserName	758	GO_DR_RQST_USER_NAME	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
RequestDateTime	759	GO_DR_RQST_DATE_TIME	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
CompositeOrchestrationId	3710	GO_DR_COMPOSITE_ORCH_ID	String	CompOrchId_11	Tag Value	0	0	0	Trim Blanks	Leave As Is
OrchestrationId	2410	GO_DR_ORCHESTRATION_ID	String	2352626	Tag Value	0	0	0	Trim Blanks	Leave As Is
BusinessUnitCode	4636	GO_DR_BUSINESS_UNIT_CODE	String	PENSION	Tag Value	0	0	0	Trim Blanks	Leave As Is
DocCode	4637	GO_DR_DOC_CODE	String	CW_SpousalWaiver	Tag Value	0	0	0	Trim Blanks	Leave As Is
Version	4638	GO_DR_VERSION	String	1	Tag Value	0	0	0	Trim Blanks	Leave As Is
DocSequence	4639	GO_DR_DOC_SEQUENCE	String	1	Tag Value	0	0	0	Trim Blanks	Leave As Is
BatchInd	760	GO_DR_BATCH_IND	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
ApplicationId	763	GO_MI_PCKGE_CDE	String	PTBENCNF	Tag Value	0	0	0	Keep Blanks	Leave As Is
UniversalId	753	GO_MI_BC_PIN_NBR	String	1234567	Tag Value	0	0	0	Trim Blanks	Leave As Is
DocumentRequestId	764	GO_DDA_DOC_REQ_ID	String	1234	Tag Value	0	0	0	Keep Blanks	Leave As Is
PrinterId	765	GO_MI_PRNTR_ID_CDE	String	TZUG	Tag Value	0	0	0	Trim Blanks	Leave As Is
Prefix	1147	GO_MI_PREFIX	String		Tag Value	0	0	0	Trim Blanks	Leave As Is
FirstName	1149	GO_MI_FIRST_NAME	String	John	Tag Value	0	0	0	Keep Blanks	Leave As Is
MiddleName	1148	GO_MI_MID_NAME	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
Suffix	1146	GO_MI_SUFFIX	String		Tag Value	0	0	0	Trim Blanks	Leave As Is
LastName	766	GO_MI_LAST_NAME	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
FullName	756	GO_MI_FULL_NAME	String	GO_MI_Mr. John Q. Participant	Tag Value	0	0	0	Keep Blanks	Leave As Is
AddressTypeCode	754	GO_MI_ADD_TYP_CDE	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
AddressLine	768	GO_MI_ADDRESS_LINE	String	GO_MI_ADDRESS_LINE	Tag Value	0	0	0	Trim Blanks	Leave As Is
EmailAddress	770	GO_MI_ALT_DLVRY_ADDR	String	Fax Number or Email Address	Tag Value	0	0	0	Trim Blanks	Leave As Is
LetterDate	771	GO_MI_LETTER_DATE	Date		Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
DeliveryType	775	GO_MI_PCKGE_DLVRY_TYP	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
EXPAGInd	776	GO_MI_PCKGE_IMAGE_IND	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
PortalDocDesc	1449	GO_MI_PORTAL_DOC_DESC	String		Tag Value	0	0	0	Trim Blanks	Leave As Is
ArchivalInd	1429	GO_MI_ARCHIVAL_IND	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
PlanId	1430	GO_MI_PLAN_ID	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
BusinessDate	1431	GO_MI_BUSINESS_DATE	Date	January 1, 2011	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
CcpPackageId	752	GO_MI_PST_RQST_ID	String	PST01672734	Tag Value	0	0	0	Trim Blanks	Leave As Is
AccountType	16067	FR_BAO_AccountType	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
AccountCategory	16068	FR_BAO_AccountCategory	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
ProposalNumber	16164	FR_ProposalNumber	String	346654	Tag Value	0	0	0	Keep Blanks	Leave As Is
LetterID	3565	FR_LetterType	String	CoverLetter	Tag Value	0	0	0	Keep Blanks	Leave As Is
FullName	14964	FR_AccountOwnerName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Prefix	14965	FR_AccountOwnerTitle	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
FirstName	14966	FR_AccountOwnerFirstName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
MiddleName	14967	FR_AccountOwnerMiddleName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
LastName	14968	FR_AccountOwnerLastName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Suffix	15014	FR_AccountOwnerSuffix	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
SSN	14969	FR_AccountOwnerSSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmailAddress	14970	FR_AccountOwnerEmail	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Gender	14971	FR_AccountOwnerGender	String	Male	Tag Value	0	0	0	Keep Blanks	Leave As Is
BirthDate	14972	FR_AccountOwnerBirthDate	Date	1967-08-13	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
MaritalStatus	14974	FR_AccountOwnerMaritalStatus	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
BusinessPhone	15478	FR_AccountOwnerBusinessPhone	String	(999)999-9999	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialStreetAddress	15373	FR_AccountOwnerResidentialStreetAddress	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialCity	15374	FR_AccountOwnerResidentialCity	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialState	15375	FR_AccountOwnerResidentialState	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialZipcode	15376	FR_AccountOwnerResidentialZipcode	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialCountry	15377	FR_AccountOwnerResidentialCountry	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
MailingStreetAddress	15378	FR_AccountOwnerMailingStreetAddress	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
MailingCity	15379	FR_AccountOwnerMailingCity	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
MailingState	15380	FR_AccountOwnerMailingState	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
MailingZipcode	15381	FR_AccountOwnerMailingZipcode	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
MailingCountry	15382	FR_AccountOwnerMailingCountry	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
StateOfResidence	14981	FR_AccountOwnerStateofResidence	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Citzenship	14980	FR_AccountOwnerCitizenship	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmploymentStatus	14982	FR_BAO_EmploymentStatus	String	Active	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerName	15442	FR_BAO_EmployerName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmploymentTitle	15444	FR_BAO_EmployerTitle	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
TradeCompanyCheckBox	14985	FR_AccountOwnerTradeCompanyCB	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
TIAACREFCheckBox	14986	FR_AccountOwnerTIAACREFCB	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
FirmCheckBox	14987	FR_AccountOwnerMemberFirmCB	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
RelationshipToEmployee	15469	FR_BAO_Firm_Relationship	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
NameOfEmployee	15468	FR_BAO_Firm_PersonName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
NameOfFirm	15470	FR_BAO_Firm_Name	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
SeniorMilitaryCheckBox	14988	FR_AccountOwnerSeniorMilitaryCB	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
InvestmentProfile	14989	FR_AccountOwnerInvestmentProfile	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
PhoneType	17913	FR_BAO_PhoneType	String	Home	Tag Value	0	0	0	Keep Blanks	Leave As Is
PhoneType	18531	FR_AccountOwner_PhoneType	String	Business	Tag Value	0	0	0	Keep Blanks	Leave As Is
GrantorInd	18770	FR_AccountOwner_GrantorType	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
BrokerageAccountNumber	15015	FR_AccountOwnerBrokerageAccountNumber	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryName	3283	FR_PrimaryBeneficiaryName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
SpousalWaiver	16645	FR_BAO_02_SpousalWaiver	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
DeliveryMethod	18450	FR_BAO_DeliveryMethod	String	E-Signature-Agent	Tag Value	0	0	0	Keep Blanks	Leave As Is
FormNumber	18540	FR_BAO_FormNumber	String	F11207	Tag Value	0	0	0	Keep Blanks	Leave As Is
NonIraOption	14990	FR_BAO_NonIraOption	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
NonIraOption	20318	BAO_NonIRAOption_2	String	Trust	Tag Value	0	0	0	Keep Blanks	Leave As Is
TrustAccountName	16134	FR_BAO_TrustAccountName	String	Herbert	Tag Value	0	0	0	Keep Blanks	Leave As Is
TrustSSN	16135	FR_BAO_TrustSSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
TrustEffectiveDate	16136	FR_BAO_TrustEffectiveDate	Date	2015-01-30-05:00	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
AmendmentDateOfTrust	20321	FR_BAO_AmendmentDateofTrust	Date	2015-01-30-05:00	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
TrustType	20322	FR_BAO_TrustType	String	Revocable	Tag Value	0	0	0	Keep Blanks	Leave As Is
InvestmentObjective	14991	FR_BAO_InvestmentObjective	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
AnnualIncome	14992	FR_BAO_AnnualIncome	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
NetWorth	14993	FR_BAO_NetWorth	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaxBraket	14994	FR_BAO_TaxBraket	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
SourceOfFunds	14995	FR_BAO_SourceOfFunds	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
SweepAccount	16176	FR_BAO_SweepSelectionSweepAccount	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
Cash	16177	FR_BAO_SweepSelectionCash	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
Liquid	21790	FR_BAO_Liquid	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Prime	16178	FR_BAO_SweepSelectionPrime	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
DreyfusGovCashManageServiceShares	21785	FR_BAO_DreyfusGovCashManageServiceShares	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
DreyfusGovSecCashManageInvestor	21786	FR_BAO_DreyfusGovSecCashManageInvestor	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
DreyfusGovCashManageInvester	21787	FR_BAO_DreyfusGovCashManageInvester	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
FederatedHermesGovObligationsCash	21788	FR_BAO_FederatedHermesGovObligationsCash	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
FederatedHermesTrustUSTreasuryOblicationsCash	21789	FR_BAO_FederatedHermesTrustUSTreasuryOblicationsCash	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Edelivery	21784	FR_BAO_Edelivery	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
Margin	15471	FR_BAO_Margin	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
DelegateToAppointedAgent	18568	FR_BAO_Trust_DelegateToAppointedAgent	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
DelegateToOutsideProfessional	18569	FR_BAO_Trust_DelegateToOutsideProfessional	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
OtherDescription	16133	FR_BAO_OtherDescription	String	test test test test	Tag Value	0	0	0	Keep Blanks	Leave As Is
SourceOfFundsOtherDescription	16142	FR_BAO_SourceOfFundsOtherDescription	String	Test Test Test Test	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentRelationship	16160	FR_BAO_DecendentRelationship	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
AccountNumber	16146	FR_BAO_InitialTransactionsAccountNumber	String	234567	Tag Value	0	0	0	Keep Blanks	Leave As Is
FullTransfer	16147	FR_BAO_InitialTransactionsFullTransfer	String	N	Tag Value	0	0	0	Keep Blanks	Leave As Is
DeliveringAccountClosed	16148	FR_BAO_InitialTransactionsDeliveringAccountClosed	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
PartialTransfer	16149	FR_BAO_InitialTransactionsPartialTransfer	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
TransferCashAmount	16150	FR_BAO_InitialTransactionsTransferCashAmount	String	20.0	Tag Value	0	0	0	Keep Blanks	Leave As Is
SharesDescription	16151	FR_BAO_InitialTransactionsSharesDescription	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
SharesQuantity	16152	FR_BAO_InitialTransactionsSharesQuantity	String	**********	Tag Value	0	0	0	Keep Blanks	Leave As Is
SignatoryName	930	GO_MI_EMPL_SGNTRY_NME	String	GO_MI_Empl_Sgntry_Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
SignatoryUnit	773	GO_MI_EMPL_UNIT_WORK_NME	String	Mail Item Work Unit Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
StateOfResidence	3558	FR_StateOfResidence	String	NY	Tag Value	0	0	0	Keep Blanks	Leave As Is
Citizenship	3559	FR_Citizenship	String	United States	Tag Value	0	0	0	Trim Blanks	Leave As Is
PhoneNumber	5953	FR_PhoneNumber	String	String	Tag Value	0	0	0	Trim Blanks	Leave As Is
PlanName	3562	FR_PlanName	String	XYZ Plan	Tag Value	0	0	0	Trim Blanks	Leave As Is
PlanId	3563	FR_PlanId	String	P12345	Tag Value	0	0	0	Trim Blanks	Leave As Is
FormNumber	3620	FR_FormNumber	String	FR_FormNumber	Tag Value	0	0	0	Trim Blanks	Leave As Is
InstitutionName	3726	FR_InstitutionName	String	FR_XYZ Institution	Tag Value	0	0	0	Trim Blanks	Leave As Is
RequestDate	3727	FR_RequestDate	Date	2012-08-06	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
MarriedIndicator	4580	FR_MarriedIndicator	String		Tag Value	0	0	0	Trim Blanks	Leave As Is
WithdrawalMethodType	3751	FR_WithdrawalMethodType	String	CD	Tag Value	0	0	0	Keep Blanks	Leave As Is
TotalWithdrawalAmount	3754	FR_TotalWithdrawalAmount	Currency	99,999,999.99	Tag Value	0	0	0	General Number	Leave As Is
SwatFrequency	4574	FR_SwatFrequency	String	Quarterly	Tag Value	0	0	0	Keep Blanks	Leave As Is
SwatNumberofPayments	4575	FR_SwatNumberofPayments	Integer	5	Tag Value	0	0	0	General Number	Leave As Is
SwatPaymentStopDate	4577	FR_SwatPaymentsStopDate	Date	2012-07-01	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
SwatNoFundsOrStopIndicator	4581	FR_SwatNoFundsOrStopIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
SwTransactionType	11692	FR_SwTransactionType	String	RMD	Tag Value	0	0	0	Trim Blanks	Leave As Is
SwDistributionType	11693	FR_SwDistributionType	String	SWAT	Tag Value	0	0	0	Trim Blanks	Leave As Is
AnnualizedRequestAmount	11694	FR_AnnualizedRequestAmount	Currency	11111.11	Tag Value	0	0	0	General Number	Leave As Is
SwRmd2in1OptionIndicator	11695	FR_SwRmd2in1OptionIndicator	String	Y	Tag Value	0	0	0	Trim Blanks	Leave As Is
SwRmd2in1RequiredIndicator	11696	FR_SwRmd2in1RequiredIndicator	String	Y	Tag Value	0	0	0	Trim Blanks	Leave As Is
SwRmd2in1EligibleIndicator	11697	FR_SwRmd2in1EligibleIndicator	String	Y	Tag Value	0	0	0	Trim Blanks	Leave As Is
TotalPercentageDollarValue	4443	FR_TotalPercentageDollarValue	Currency	$22,222.22	Tag Value	0	0	0	General Number	Leave As Is
SwSubPlanDistributionType	12025	FR_SwSubPlanDistributionType	String	SWAT	Tag Value	0	0	0	Keep Blanks	Leave As Is
TotalWithdrawalPercentage	3753	FR_TotalWithdrawalPercentage	Float	50.00	Tag Value	0	0	0	General Number	Leave As Is
TotalBalanceAmount	4579	FR_TotalBalanceAmount	Currency	$22,222.22	Tag Value	0	0	0	General Number	Leave As Is
FundNameArray	4466	LT_FundNameArray	String	Payment Duration Fund Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundTickerSymbolArray	6460	LT_FundTickerSymbolArray	String	#1111	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundWithdrawalPercentageArray	6401	LT_FundWithdrawalPercentageArray	Float	25	Tag Value	0	0	0	General Number	Leave As Is
FundWithdrawalAmountArray	6402	LT_FundWithdrawalAmountArray	Currency	********11111.11	Tag Value	0	0	0	General Number	Leave As Is
Name	3756	FR_PullForm_NonStaplingPortraitFileName	String		Tag Value	0	0	0	Trim Blanks	Leave As Is
Salutation	772	GO_MI_LTTR_SLTTN_TXT	String	GO MI Salutation Text	Tag Value	0	0	0	Trim Blanks	Leave As Is
ArchivalAck	1450	GO_MI_ARCHACK	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
EdelAck	1427	GO_MI_EDELACK	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
PrintAck	1451	GO_MI_PRINTACK	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
ExpAgContracts	954	GO_PI_CONTRACTS	String		Tag Value	0	0	0	Trim Blanks	Leave As Is
FundWithdrawalAmountArray	6402	LT_FundWithdrawalAmountArray	Currency	********11111.11	Tag Value	0	0	0	General Number	Leave As Is
RmdYearEndBalanceAdjustment	9171	LT_RmdYearEndBalanceAdjustment	Currency	666.66	Tag Value	0	0	0	General Number	Leave As Is
ExcludeGrandfatheredAmountIndicator	9172	LT_ExcludeGrandfatheredAmountIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundNameArray	4466	LT_FundNameArray	String	Payment Duration Fund Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundWithdrawalPercentageArray	6401	LT_FundWithdrawalPercentageArray	Float	25	Tag Value	0	0	0	General Number	Leave As Is
FundWithdrawalAmountArray	6402	LT_FundWithdrawalAmountArray	Currency	********11111.11	Tag Value	0	0	0	General Number	Leave As Is
FundRothTotalArray	9706	LT_RothTotalAmount	Currency	111,111,111.11	Tag Value	0	0	0	General Number	Leave As Is
InServiceType	8627	LT_InServiceType	String	InServiceType	Tag Value	0	0	0	Keep Blanks	Leave As Is
RecurringPaymentIndicator	4296	FR_RecurringPaymentIndicator	String	SWAT	Tag Value	0	0	0	Trim Blanks	Leave As Is
FaxNumber	2380	GO_MI_FAX_NUMBER	String	<EMAIL>	Tag Value	0	0	0	Trim Blanks	Leave As Is
ExportInd	822	GO_PI_EXPORT_IND	String	A	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaskId	828	GO_PI_TASK_ID	String	PI Task ID	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaskType	829	GO_PI_TASK_TYPE	String	PI Task Type	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaskGuid	823	GO_PI_TASK_GUID	String	PI Task Guid	Tag Value	0	0	0	Keep Blanks	Leave As Is
ActionStep	824	GO_PI_ACTION_STEP	String	PI Action Step	Tag Value	0	0	0	Keep Blanks	Leave As Is
DocContent	834	GO_PI_DOC_CONTENT	String	PI Doc Content	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaskStatus	827	GO_PI_TASK_STATUS	String	A	Tag Value	0	0	0	Keep Blanks	Leave As Is
PlanId	833	GO_PI_PLAN_ID	String	PI Plan ID	Tag Value	0	0	0	Keep Blanks	Leave As Is
TiaaDateTime	826	GO_PI_TIAA_TIME	String	11:22:33	Tag Value	0	0	0	Keep Blanks	Leave As Is
SSN	830	GO_PI_SSN	String	ABCDEFGHI	Tag Value	0	0	0	Keep Blanks	Leave As Is
UniversalId	831	GO_PI_PIN_NPIN_PPG	String	PI Pin Npin PPG	Tag Value	0	0	0	Keep Blanks	Leave As Is
UniversalType	832	GO_PI_PIN_TYPE	String	A	Tag Value	0	0	0	Keep Blanks	Leave As Is
DeclineReason	5343	LT_DeclineReasonText	String	Decline Reason Text	Tag Value	0	0	0	Keep Blanks	Leave As Is
RequestOptionType	4456	LT_RequestOptionType	String	S	Tag Value	0	0	0	Trim Blanks	Leave As Is
WithdrawalType	4457	LT_WithdrawalType	String	C	Tag Value	0	0	0	Trim Blanks	Leave As Is
F402fOption	4458	LT_F402fOption	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
RelativeValueDisclosureOption	4459	LT_RelativeValueDisclosureOption	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
NewEnrollmentIndicator	6381	LT_NewEnrollmentIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
LetterTransactionType	10551	LT_LetterTransactionType	String	Cash	Tag Value	0	0	0	Keep Blanks	Leave As Is
LetterDistributionType	10552	LT_LetterDistributionType	String	OneTime	Tag Value	0	0	0	Keep Blanks	Leave As Is
BinItem	774	GO_MI_BIN_ITEMS	String		Tag Value	0	0	0	Keep Blanks	Leave As Is
PlanName	4276	LT_PlanName	String	XYZ University	Tag Value	0	0	0	Trim Blanks	Leave As Is
Frequency	4628	LT_Frequency	String	Annual	Tag Value	0	0	0	Keep Blanks	Leave As Is
LastPaymentDate	4501	LT_LastPaymentDate	Date	2012-11-01	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
NetAmountofLastPayment	4502	LT_NetAmountOfLastPayment	Currency	3333.33	Tag Value	0	0	0	General Number	Leave As Is
PaymentStopDate	5955	LT_PaymentStopDate	Date	2012-09-13	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
OtpDeliveryMode	6467	LT_OtpDeliveryMode	String	M	Tag Value	0	0	0	Keep Blanks	Leave As Is
RecurringPhase	8349	LT_RecurringPhase	String	Maintenance	Tag Value	0	0	0	Trim Blanks	Leave As Is
FirstPaymentDate	4630	LT_FirstPaymentDate	Date	2012-09-01	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
NumberofPayments	6388	LT_NumberOfPayments	Integer	1234	Tag Value	0	0	0	General Number	Leave As Is
UntilFundsDepletedInd	8485	LT_UntilFundsDepletedInd	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
FinalPaymentDate	8403	LT_FinalPaymentDate	Date	2014-03-15	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
SubsequentPaymentsDay	5387	LT_SubsequentPaymentsDay	Integer	15	Tag Value	0	0	0	General Number	Leave As Is
ProductSubCode	6399	LT_ProductSubCode	String	PSub11	Tag Value	0	0	0	Keep Blanks	Leave As Is
RequestedPercentage	6469	LT_RequestedPercentage	Float	11.11	Tag Value	0	0	0	General Number	Leave As Is
TiaaIndexContractNumber	6206	LT_TiaaIndexContractNumber	String	T_INDEX-2	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundWithdrawalPercentageArray	6401	LT_FundWithdrawalPercentageArray	Float	25	Tag Value	0	0	0	General Number	Leave As Is
RmdPlanTotal	6406	LT_RmdPlanTotal	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
OptionalWithholdingAmount	6412	LT_OptionalWithholdingAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxMaritalStatus	6415	LT_TaxMaritalStatus	String	HH	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaxExemptIndicator	6416	LT_TaxExemptIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
NumberofExemptions	6417	LT_NumberofExemptions	Integer	1234	Tag Value	0	0	0	General Number	Leave As Is
TaxFlatDollarAmount	6418	LT_TaxFlatDollarAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFixedPercentage	6419	LT_TaxFixedPercentage	Float	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxWithholdingAmount	6428	LT_TaxWithholdingAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFixedDollarAmount	6422	LT_TaxFixedDollarAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
OptionalWithholdingAmount	6412	LT_OptionalWithholdingAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxMaritalStatus	6415	LT_TaxMaritalStatus	String	HH	Tag Value	0	0	0	Keep Blanks	Leave As Is
NumberofExemptions	6417	LT_NumberofExemptions	Integer	1234	Tag Value	0	0	0	General Number	Leave As Is
TaxFlatDollarAmount	6418	LT_TaxFlatDollarAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFixedPercentage	6419	LT_TaxFixedPercentage	Float	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxWithholdingAmount	6428	LT_TaxWithholdingAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFixedDollarAmount	6422	LT_TaxFixedDollarAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
RmdCalculationMethod	6434	LT_RmdCalculationMethod	String	Uniform	Tag Value	0	0	0	Keep Blanks	Leave As Is
SpouseDateofBirth	6435	LT_SpouseDateofBirth	Date	1980-08-13	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
UniformLifeExpectancyFactor	6470	LT_UniformLifeExpectancyFactor	Float	11.11	Tag Value	0	0	0	General Number	Leave As Is
JointLifeExpectancyFactor	6471	LT_JointLifeExpectancyFactor	Float	22.22	Tag Value	0	0	0	General Number	Leave As Is
RmdTotalWithdrawalPayments	6438	LT_RmdTotalWithdrawalPayments	Currency	121212.12	Tag Value	0	0	0	General Number	Leave As Is
RmdPriorYearGrandfatheredAmount	6439	LT_RmdPriorYearGrandfatheredAmount	Currency	************.34	Tag Value	0	0	0	General Number	Leave As Is
RmdPriorYearEndAccumulation	6440	LT_RmdPriorYearEndAccumulation	Currency	**********.56	Tag Value	0	0	0	General Number	Leave As Is
GfAccumulationEligibilityIndicator	10553	LT_GfAccumulationEligibilityIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankName	3175	LT_BankName	String	Bank Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankTransitNumber	3174	LT_BankTransitNumber	String	Transit Number	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankAccountNumber	3173	LT_BankAccountNumber	String	Bank Account Number	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankRoutingNumber	6442	LT_BankRoutingNumber	String	BRN-********	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankAccountType	4635	LT_BankAccountType	String	C	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExternalRolloverPlanType	6443	LT_ExternalRolloverPlanType	String	ExtRollType	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExternalCompanyName	3181	LT_ExternalCompanyName	String	External Company Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExternalCompanyAddressLine1	6444	LT_ExternalCompanyAddressLine1	String	External Company Address Line 1	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExternalCompanyAddressLine2	6445	LT_ExternalCompanyAddressLine2	String	External Company Address Line 2	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExternalCompanyAccountNumber	4282	LT_ExternalCompanyAccountNumber	String	Ext. Co. Acct. 123	Tag Value	0	0	0	Keep Blanks	Leave As Is
City	6446	LT_City	String	New York City	Tag Value	0	0	0	Keep Blanks	Leave As Is
State	6447	LT_State	String	New York	Tag Value	0	0	0	Keep Blanks	Leave As Is
ZipCode	6448	LT_ZipCode	String	10017-1234	Tag Value	0	0	0	Keep Blanks	Leave As Is
RolloverType	6455	LT_RolloverType	String	Internal	Tag Value	0	0	0	Keep Blanks	Leave As Is
NewEnrollmentInRollover	6456	LT_NewEnrollmentInRollover	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
RolledOverPlanName	6457	LT_RolledOverPlanName	String	Rolled Over Plan Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
RolledOverTiaaContractNumber	6458	LT_RolledOverTiaaContractNumber	String	RO111111-1	Tag Value	0	0	0	Keep Blanks	Leave As Is
InvestmentOption	6459	LT_InvestmentOption	String	C	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundNameArray	4466	LT_FundNameArray	String	Payment Duration Fund Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundTickerSymbolArray	6460	LT_FundTickerSymbolArray	String	#1111	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundWithdrawalPercentageArray	6401	LT_FundWithdrawalPercentageArray	Float	25	Tag Value	0	0	0	General Number	Leave As Is
CarrierName	6474	LT_CarrierName	String	Mr. Carrier Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
AddressLine	6475	LT_CarrierAddressLines	String	Carrier Address Line 1	Tag Value	0	0	0	Keep Blanks	Leave As Is
OtherAccountName	6461	LT_OtherAccountName	String	Other Account Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
OtherAccountNumber	6462	LT_OtherAccountNumber	String	OANbr-********	Tag Value	0	0	0	Keep Blanks	Leave As Is
OtherAccountType	6464	LT_OtherAccountType	String	OAType	Tag Value	0	0	0	Keep Blanks	Leave As Is
IvcOption	7945	LT_IvcOption	String	I	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerName	6373	LT_EmployerName	String	Employer Name in Personal Info Section	Tag Value	0	0	0	Trim Blanks	Leave As Is
LetterType	3565	FR_LetterType	String	CoverLetter	Tag Value	0	0	0	Trim Blanks	Leave As Is
SpousalWaiverOption	4460	LT_SpousalWaiverOption	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
SponsorIndicator	4548	LT_SponsorIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
MarriedUnmarriedIndicator	4546	LT_MarriedUnmarriedIndicator	String	M	Tag Value	0	0	0	Trim Blanks	Leave As Is
RecurringPaymentIndicator	5047	LT_RecurringPaymentIndicator	String	SWAT	Tag Value	0	0	0	Keep Blanks	Leave As Is
MaritalStatusValidation	8626	LT_MaritalStatusValidation	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
PrismSlaInd	10083	LT_PrismSlaInd	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
PrismSponsorApprovalDays	10084	LT_PrismSponsorApprovalDays	Integer	5	Tag Value	0	0	0	General Number	Leave As Is
PrismSponsorApprovalExtDays	10085	LT_PrismSponsorApprovalExtDays	Integer	10	Tag Value	0	0	0	General Number	Leave As Is
TransactionType	6371	LT_TransactionType	String	Cash	Tag Value	0	0	0	Trim Blanks	Leave As Is
DistributionType	6372	LT_DistributionType	String	OneTime	Tag Value	0	0	0	Trim Blanks	Leave As Is
GrossAmount	6374	LT_GrossAmount	Currency	********11111.11	Tag Value	0	0	0	General Number	Leave As Is
Fees	6376	LT_Fees	Currency	2222222222222.22	Tag Value	0	0	0	General Number	Leave As Is
EstimatedTaxes	6377	LT_EstimatedTaxes	Currency	********33333.33	Tag Value	0	0	0	General Number	Leave As Is
NetWithdrawal	6378	LT_NetWithdrawal	Currency	4444444444444.44	Tag Value	0	0	0	General Number	Leave As Is
DataValidationRequiredIndicator	6382	LT_DataValidationRequiredIndicator	String	Y	Tag Value	0	0	0	Trim Blanks	Leave As Is
PendingOtpIndicator	6383	LT_PendingOtpIndicator	String	N	Tag Value	0	0	0	Trim Blanks	Leave As Is
MailingAddressIndicator	6384	LT_MailingAddressIndicator	String	N	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmailAddressIndicator	6385	LT_EmailAddressIndicator	String	N	Tag Value	0	0	0	Keep Blanks	Leave As Is
EdeliveryPreferenceIndicator	9170	LT_EdeliveryPreferenceIndicator	String	Y	Tag Value	0	0	0	Trim Blanks	Leave As Is
EffectiveDateofDistribution	6397	LT_EffectiveDateofDistribution	Date	2013-10-01	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
AnnualizedRequestAmount	6424	LT_AnnualizedRequestAmount	Currency	********.11	Tag Value	0	0	0	General Number	Leave As Is
FrequencyPaymentAmount	6425	LT_FrequencyPaymentAmount	Currency	2222.222	Tag Value	0	0	0	General Number	Leave As Is
ProductCode	6398	LT_ProductCode	String	PC111	Tag Value	0	0	0	Keep Blanks	Leave As Is
RequestedAmount	6468	LT_RequestedAmount	Currency	$11111.11	Tag Value	0	0	0	General Number	Leave As Is
TiaaNumber	4461	LT_TiaaNumber	String	T11111-1	Tag Value	0	0	0	Keep Blanks	Leave As Is
IraIndexIndicator	6207	LT_IraIndexIndicator	String	B	Tag Value	0	0	0	Keep Blanks	Leave As Is
CrefNumber	4462	LT_CrefNumber	String	C11111-1	Tag Value	0	0	0	Keep Blanks	Leave As Is
SettlementSelectionType	6400	LT_SettlementSelectionType	String	Maximum	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundNameArray	4466	LT_FundNameArray	String	Payment Duration Fund Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
FundWithdrawalAmountArray	6402	LT_FundWithdrawalAmountArray	Currency	********11111.11	Tag Value	0	0	0	General Number	Leave As Is
AfterTaxAmount	6403	LT_AfterTaxAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxableAmount	6405	LT_TaxableAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TotalAmountRequested	6407	LT_TotalAmountRequested	Currency	*********.99	Tag Value	0	0	0	General Number	Leave As Is
TaxAnnualizedAmount	6409	LT_TaxAnnualizedAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFrequencyAmount	7969	LT_TaxFrequencyAmount	Currency	$123,456.78	Tag Value	0	0	0	General Number	Leave As Is
OmniTaxAmount	6411	LT_OmniTaxAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
OptionalWithholdingPercentage	6413	LT_OptionalWithholdingPercentage	Float	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFrequencyAmountFedAndState	8095	LT_TaxFrequencyAmountFedAndState	Currency	12345678.90	Tag Value	0	0	0	General Number	Leave As Is
TaxOptOutIndicator	6414	LT_TaxOptOutIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
TotalTaxWithheldAmount	6423	LT_TotalTaxWithheldAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TotalTaxFrequencyAmount	6433	LT_TotalTaxFrequencyAmount	Currency	$*********1.12	Tag Value	0	0	0	General Number	Leave As Is
TaxAnnualizedAmount	6409	LT_TaxAnnualizedAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFrequencyAmount	7969	LT_TaxFrequencyAmount	Currency	$123,456.78	Tag Value	0	0	0	General Number	Leave As Is
OmniTaxAmount	6411	LT_OmniTaxAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
OptionalWithholdingPercentage	6413	LT_OptionalWithholdingPercentage	Float	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TaxFrequencyAmountFedAndState	8095	LT_TaxFrequencyAmountFedAndState	Currency	12345678.90	Tag Value	0	0	0	General Number	Leave As Is
TaxOptOutIndicator	6414	LT_TaxOptOutIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
TaxExemptIndicator	6416	LT_TaxExemptIndicator	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
TotalTaxWithheldAmount	6423	LT_TotalTaxWithheldAmount	Currency	*********1234.12	Tag Value	0	0	0	General Number	Leave As Is
TotalTaxFrequencyAmount	6433	LT_TotalTaxFrequencyAmount	Currency	$*********1.12	Tag Value	0	0	0	General Number	Leave As Is
PaymentMethod	6441	LT_PaymentMethod	String	EFT	Tag Value	0	0	0	Keep Blanks	Leave As Is
AddressLine	3191	LT_SendAddress	String	Send Address Line	Tag Value	0	0	0	Keep Blanks	Leave As Is
IncludeIvcAmount	7518	LT_IncludeIvcAmount	String	N	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExcludedIvcAmount	8001	LT_ExcludedIvcAmount	Currency	111111.11	Tag Value	0	0	0	General Number	Leave As Is
PaymentMethod	6441	LT_PaymentMethod	String	EFT	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankName	3175	LT_BankName	String	Bank Name	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankTransitNumber	3174	LT_BankTransitNumber	String	Transit Number	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankAccountNumber	3173	LT_BankAccountNumber	String	Bank Account Number	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankRoutingNumber	6442	LT_BankRoutingNumber	String	BRN-********	Tag Value	0	0	0	Keep Blanks	Leave As Is
BankAccountType	4635	LT_BankAccountType	String	C	Tag Value	0	0	0	Keep Blanks	Leave As Is
AddressLine	3191	LT_SendAddress	String	Send Address Line	Tag Value	0	0	0	Keep Blanks	Leave As Is
IvcOption	7945	LT_IvcOption	String	I	Tag Value	0	0	0	Keep Blanks	Leave As Is
IncludeIvcAmount	7518	LT_IncludeIvcAmount	String	N	Tag Value	0	0	0	Keep Blanks	Leave As Is
ExcludedIvcAmount	8001	LT_ExcludedIvcAmount	Currency	111111.11	Tag Value	0	0	0	General Number	Leave As Is
TiaaNumber	3560	FR_TiaaNumber	String	T11111-1	Tag Value	0	0	0	Keep Blanks	Leave As Is
CrefNumber	3561	FR_CrefNumber	String	C22222-2	Tag Value	0	0	0	Trim Blanks	Leave As Is
SubplanId	3564	FR_SubplanId	String	S12345	Tag Value	0	0	0	Trim Blanks	Leave As Is
RothHeaderInd	9705	LT_RothHeaderInd	String	- SUMMARY	Tag Value	0	0	0	Trim Blanks	Leave As Is
RothTotalAmount	9706	LT_RothTotalAmount	Currency	111,111,111.11	Tag Value	0	0	0	General Number	Leave As Is
AddressLine	14975	FR_AccountOwnerAddressLines	String	Address	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerTaxID	16153	FR_BAO_EmployerTaxID	String	2131415	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerCity	15440	FR_BAO_EmployerCity	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerZipcode	15445	FR_BAO_EmployerZipcode	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
NameOfPerson	15466	FR_BAO_Trade_PersonName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
CompanyNameSymbol	15467	FR_BAO_Trade_CompanyNameSymbol	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
FullName	18699	FR_GrantorName	String	6666MR TEST B ACCOUNT	Tag Value	0	0	0	Keep Blanks	Leave As Is
Prefix	18700	FR_Grantor_Prefix	String	MR	Tag Value	0	0	0	Keep Blanks	Leave As Is
FirstName	18701	FR_Grantor_FirstName	String	6TEST	Tag Value	0	0	0	Keep Blanks	Leave As Is
MiddleName	18702	FR_Grantor_Middlename	String	B	Tag Value	0	0	0	Keep Blanks	Leave As Is
LastName	18703	FR_Grantor_LastName	String	ACCOUNT	Tag Value	0	0	0	Keep Blanks	Leave As Is
SSN	18704	FR_Grantor_SSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmailAddress	18705	FR_Grantor_Email	String	<EMAIL>	Tag Value	0	0	0	Keep Blanks	Leave As Is
Gender	18706	FR_Grantor_Gender	String	Female	Tag Value	0	0	0	Keep Blanks	Leave As Is
BirthDate	18707	FR_Grantor_BirthDate	Date	6666-06-21	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
MaritalStatus	18708	FR_Grantor_MaritalStatus	String	Mard	Tag Value	0	0	0	Keep Blanks	Leave As Is
BusinessPhone	18709	FR_Grantor_BusinessPhone	String	**********	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialStreetAddress	18710	FR_Grantor_ResAddress	String	********* KNOXWOOD DR	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialCity	18711	FR_Grantor_ResCity	String	6666HUNTERSVILLE	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialState	18712	FR_Grantor_ResState	String	NC	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialZipcode	18713	FR_Grantor_ResZip	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialCountry	18714	FR_Grantor_ResCountry	String	US	Tag Value	0	0	0	Keep Blanks	Leave As Is
FullName	18715	FR_JointGrantor_FullName	String	6666MR TEST B ACCOUNT	Tag Value	0	0	0	Keep Blanks	Leave As Is
Prefix	18716	FR_JointGrantor_Prefix	String	MR	Tag Value	0	0	0	Keep Blanks	Leave As Is
FirstName	18717	FR_JointGrantor_FirstName	String	6TEST	Tag Value	0	0	0	Keep Blanks	Leave As Is
MiddleName	18718	FR_JointGrantor_MiddleName	String	B	Tag Value	0	0	0	Keep Blanks	Leave As Is
LastName	18719	FR_JointGrantor_LastName	String	ACCOUNT	Tag Value	0	0	0	Keep Blanks	Leave As Is
SSN	18720	FR_JointGrantor_SSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmailAddress	18721	FR_JointGrantor_Email	String	<EMAIL>	Tag Value	0	0	0	Keep Blanks	Leave As Is
Gender	18722	FR_JointGrantor_Gender	String	Female	Tag Value	0	0	0	Keep Blanks	Leave As Is
BirthDate	18723	FR_JointGrantor_Birthdate	Date	6666-06-21	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
MaritalStatus	18724	FR_JointGrantor_MaritalStatus	String	Mard	Tag Value	0	0	0	Keep Blanks	Leave As Is
BusinessPhone	18725	FR_JointGrantor_BusinessPhone	String	**********	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialStreetAddress	18726	FR_JointGrantor_ResStreetAddress	String	********* KNOXWOOD DR	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialCity	18727	FR_JointGrantor_ResCity	String	6666HUNTERSVILLE	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialState	18728	FR_JointGrantor_ResState	String	NC	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialZipcode	18729	FR_JointGrantor_ResZip	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
ResidentialCountry	18730	FR_JointGrantor_ResCountry	String	US	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryType	18696	FR_PrimaryBeneficiary_BeneficiaryType	String	Individual	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryLDPS	16131	FR_PrimaryBeneficiaryLDPS	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryType	18697	FR_ContingentBeneficiary_BeneficiaryType	String	Trust	Tag Value	0	0	0	Keep Blanks	Leave As Is
TrustAmmendmentDate	18771	FR_BAO_Trust_AmmendmentDate	Date	2016-01-01	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
TypeOfTrust	18570	FR_BAO_TypeOfTrust	String	Irrevocable	Tag Value	0	0	0	Keep Blanks	Leave As Is
JointTenantAccountType	18883	FR_JointTenantAccount	String	RightsOfSurvivorship	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerStreetAddress	15439	FR_BAO_EmployerAddress	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerState	15443	FR_BAO_EmployerState	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
EmployerCountry	15441	FR_BAO_EmployerCountry	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
RelationshipToEmployee	15481	FR_BAO_TIAACREF_Relationship	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
NameOfEmployee	15482	FR_BAO_TIAACREF_PersonName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryPercentage	3284	FR_PrimaryBeneficiaryPercentage	String	0	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiarySSN	3285	FR_PrimaryBeneficiarySSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryRelationship	3287	FR_PrimaryBeneficiaryRelationship	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryDateOfBirth	3286	FR_PrimaryBeneficiaryBirthDate	Date	1967-08-13	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
BeneficiaryGender	3437	FR_PrimaryBeneficiaryGender	String	Male	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryStreetAddress	18466	FR_PrimaryBeneficiary_StreetAddress	String	1733 sweet street	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryCity	18467	FR_PrimaryBeneficiary_City	String	charlotte	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryState	18468	FR_PrimaryBeneficiary_State	String	MA	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryZipcode	18469	FR_PrimaryBeneficiary_zipcode	String	28162	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryContactPhoneNumber	18486	FR_PrimaryBeneficiary_PhoneNumber	String	6897645432	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryName	3288	FR_ContingentBeneficiaryName	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryPercentage	3289	FR_ContingentBeneficiaryPercentage	String	0	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiarySSN	3290	FR_ContingentBeneficiarySSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryRelationship	3292	FR_ContingentBeneficiaryRelationship	String	String	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryDateOfBirth	3291	FR_ContingentBeneficiaryBirthDate	Date	1967-08-13	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
BeneficiaryGender	3442	FR_ContingentBeneficiaryGender	String	Male	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryStreetAddress	18470	FR_ContingentBeneficiary_StreetAddress	String	173 candler street	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryCity	18471	FR_ContingentBeneficiary_City	String	atlanta	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryState	18472	FR_ContingentBeneficiary_State	String	CO	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryZipcode	18473	FR_ContingentBeneficiary_zipcode	String	55562	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryContactPhoneNumber	8380	FR_ContingentBeneficiaryContactPhoneNumber	String	(999)999-9999	Tag Value	0	0	0	Keep Blanks	Leave As Is
Category	18614	FR_ContingentBeneficiary_Category	String	A	Tag Value	0	0	0	Keep Blanks	Leave As Is
BeneficiaryLDPS	16132	FR_ContingentBeneficiaryLDPS	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentMiddleName	16155	FR_BAO_DecendentMiddleName	String	Middlename	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentSSN	16157	FR_BAO_DecendentSSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
IraOption	15016	FR_BAO_IraOption	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
IraOption	20317	BAO_IRAOption_2	String	Inherited Roth IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentPlanType	18563	FR_BAO_Decendent_PlanType	String	Traditional IRA	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentFirstName	16154	FR_BAO_DecendentFirstName	String	Firstname	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentLastName	16156	FR_BAO_DecendentLastName	String	Lastname	Tag Value	0	0	0	Keep Blanks	Leave As Is
DecendentBirthDate	16158	FR_BAO_DecendentBirthDate	Date	1960-11-11	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
DecendentDeathDate	16159	FR_BAO_DecendentDeathDate	Date	1990-11-11	Tag Value	0	0	0	yyyy-mm-dd (2001-04-06)	Leave As Is
TransferFunds	16145	FR_BAO_InitialTransactionsTransferFunds	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
VoteProxy	16642	FR_BAO_VoteProxy	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
ConfirmSuppressionStatus	18449	FR_BAO_ConfirmSuppressionStatus_Ind	String	Y	Tag Value	0	0	0	Keep Blanks	Leave As Is
BatchTotal	762	GO_DR_BATCH_TOTAL	Integer		Tag Value	0	0	0	General Number	Leave As Is
SSN	767	GO_MI_SSN	String	*********	Tag Value	0	0	0	Keep Blanks	Leave As Is
ReturnDocumentType	1426	GO_MI_RETURN_DOC_TYPE	String	D	Tag Value	0	0	0	Trim Blanks	Leave As Is
ListofForms	14935	BAO_ListofForms	String	TIAA-CREF Borkerage Services - Account Application	Tag Value	0	0	0	Keep Blanks	Leave As Is
Extension	16137	FR_AccountOwnerExtension	String	x2888	Tag Value	0	0	0	Keep Blanks	Leave As Is
HomePhone	14973	FR_AccountOwnerHomePhone	String	(999)999-9999	Tag Value	0	0	0	Keep Blanks	Leave As Is
HomeExtension	16165	FR_AccountOwnerHomeExtension	String	ex. 2	Tag Value	0	0	0	Keep Blanks	Leave As Is
SourceOfIncome	14983	FR_BAO_SourceOfIncome	String	Active	Tag Value	0	0	0	Keep Blanks	Leave As Is
