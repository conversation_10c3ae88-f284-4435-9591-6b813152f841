<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v5 rel. 3 U (http://www.xmlspy.com) by TIAA-CREF (TIAA-CREF) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xmime="http://ccp.tiaa.org/communications-rs-v1/communication/xmlmime/types" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xsd:annotation>
		<xsd:documentation xml:lang="en-US">
			This schema will contain all the common elements like MailItemInfo and ExpAgWorkflowInfo for all the Documents Requests. The source application specific schema's will extend these common Elements.
			<!--
		      If the data contains ", then the calling application must send &quot;
			  If the data contains &, then the calling application must send &amp;
			  If the data contains ', then the calling application must send &apos;
	        -->
		</xsd:documentation>
	</xsd:annotation>
	<!-- Includes -->
	<xsd:include schemaLocation="ccp-document-request-datatypes-v1.xsd"/>
	<!-- Extend DocumentRequestInfo to provide the basic application specific information for a document request-->
	<xsd:complexType name="DocumentRequestInfo">
		<xsd:sequence>
			<!-- Request User Id, in batch is the batch job name and in individual request, it will be a user id -->
			<xsd:element name="RequestUserId" type="String15" minOccurs="0"/>
			<!-- Request User Name, in a batch can be the Application Name (RosterMapping) and in individual request, it will be a user name -->
			<xsd:element name="RequestUserName" type="String50" minOccurs="0"/>
			<!-- RequestDateTime Format: 2011-04-01T12:50:09 -->
			<xsd:element name="RequestDateTime" type="xsd:dateTime" minOccurs="0"/>
			<!-- CompositeOrchestrationId-->
			<xsd:element name="CompositeOrchestrationId" type="String35" minOccurs="0"/>
			<!-- OrchestrationId, the id originated in BPEL -->
			<xsd:element name="OrchestrationId" type="String20" minOccurs="0"/>
			<!-- TransactionReqID, Composite Request ID -->
			<xsd:element name="TransactionReqID" type="String35" minOccurs="0"/>
			<xsd:element name="BusinessUnitCode" type="String35" minOccurs="0"/>
			<xsd:element name="DocCode" type="String35" minOccurs="0"/>
			<xsd:element name="Version" type="String6" minOccurs="0"/>
			<xsd:element name="DocSequence" type="Int4" minOccurs="0"/>
			<!-- The batch indicator can be 'Y' or 'N', based on the request. If it is single online/web requests, then the batchInd='N' else if it is a batch request then set batchInd='Y'. If the batchInd='Y', then set the values for the BatchTotal and BatchCounter. BatchTotal will be the total number of requests in the batch and BatchCounter will be the incremental counter. Maximum BatchCounter number will be equal to the BatchTotal -->
			<xsd:element name="BatchInd" type="YesOrNoOption"/>
			<!-- BatchCounter will be the incremental counter -->
			<xsd:element name="BatchCounter" type="xsd:long" minOccurs="0"/>
			<!-- BatchTotal will be the total number of requests in the batch -->
			<xsd:element name="BatchTotal" type="xsd:long" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- MailItemRequestInfo provides MailItemInfo, which also extends the DocumentRequestInfo (i.e. application specific information) -->
	<xsd:complexType name="MailItemRequestInfo">
		<xsd:complexContent>
			<xsd:extension base="DocumentRequestInfo">
				<xsd:sequence>
					<!-- Requests of type MailItemInfo -->
					<xsd:element ref="MailItemInfo"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- MailItemInfo is a  mandatory and all the calling applications should provide this information to DCS to process their request. This element will contain recipient name, address, barcode, request id, pin number, package code etc. -->
	<xsd:element name="MailItemInfo" type="MailItemInfo"/>
	<xsd:complexType name="MailItemInfo">
		<xsd:sequence>
			<!-- Application Id (Package Code) will be provided by DCS team during development. It identifies the specific document being requested to be created, e.g. PTRKICA, etc.-->
			<xsd:element name="ApplicationId" type="ApplicationId"/>
			<!-- Universal ID can be one of the following: PIN, NPIN, or Institution ID/PPG. This is identified by the Universal Type below. -->
			<xsd:element name="UniversalId" type="UniversalId"/>
			<!-- Universal Type specifies the type of the value in the Universal ID.  P=PIN, N=NPIN, I=Institution -->
			<xsd:element name="UniversalType" type="UniversalTypeCodes"/>
			<!-- Document Request ID is a unique ID created by the calling application for each document
				 request equivalent to the POST PST #.  The format will be
				 xxxzzzYYYYMMDDhhmmssUUUUUUUUUUUUU9999999 where:
				 xxx is a 3-character system ID that  DCS will assign;
				 zzz is a 3-digit sub-system ID that  DCS will assign;
				 YYYY is the 4-digit current year;
				 MM is the 2-digit current month;
				 DD is the 2-digit current day;
				 hh is the 2-digit current hour;
				 mm is the 2-digit current minute;
				 ss is the 2-digit current second;
				 UUUUUUUUUUUUU is the 13-digit for universal id;
				 9999999 is the 7-digit counter, this is an incremental counter within a batch;  e.g. 001001201003251658000000001 -->
			<xsd:element name="DocumentRequestId" type="DocumentRequestId"/>
			<xsd:element name="EventIdentifier" type="String50" minOccurs="0"/>
			<!-- Printer ID for local print request. -->
			<xsd:element name="PrinterId" type="PrinterId" minOccurs="0"/>
			<!-- The Full name is nothing but the combination of (prefix + firstname + middlename + lastname + suffix), can accept maximum 185 characters and accept alphabets, dot(.), and apostrophe (') only -->
			<xsd:element name="FullName" type="FullName" minOccurs="0"/>
			<!-- Prefix is nothing but Dr. or Prof. etc, which can be maximum 20 characters and can accept alphabets and dot(.) -->
			<xsd:element name="Prefix" type="PrefixSuffix" minOccurs="0"/>
			<!-- First Name, can be maximum 50 characters and can accept alphabets and apostrophe (') only -->
			<xsd:element name="FirstName" type="UserName" minOccurs="0"/>
			<!-- Middle Name, can be maximum 50 characters and can accept alphabets and apostrophe (') only  -->
			<xsd:element name="MiddleName" type="UserName" minOccurs="0"/>
			<!-- Last Name, can be maximum 50 characters and can accept alphabets and apostrophe (') only. -->
			<xsd:element name="LastName" type="UserName" minOccurs="0"/>
			<!-- Suffix can be Sr. or Jr. etc., which can be maximum 20 characters and can accept alphabets and dot(.) -->
			<xsd:element name="Suffix" type="PrefixSuffix" minOccurs="0"/>
			<!-- Social Security No (SSN) or Tax Identification No (TIN), should be 9 digit numbers (XXX-XX-XXXX) and can accept numbers only (0-9). -->
			<xsd:element name="SSN" type="SSN" minOccurs="0"/>
			<!-- Address type Code,  will accept one of the enumeration type of AddressTypeCodes (U=US, F=Foreign, C=Canada or B=Bad). -->
			<xsd:element name="AddressTypeCode" type="AddressTypeCodes"  minOccurs="0"/>
			<!-- Address Lines,  will give option to provide maximum six lines of information -->
			<xsd:element name="AddressLines" type="AddressLines" minOccurs="0"/>
			<!-- PostNet BarCode (i.e.; is the old US Postal Barcode) or Intelligent Mail BarCode (i.e.; is the new US Postal Barcode) for Pre-sorting -->
			<xsd:element name="PostalBarCode" type="String50" minOccurs="0"/>
			<!-- Fax Number, should be 9 digit numbers (XXX-XXX-XXXX) and can accept numbers only (0-9) -->
			<xsd:element name="FaxNumber" type="FaxNumber" minOccurs="0"/>
			<!-- Email Address, should be 50 digits and will be in <NAME_EMAIL> -->
			<xsd:element name="EmailAddress" type="String100" minOccurs="0"/>
			<!-- Letter Date is the date passed by the calling application, based on business requirements, Format: 2011-04-04 -->
			<xsd:element name="LetterDate" type="xsd:date" minOccurs="1"/>
			<!-- Salutation, "Dear xxxxx" -->
			<xsd:element name="Salutation" type="String50" minOccurs="0"/>
			<!-- Name of Authorized Signatory -->
			<xsd:element name="SignatoryName" type="UserName" minOccurs="0"/>
			<!-- Unit of Authorized Signatory -->
			<xsd:element name="SignatoryUnit" type="String50" minOccurs="0"/>
			<!-- List of Bin Items, up to 10, selected publications/booklets to be included with the document -->
			<xsd:element name="BinItems" type="BinItems" minOccurs="0"/>
			<!-- List of MI Other Items, will be a combination of key/value pairs -->
			<xsd:element name="OtherItems" type="OtherItems" minOccurs="0"/>
			<!--
				Print Delivery Type specifies the type of the print.S=STANDARD PRINT.
                This tag is should not be used if your application is new to DCS.
                Only available for use by IPAC and Roster Mapping and Annuity Contribution Statements.
                Use the other choice element, Delivery Type which offers more distribution channels
			-->
			<!--
				Delivery Type specifies the type of delivery. P=LOCAL PRINT,
				S=STANDARD PRINT, O=OVERNIGHT DELIVERY, R=RETURN TO UNIT (Pullouts),
				F=FAX, L=Local Printer, E=EDelivery, C=Return document to Client If
				the DeliveryType = 'P', then there should be a value in Printer ID;
				If the DeliveryType = 'S' or 'O' or 'R', then the request is for
				Vendor Print and there should be output channel set in APPLICATION
				table. If the DeliveryType = 'E', then the request is for eDelivery
				and there should be output channel set in APPLICATION table. DCS
				expects values for prefix, suffix, first name, middle name, last
				name and email address in MailItemInfo. If the DeliveryType = 'F',
				then there should be a value in Fax Number
			-->
			<xsd:choice>
				<xsd:element name="PrintDeliveryType" type="PrintDeliveryTypes"/>
				<xsd:element name="DeliveryType" type="DeliveryTypes"/>
			</xsd:choice>
			<!-- EXP AG Indicator, can be 'Yes' or 'No' based on the request type. If the EXPAGInd = 'Y', then the request is for EXP AG and there should be output channel set in APPLICATION table. For all EXP AG requests, the calling applications must pass the ExpAgWorkflowInfo -->
			<xsd:element name="EXPAGInd" type="YesOrNoOption" minOccurs="0"/>
			<!--
				Return document type can be PDF or TIFF. ReturnDocType indicates the
				format that the calling application wants to get the document back.
				This field is defaulted to PDF.
			-->
			<xsd:element name="ReturnDocumentType" type="ReturnDocumentType" default="PDF" minOccurs="0"/>			
			<!--
				Portal_Doc_Desc field will carry request-level description of the
				PDF document to be displayed on the web portal. Default value will be
				the Application_Name from the Application table.
			-->
			<xsd:element name="PortalDocDesc" type="PortalDocDesc" minOccurs="0"/>
			<!--
				EmailDocDesc field will carry request-level description of the
				Email document to be displayed on the web portal. 
			-->
			<xsd:element name="EmailDocDesc" type="EmailDocDesc" minOccurs="0"/>
			<!--
				Archival_Ind = M requires the document to be archived in Mobius.
				Default value will be N.
			-->
			<xsd:element name="ArchivalInd" type="ArchivalIndicator" minOccurs="0"/>
			<!-- Plan ID is a required field for Archival into Mobius, if the transaction does not have an associated plan id, look for a default value for Plan ID -->
			<xsd:element name="PlanId" type="String6" minOccurs="0"/>
			<!-- Business Date is the date passed by the calling application, based on business requirements, Format: 2011-04-04. It is a required field for Archival into Mobius -->
			<xsd:element name="BusinessDate" type="xsd:date" minOccurs="0"/>
			<!--
				Archival Acknowledgement Indicator. ArchivalAck = Y will allow DCS
				to respond to the WS call, that the request was successfully
				submitted to Mobius for archival. Default is N.
			-->
			<xsd:element name="ArchivalAck" type="YesOrNoOption" default="N" minOccurs="0"/>
			<!--
				EDelivery Acknowledgement Indicator. EdelAck = Y will allow DCS to
				respond to the WS call, that the request was successfully submitted
				to Mobius for EDelivery. Default is N.
			-->
			<xsd:element name="EdelAck" type="YesOrNoOption" default="N" minOccurs="0"/>
			<xsd:element name="EmailAck" type="YesOrNoOption" default="N" minOccurs="0"/>
			<!--
				Print Acknowledgement Indicator. PrintAck = Y will allow DCS to
				respond to the WS call, that the request was successfully submitted
				for printing to the local printer. Default is N.
			-->
			<xsd:element name="PrintAck" type="YesOrNoOption" default="N" minOccurs="0"/>
			<!--
				Harte Hanks Acknowledgement Indicator. HHAck = Y will allow DCS to
				respond to the WS call, that the request was successfully submitted
				for printing to the Harte Hanks. Default is N.
			-->
			<xsd:element name="HHAck" type="YesOrNoOption" default="N" minOccurs="0"/>
			<!--
				Fax Acknowledgement Indicator. FaxAck = Y will allow DCS to
				respond to the WS call, that the request was successfully submitted
				for faxing. Default is N.
			-->
			<xsd:element name="FaxAck" type="YesOrNoOption" default="N" minOccurs="0"/>
			<!-- Added by CCP -->
			<xsd:element name="DeliveryPreferences" minOccurs="0" maxOccurs="1">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="DeliveryPreference" minOccurs="1" maxOccurs="unbounded">
							<xsd:annotation>
								<xsd:documentation>List of delivery preferences for participant.  No strict checking at this point.</xsd:documentation>
							</xsd:annotation>
							<xsd:complexType>
								<xsd:sequence>
									<xsd:element name="Category" type="xsd:string"/>
									<xsd:element name="PreferenceId" type="xsd:string"/>
									<xsd:element name="PreferenceAction" type="xsd:string"/>
									<xsd:element name="Preference" type="xsd:string"/>
								</xsd:sequence>
							</xsd:complexType>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="CcpPackageId" minOccurs="0" maxOccurs="1">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:pattern value="C[0-9]{24}[a-zA-z0-9]{15}"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="OrderNumber" minOccurs="0" maxOccurs="1">
                           <xsd:simpleType>
                                 <xsd:restriction base="xsd:string">
                                 </xsd:restriction>
                           </xsd:simpleType>
            </xsd:element>
			<xsd:element name="CcpLetterType" type="String6" minOccurs="0" maxOccurs="1"/>
			<!-- WebRegistrationIndicator, which comes from QRS system -->
			<xsd:element name="WebRegistrationIndicator" type="YesOrNoOption" default="N" minOccurs="0"/>
			<!-- ParticipantLastLoginDate is the last web login of the participant, comes from QRS -->
			<xsd:element name="ParticipantLastLoginDate" type="xsd:date" minOccurs="0"/>
			<xsd:element name="BillingProfile" type="String4" minOccurs="0" maxOccurs="1"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- List of Bin Items, up to 10, selected publications/booklets to be included with the document-->
	<xsd:complexType name="BinItems">
		<xsd:sequence>
			<!-- This is only required when there are business rules requiring selective inserts.  Calling applications must pass the list of publications/booklets that need to be included with the document.-->
			<xsd:element name="BinItem" type="String8" maxOccurs="10"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- This is required for requests where either an image copy of the document or the packing list needs to be uploaded to the EXP AG task. -->
	<xsd:element name="ExpAgWorkflowInfo" type="ExpAgWorkflowInfo"/>
	<xsd:complexType name="ExpAgWorkflowInfo">
		<xsd:sequence>
			<!-- Export Indicator can be one of the following:  I=Task ID, G=Task GUID, T=Task Type.  Task Type is required for Task Creation in EXP AG.  Task ID or Task GUID is for Task Update in EXP AG. -->
			<xsd:element name="ExportInd" type="ExportOptions"/>
			<!--If ExportInd is I, Task ID must be passed.  Task ID is 11-characters and starts with 't'. The value should contain only alphabets or 0-9. -->
			<xsd:element name="TaskId" type="ExpAgTaskId" minOccurs="0"/>
			<!--If ExportInd is G, Task GUID must be passed. -->
			<xsd:element name="TaskGuid" type="String50" minOccurs="0"/>
			<!--If ExportInd is T, Task Type must be passed. Other fields below should be filled since this is for Task Creation.-->
			<xsd:element name="TaskType" type="String10" minOccurs="0"/>
			<!--Should be PROCESS, NONE, or FAIL if the ExportInd is 'T' for Task Creation-->
			<xsd:element name="ActionStep" type="ActionSteps" minOccurs="0"/>
			<!--Specify the name of the document being attached in EXP AG task, e.g. DCS LETTER or DCS Content.-->
			<xsd:element name="DocContent" type="String35" minOccurs="0"/>
			<!--For Task Creation, should be 'O' for Open task or 'C' for Closed task. -->
			<xsd:element name="TaskStatus" type="ExpAgWorkflowTaskStatus" minOccurs="0"/>
			<!--For Task Creation, pass the Plan ID-->
			<xsd:element name="PlanId" type="String6" minOccurs="0"/>
			<!--Tiaa Full Date is in YYYY-MM-DD hh:mm:ss format-->
			<xsd:element name="TiaaDateTime" type="xsd:dateTime" minOccurs="0"/>
			<!-- Social Security No (SSN) or Tax Identification No (TIN), should be 9 digit numbers (XXX-XX-XXXX) and can accept numbers only (0-9). -->
			<xsd:element name="SSN" type="SSN" minOccurs="0"/>
			<!-- Universal ID can be one of the following: PIN, NPIN, or Institution ID/PPG. This is identified by the Universal Type below. -->
			<xsd:element name="UniversalId" type="UniversalId"/>
			<!-- Universal Type specifies the type of the value in the Universal ID.  P=PIN, N=NPIN, I=Institution -->
			<xsd:element name="UniversalType" type="UniversalTypeCodes"/>
			<!--For Task Creation, list of contracts for this request, up to 10.-->
			<xsd:element name="ExpAgContracts" type="ExpAgContracts" minOccurs="0"/>
			<!-- List of MI Other Items, will be a combination of key/value pairs -->
			<xsd:element name="OtherItems" type="OtherItems" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--For Task Creation, list of contracts for this request, up to 10.-->
	<xsd:complexType name="ExpAgContracts">
		<xsd:sequence>
			<xsd:element name="ContractNumber" type="String10" maxOccurs="10"/>
			<!--Contract Numbers, can be maximum up to 10.-->
		</xsd:sequence>
	</xsd:complexType>
	<!--Other Items, can be a list of Other Item (name/value pairs).-->
	<xsd:complexType name="OtherItems">
		<xsd:sequence>
			<xsd:element ref="OtherItem" maxOccurs="unbounded"/>
			<!-- OtherItem, can be a name/value pairs -->
		</xsd:sequence>
	</xsd:complexType>
	<!-- OtherItem, can be a name/value pairs -->
	<xsd:element name="OtherItem" type="OtherItem"/>
	<xsd:complexType name="OtherItem">
		<xsd:sequence>
			<!-- Name -->
			<xsd:element name="Name" type="String35"/>
			<!-- Value -->
			<xsd:element name="Value" type="String35"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--
		Print Delivery Type specifies the type of the print.S=STANDARD PRINT.
        This tag is should not be used if your application is new to DCS.
        Only available for use by IPAC and Roster Mapping and Annuity Contribution Statements.
        Use the other choice element, Delivery Type which offers more distribution channels
	-->
	<xsd:simpleType name="PrintDeliveryTypes">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="S"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Address type Codes is the enumeration type with the values (U=US, F=Foreign, C=Canada or B=Bad). -->
	<xsd:simpleType name="AddressTypeCodes">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="U"/>
			<xsd:enumeration value="F"/>
			<xsd:enumeration value="C"/>
			<xsd:enumeration value="B"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ExportOptions is the enumeration type with the values (I=Task ID, G=Task GUID, T=Task Type). -->
	<xsd:simpleType name="ExportOptions">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="I"/>
			<xsd:enumeration value="G"/>
			<xsd:enumeration value="T"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ExportOptions is the enumeration type with the values (PROCESS, NONE, FAIL). -->
	<xsd:simpleType name="ActionSteps">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="PROCESS"/>
			<xsd:enumeration value="NONE"/>
			<xsd:enumeration value="FAIL"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ExpAgWorkflowTaskStatus is the enumeration type with the values (O=Open, C=Closed). -->
	<xsd:simpleType name="ExpAgWorkflowTaskStatus">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="O"/>
			<xsd:enumeration value="C"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ExpAgTaskId can be a simpleType with values should start with letter 't' and followed by 10 occurrences of lower case alphabet or 0-9. -->
	<xsd:simpleType name="ExpAgTaskId">
		<xsd:restriction base="xsd:string">
			<!--  <xsd:pattern value="(t[a-z0-9]{10})"/> -->
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		Portal_Doc_Desc field can carry request-level description of the
		document to be displayed on the web portal.
	-->
	<xsd:simpleType name="PortalDocDesc">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="250"/>
		</xsd:restriction>
	</xsd:simpleType>
	
<!--
		EmailDocDesc field can carry request-level description of the
		email document to be displayed on the web portal.
	-->
	<xsd:simpleType name="EmailDocDesc">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="250"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		Archival_Ind = M Archive the document in Mobius. N indicates No
		Archival
	-->
	<xsd:simpleType name="ArchivalIndicator">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="M"/>
			<xsd:enumeration value="I"/>
			<xsd:enumeration value="N"/>
			<!-- M - Mobius -->
			<!-- N - No archival -->
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		Return document type can be PDF or TIFF.
	-->
	<xsd:simpleType name="ReturnDocumentType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="PDF"/>
			<xsd:enumeration value="TIFF"/>
			<!-- POSTSCRIPT will be supported in future	-->
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		Delivery Types is the enumeration type with the values (L=LOCAL PRINT,
		S=STANDARD PRINT, O=OVERNIGHT DELIVERY, R=RETURN TO UNIT (Pullouts),
		E=EDELIVERY, F=FAX, C=RETURN DOCUMENT TO CLIENT, A=MOBIUS ARCHIVAL, D=CDROM).
	-->
	<xsd:simpleType name="DeliveryTypes">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="S"/>
			<xsd:enumeration value="O"/>
			<xsd:enumeration value="R"/>
			<xsd:enumeration value="E"/>
			<xsd:enumeration value="F"/>
			<xsd:enumeration value="A"/>
			<xsd:enumeration value="L"/>
			<xsd:enumeration value="C"/>
			<xsd:enumeration value="H"/>
			<xsd:enumeration value="EMAIL"/>
			<xsd:enumeration value="M"/>
			<xsd:enumeration value="P"/>
			<xsd:enumeration value="D"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:element name="InputDocuments" type="Documents"/>
	<xsd:complexType name="Documents">
		<xsd:choice maxOccurs="unbounded">
			<!-- List of Document -->
			<xsd:element name="Document" type="Document"/>
			<xsd:element name="Document_SDP" type="Document"/>
			<xsd:element name="Document_SSP" type="Document"/>
			<xsd:element name="Document_SHL" type="Document"/>
			<xsd:element name="Document_STL" type="Document"/>
			<xsd:element name="Document_NSP" type="Document"/>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="Document">
		<xsd:sequence>
			<xsd:element name="Name" type="String200" minOccurs="0"/>
			<xsd:element name="Type" type="DocumentType" default="PDF" minOccurs="0"/>
			<xsd:element name="Content" type="xsd:base64Binary" minOccurs="0" xmime:expectedContentTypes="application/pdf, application/rtf, image/tiff"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="DocumentType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="PDF"/>
			<xsd:enumeration value="RTF"/>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
