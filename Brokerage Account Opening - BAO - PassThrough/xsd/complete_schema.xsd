<?xml version='1.0' encoding='UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xmime="http://ccp.tiaa.org/communications-rs-v1/communication/xmlmime/types" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" elementFormDefault="qualified" attributeFormDefault="unqualified">
  <xsd:annotation>
    <xsd:documentation xml:lang="en-US">
			This schema will contain all the common elements like MailItemInfo and ExpAgWorkflowInfo for all the Documents Requests. The source application specific schema's will extend these common Elements.
		<!--
		     If the data contains ", then the calling application must send &quot;
			 If the data contains &, then the calling application must send &amp;
			 If the data contains ', then the calling application must send &apos;
			-->
		</xsd:documentation>
  </xsd:annotation>
  <!-- Includes -->
  <xsd:include schemaLocation="ccp-document-request-commons-v1.xsd"/>
  <xsd:include schemaLocation="ccp-document-request-datatypes-v1.xsd"/>
  <xsd:import schemaLocation="ccp-document-request-bao-v2.xsd"/>
  <!-- RootElement DocumentRequests, for all the MQ/ESB requests will contain one DocumentRequest/account per request, where as File Share (Quarterly or Annually) requests will contain multiple DocumentRequests/accounts per request -->
  <xsd:element name="DocumentRequests" type="DocumentRequests"/>
  <xsd:complexType name="DocumentRequests">
    <xsd:sequence>
      <!-- List of DocumentRequest -->
      <xsd:element ref="DocumentRequest" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <!-- Each DocumentRequest will extend the MailItemRecord (i.e.; which will provide the MI info) -->
  <xsd:element name="DocumentRequest" type="DocumentRequest"/>
  <xsd:complexType name="DocumentRequest">
    <xsd:complexContent>
      <!-- Extends the MailItemRequestInfo Element -->
      <xsd:extension base="MailItemRequestInfo">
        <xsd:sequence>
          <xsd:element name="EmailInfo" type="xsd:anyType" minOccurs="0"/>
          <!-- Requests of type ExpAgWorkflowInfo -->
          <xsd:element ref="ExpAgWorkflowInfo" minOccurs="0"/>
          <xsd:choice>
            <!-- Calling Application specific XML data will be embeded in this field as a String; This will reduce the impacts/testing in DCS for any application specific field changes in future. DCS will convert this XML string into a document and validate it agianst application specific XML schema using XSLT and send it to Dialogue -->
            <xsd:element name="DocumentInfo" type="xsd:anyType"/>
            <xsd:element name="DocumentInfoXml">
              <xsd:complexType>
                <xsd:sequence>
                  <xsd:element ref="BAORequest"/>
                </xsd:sequence>
              </xsd:complexType>
            </xsd:element>
          </xsd:choice>
          <xsd:element ref="InputDocuments" minOccurs="0"/>
          <xsd:element name="HarteHanksControlInfo" type="xsd:anyType" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="DocumentResponse">
    <xsd:sequence>
      <xsd:element name="DocumentRequestId" type="DocumentRequestId" minOccurs="0"/>
      <xsd:element name="DocumentLocator" type="DocumentLocator" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation>One of several types of keys which may be used to retrieve or display an archived document.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <!--
				EDelivery Acknowledgement Indicator. When EdelAck = Y, DCS will
				respond to the WS call, that the request was successfully submitted
				to Mobius for EDelivery with a Value of Y
			-->
      <xsd:element name="EDelAck" type="YesOrNoOption" minOccurs="0"/>
      <xsd:element name="EmailAck" type="YesOrNoOption" minOccurs="0"/>
      <!--
				Harte Hanks Acknowledgement Indicator. When HHAck = Y, DCS will
				respond to the WS call, that the request was successfully submitted
				for printing to Harte Hanks with a Value of Y
			-->
      <xsd:element name="HHAck" type="YesOrNoOption" minOccurs="0"/>
      <!--
				Mobius Archival Acknowledgement Indicator. When ArchivalAck = Y, DCS
				will respond to the WS call, that the request was successfully
				submitted to Mobius for Archival with a Value of Y
			-->
      <xsd:element name="ArchivalAck" type="YesOrNoOption" minOccurs="0"/>
      <!--
				Print Acknowledgement Indicator. When PrintAck = Y, DCS will respond
				to the WS call, that the request was successfully submitted for
				printing to local printer with a Value of Y
			-->
      <xsd:element name="PrintAck" type="YesOrNoOption" minOccurs="0"/>
      <!--
				Fax Acknowledgement Indicator. When PrintAck = Y, DCS will respond
				to the WS call, that the request was successfully submitted to
				fax with a Value of Y
			-->
      <xsd:element name="FaxAck" type="YesOrNoOption" minOccurs="0" default="N"/>
      <!--
				PDF/TIFF document returned to client when the DeliveryType = 'C'
			-->
      <xsd:element name="DocumentContent" type="xsd:base64Binary" xmime:expectedContentTypes="application/pdf, image/tiff" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DocumentLocator">
    <xsd:annotation>
      <xsd:documentation>One of several types of keys which may be used to retrieve or display an archived document.</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="Type" type="DocumentLocatorType">
        <xsd:annotation>
          <xsd:documentation>The DocumentLocatorType (type of key) of this DocumentLocator
						 Values:
						   EMLContentLocationID: Unique EML DOC_LOCATION key used to retrieve a document through EDocument-V1:RetrieveEDocumentContent.
						   TCIContentLocationID: Unique TCI Document ID key for retrieving the document through EDocument-V1:RetrieveEDocumentContent.
						   InternalWebRetrievalURL: URL which may be used to download and display in a browser on the TIAA-CREF network.
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Value" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>The value of this document locator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="DocumentLocatorType">
    <xsd:annotation>
      <xsd:documentation>This enumeration defines the types of document locators which can be requested by, and returned to,
			 the consumer by store operations.
			 DocumentLocatorType values:
			   EMLContentLocationID: Unique EML DOC_LOCATION key used to retrieve a document through EDocument-V1:RetrieveEDocumentContent.
			   TCIContentLocationID: Unique TCI Document ID key for retrieving the document.
			   InternalWebRetrievalURL: URL which may be used to download and display in a browser on the TIAA-CREF network.
			 </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="EMLContentLocationID"/>
      <xsd:enumeration value="TCIContentLocationID"/>
      <xsd:enumeration value="InternalWebRetrievalURL"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
