<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v5 rel. 3 U (http://www.xmlspy.com) by TIAA-CREF (TIAA-CREF) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xsd:annotation>
		<xsd:documentation xml:lang="en-US">
			This schema will contain the common datatypes, which will be used by all the other Xml schema. It will provide the validation rules/patterns used by the fields.
		<!--
		     If the data contains ", then the calling application must send &quot;
			 If the data contains &, then the calling application must send &amp;
			 If the data contains ', then the calling application must send &apos;
	    -->
		</xsd:documentation>
	</xsd:annotation>
	<!-- Extend DocumentRequestInfo to provide the basic application specific information for a document request-->
	<xsd:complexType name="BaseDocumentRequest"/>
	<!-- PlanInfo contains the plan information -->
	<xsd:complexType name="PlanInfo">
		<xsd:sequence>
			<!-- Plan Name, can contain maximum 100 characters and the values should have alphabets and apostrophe (') only. -->
			<xsd:element name="PlanName" type="EntityName" minOccurs="1"/>
			<!-- Plan Number, can contain maximum 6 characters -->
			<xsd:element name="PlanNumber" type="String6" minOccurs="1"/>
			<!-- Plan Admin Name, can contain maximum 185 characters -->
			<xsd:element name="PlanAdminName" type="FullName" minOccurs="0"/>
			<!-- Plan Admin Title, can contain maximum 75 characters -->
			<xsd:element name="PlanAdminTitle" type="String75" minOccurs="0"/>
			<!-- Name of Authorized Signatory for the Plan -->
			<xsd:element name="PlanSignatoryName" type="String35" minOccurs="0"/>
			<!-- Unit of Authorized Signatory for the Plan -->
			<xsd:element name="PlanSignatoryUnit" type="String50" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- AddressLines contains maximum of six lines of address with 200 characters length -->
	<xsd:complexType name="AddressLines">
		<xsd:sequence>
			<!-- AddressLine Element-->
			<xsd:element name="AddressLine" type="String200" minOccurs="1" maxOccurs="6"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- YesOrNoOption is the enumeration type with the values (Y=Yes, N=No). -->
	<xsd:simpleType name="YesOrNoOption">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Y"/>
			<xsd:enumeration value="N"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- SimpleType SSN, should be 9 digit numbers (XXX-XX-XXXX) and can accept numbers only (0-9). -->
	<xsd:simpleType name="SSN">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9]{9})"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Fax Number, should be 9 digit numbers (XXX-XXX-XXXX) and can accept numbers only (0-9) -->
	<xsd:simpleType name="FaxNumber">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9]{10})"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Application Id, should be minimum 2 and maximum 20 digits -->
	<xsd:simpleType name="ApplicationId">
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="30"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Universal ID, should be minimum 7 and maximum 13 digits -->
	<xsd:simpleType name="UniversalId">
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="7"/>
			<xsd:maxLength value="13"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- UniversalTypeCodes is the enumeration type with the values (P=PIN, N=NPIN, I=Institution). -->
	<xsd:simpleType name="UniversalTypeCodes">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="P"/>
			<xsd:enumeration value="N"/>
			<xsd:enumeration value="I"/>
			<xsd:enumeration value="T"/>
			<xsd:enumeration value="E"/>
			<xsd:enumeration value="CN"/>
			<xsd:enumeration value="C"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Document Request ID, should be minimum 34 digits and maximum 13 digits and should contain only 0-9 -->
	<xsd:simpleType name="DocumentRequestId">
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="34"/>
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Printer ID, should be minimum 4 and maximum 8 digits -->
	<xsd:simpleType name="PrinterId">
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="4"/>
			<xsd:maxLength value="8"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- UniversalTypeCodes is the enumeration type with the values (C=CHECKING, S=SAVING). -->
	<xsd:simpleType name="AccountTypeCodes">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="C"/>
			<xsd:enumeration value="S"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Prefix and Suffix, can be maximum 10 digits and should contain alphabets and dot (.) only -->
	<xsd:simpleType name="PrefixSuffix">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- User Name, can be maximum 50 digits and should contain alphabets and apostrophe (') only -->
	<xsd:simpleType name="UserName">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Full Name, can be maximum 185 digits and should contain alphabets, dot(.) and apostrophe (') only -->
	<xsd:simpleType name="FullName">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="185"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Full Name, can be maximum 100 digits and should contain alphabets, dot(.) and apostrophe (') only -->
	<xsd:simpleType name="EntityName">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="100"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Account Number, can be maximum 35 digits and should contain numeric numbers (0-9) only -->
	<xsd:simpleType name="AccountNumber">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9]{35})"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Digit3">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="3"/>
			<xsd:minLength value="3"/>
			<xsd:pattern value="([0-9]{3})"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String1, can be maximum 1 character -->
	<xsd:simpleType name="String1">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String3, can be maximum 3 character -->
	<xsd:simpleType name="String3">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="3"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String5">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="5"/>
		</xsd:restriction>
	</xsd:simpleType>	
	<!-- String6, can be maximum 6 characters -->
	<xsd:simpleType name="String6">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="6"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String8, can be maximum 8 characters -->
	<xsd:simpleType name="String8">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="8"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String9, can be maximum 9 characters -->
	<xsd:simpleType name="String9">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="9"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String10, can be maximum 10 characters -->
	<xsd:simpleType name="String10">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String11, can be maximum 11 characters -->
	<xsd:simpleType name="String11">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="11"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String12, can be maximum 12 characters -->
	<xsd:simpleType name="String12">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="12"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String13, can be maximum 13 characters -->
	<xsd:simpleType name="String13">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="13"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String15, can be maximum 15 characters -->
	<xsd:simpleType name="String15">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="15"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String18, can be maximum 15 characters -->
	<xsd:simpleType name="String18">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="18"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String19, can be maximum 19 characters -->
	<xsd:simpleType name="String19">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="19"/>
		</xsd:restriction>
	</xsd:simpleType>	
	<!-- String20, can be maximum 20 characters -->
	<xsd:simpleType name="String20">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
       <!-- String21, can be maximum 21 characters -->
	<xsd:simpleType name="String21">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="21"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String22, can be maximum 22 characters -->
	<xsd:simpleType name="String22">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="22"/>
		</xsd:restriction>
	</xsd:simpleType>	
	<!-- String35, can be maximum 35 characters -->
	<xsd:simpleType name="String35">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String45">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="45"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String50, can be maximum 50 characters -->
	<xsd:simpleType name="String50">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String55, can be maximum 55 characters -->
	<xsd:simpleType name="String55">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="55"/>
		</xsd:restriction>
	</xsd:simpleType>
       <!-- String64, can be maximum 64 characters -->
	<xsd:simpleType name="String64">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="64"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String75, can be maximum 75 characters -->
	<xsd:simpleType name="String75">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="75"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String16">
        <xsd:restriction base="xsd:string">
            <xsd:maxLength value="16"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="String32">
        <xsd:restriction base="xsd:string">
            <xsd:maxLength value="32"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="String33">
        <xsd:restriction base="xsd:string">
            <xsd:maxLength value="33"/>
        </xsd:restriction>
    </xsd:simpleType>
	<!-- String100, can be maximum 100 characters -->
	<xsd:simpleType name="String100">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="100"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String50, can be maximum 200 characters -->
	<xsd:simpleType name="String200">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="200"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- String250, can be maximum 250 characters -->
	<xsd:simpleType name="String250">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="250"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String1500">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="1500"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String300">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="300"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String500">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="500"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Int4, can be maximum 4 digits -->
	<xsd:simpleType name="Int4">
		<xsd:restriction base="xsd:int">
			<xsd:totalDigits value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Int10,  must be 10 digits (XXXXXXXXXX) and can accept numbers only (0-9) -->
	<xsd:simpleType name="Int10">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9]{10})"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Decimal152, can be maximum 15 digits with 2 decimal places -->
	<xsd:simpleType name="Decimal152">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="15"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- Int5, can be maximum 5 digits -->
	<xsd:simpleType name="Int5">
		<xsd:restriction base="xsd:int">
			<xsd:totalDigits value="5"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Int7">
		<xsd:restriction base="xsd:int">
			<xsd:totalDigits value="7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal157">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="15"/>
			<xsd:fractionDigits value="7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal112">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="11"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String120">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="120"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String2">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="CoRecordType">
		<xsd:sequence>
			<xsd:element name="CoRecordId" type="String2" minOccurs="0"/>
			<xsd:element name="CoApplicationId" type="String8" minOccurs="0"/>
			<xsd:element name="CoStatementDate" type="String8" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- CCP Common? -->
	<xsd:simpleType name="DetailCategoryCodeType">
		<xsd:restriction base="xsd:string">
			<xsd:length value="1"/>
			<xsd:enumeration value="A"/>
			<xsd:enumeration value="R"/>
			<xsd:enumeration value="G"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal3">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="3"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal4_1">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="4"/>
			<xsd:fractionDigits value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal5">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="5"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal5_2">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="5"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal6">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="6"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal7">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal10_4">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="10"/>
			<xsd:fractionDigits value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal12_4">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="12"/>
			<xsd:fractionDigits value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal14_2">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="14"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Decimal14_4">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="14"/>
			<xsd:fractionDigits value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DohMachFuncType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="0"/>
			<xsd:enumeration value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Int3">
		<xsd:restriction base="xsd:int">
			<xsd:totalDigits value="3"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LoanTypeType">
		<xsd:restriction base="xsd:string">
			<xsd:length value="1"/>
			<xsd:enumeration value="0"/>
			<xsd:enumeration value="1"/>
			<xsd:enumeration value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String4">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String7">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String14">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="14"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String25">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="25"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String30">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="30"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String40">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String60">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="60"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String70">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="70"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String80">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="80"/>

		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String150">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="150"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="YyyyMmDdDateType">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{4}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:simpleType name="String1000">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="1000"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:simpleType name="String2000">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="2000"/>
		</xsd:restriction>
	</xsd:simpleType>
	
</xsd:schema>
