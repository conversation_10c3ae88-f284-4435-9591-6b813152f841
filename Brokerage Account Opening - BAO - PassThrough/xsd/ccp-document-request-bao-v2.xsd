<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2014 rel. 2 sp1 (x64) (http://www.altova.com) by <PERSON> (TIAA-CREF) -->
<!-- edited with XMLSPY v5 rel. 3 U (http://www.xmlspy.com) by TIAA-CREF (TIAA-CREF) -->
<xsd:schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="qualified">
	<xsd:annotation>
		<xsd:documentation xml:lang="en-US">
			IRA Enrollment			
			Application ID:BAO
				System ID: 080, SubSystem ID: 001		
		</xsd:documentation>
	</xsd:annotation>
	<!-- Includes -->
	<xsd:include schemaLocation="ccp-document-request-v1-types.xsd"/>
	<!-- Requests of type IraEnrollmentRequests-->
	<xsd:element name="BAORequest" type="BAORequest"/>
	<xsd:complexType name="BAORequest">
		<xsd:complexContent>
			<xsd:extension base="BaseDocumentRequest">
				<xsd:sequence>
					<xsd:element name="AccountType" type="String20" minOccurs="0"/>
					<xsd:element name="JointTenantAccountType" type="JointTenantAccountType" minOccurs="0"/>					
					<xsd:element name="AccountCategory" type="String20" minOccurs="0"/>
					<xsd:element name="ProposalNumber" type="String20" minOccurs="0"/>
					<xsd:element name="LetterRequest" type="LetterRequest" minOccurs="0"/>
					<xsd:element name="AccountOwnerInformation" type="AccountOwnerInformation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="GrantorInformation" type="AccountOwnerInformation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="JointGrantorInformation" type="AccountOwnerInformation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="PrimaryBeneficiary" type="BeneficiaryInformation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="ContingentBeneficiary" type="BeneficiaryInformation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="SpousalWaiver" type="YesOrNoOption" minOccurs="0"/>
					<xsd:element name="DeliveryMethod" type="String50" minOccurs="0" />
					<xsd:choice maxOccurs="unbounded">
						<xsd:element name="ApplicationForm" type="ApplicationForm" minOccurs="0"/>
						<xsd:element name="FundingForm" type="FundingForm" minOccurs="0"/>
					</xsd:choice>
					<xsd:element name="InputDocuments" type="Documents" />
					<xsd:element name="HHBAOControlInfo" type="xsd:anyType" minOccurs="0" />
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="LetterRequest">
		<xsd:sequence>
			<xsd:element name="LetterID" type="String8" minOccurs="0"/>
			<xsd:element name="ListofForms" type="String75" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ApplicationForm">
		<xsd:sequence>
			<xsd:element name="FormNumber" type="String15" minOccurs="0"/>
			<xsd:element name="FormSequenceNumber" type="Int4" minOccurs="0"/>
			<xsd:element name="OrchestrationIdAF" type="String20" minOccurs="0"/>
			<xsd:element name="BrokerageServices" type="BrokerageServices" minOccurs="0"/>
			<xsd:element name="InheritedIRAInformation" type="InheritedIRAInformation" minOccurs="0"/>
			<xsd:element name="InitialTransactions" type="InitialTransactions" minOccurs="0"/>
			<xsd:element name="SweepSelection" type="SweepSelection" minOccurs="0"/>
			<xsd:element name="Edelivery" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Margin" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="TrustCertification" type="TrustCertification" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="FundingForm">
		<xsd:sequence>
			<xsd:element name="FormNumber" type="String15" minOccurs="0"/>
			<xsd:element name="FormSequenceNumber" type="Int4" minOccurs="0"/>
			<xsd:element name="OrchestrationIdFF" type="String20" minOccurs="0"/>
			<xsd:element name="SixtyRollover" type="SixtyRollover" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="BrokerageServices">
		<xsd:sequence>
			<xsd:element name="NonIraOption" type="NonIraOptions" minOccurs="0"/>
			<xsd:element name="IraOption" type="IraTypes" minOccurs="0"/>
			<xsd:element name="EmployerTaxID" type="String20" minOccurs="0"/>
			<xsd:element name="OtherDescription" type="String75" minOccurs="0"/>
			<xsd:element name="TrustAccount" type="TrustAccount" minOccurs="0"/>
			<xsd:element name="InvestmentObjective" type="InvestmentObjective" minOccurs="0"/>
			<xsd:element name="InvestmentExperience" type="InvestmentExperience" minOccurs="0"/>
			<xsd:element name="AnnualIncome" type="AnnualIncome" minOccurs="0"/>
			<xsd:element name="NetWorth" type="Networth" minOccurs="0"/>
			<xsd:element name="TaxBraket" type="TaxBraket" minOccurs="0"/>
			<xsd:element name="InterestedParty" type="InterestedParty" minOccurs="0"/>
			<xsd:element name="SourceOfFunds" type="SourceOfFunds" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="SourceOfFundsOtherDescription" type="String35" minOccurs="0"/>
			<xsd:element name="DecendentPlanType" type="PlanTypes" minOccurs="0"/>
			<xsd:element name="TypeOfTrust" type="TrustTypes" minOccurs="0"/>

		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="InterestedParty">
		<xsd:sequence>
			<xsd:element name="Title" type="FullName" minOccurs="0"/>
			<xsd:element name="PartyFirstName" type="FullName" minOccurs="0"/>
			<xsd:element name="PartyMiddleName" type="FullName" minOccurs="0"/>
			<xsd:element name="PartyLastName" type="FullName" minOccurs="0"/>
			<xsd:element name="PartyAddress" type="AddressLines" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="PlanTypes">
		<xsd:restriction base="String35">
			<xsd:enumeration value="Traditional IRA"/>
			<xsd:enumeration value="Roth IRA"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TrustTypes">
		<xsd:restriction base="String35">
			<xsd:enumeration value="Charitable"/>
			<xsd:enumeration value="Family"/>
			<xsd:enumeration value="Irrevocable"/>
			<xsd:enumeration value="Irrevocable Living"/>
			<xsd:enumeration value="Testamentary"/>
			<xsd:enumeration value="Living"/>
			<xsd:enumeration value="Revocable"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="SixtyRollover">
		<xsd:sequence>
			<xsd:element name="AnotherTiaaCrefAccount" type="AnotherTiaaCrefAccount" minOccurs="0"/>
			<xsd:element name="InvestmentCompanyInformation" type="BankCompanyInformation" minOccurs="0"/>
			<xsd:element name="RolloverAccountType" type="RolloverAccountTypes" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TrustAccount">
		<xsd:sequence>
			<xsd:element name="TrustAccountName" type="FullName" minOccurs="0"/>
			<xsd:element name="TrustSSN" type="SSN" minOccurs="0"/>
			<xsd:element name="TrustEffectiveDate" type="xsd:date" minOccurs="0"/>
			<xsd:element name="TrustAmmendmentDate" type="xsd:date" minOccurs="0"/>			
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="InvestmentExperience">
		<xsd:sequence>
			<xsd:element name="Equity" type="Int4" minOccurs="0"/>
			<xsd:element name="MutualFunds" type="Int4" minOccurs="0"/>
			<xsd:element name="Options" type="Int4" minOccurs="0"/>
			<xsd:element name="FixedIncome" type="Int4" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AccountOwnerInformation">
		<xsd:sequence>
			<xsd:element name="FullName" type="FullName" minOccurs="0"/>
			<xsd:element name="Prefix" type="PrefixSuffix" minOccurs="0"/>
			<xsd:element name="FirstName" type="UserName" minOccurs="0"/>
			<xsd:element name="MiddleName" type="UserName" minOccurs="0"/>
			<xsd:element name="LastName" type="UserName" minOccurs="0"/>
			<xsd:element name="Suffix" type="PrefixSuffix" minOccurs="0"/>
			<xsd:element name="SSN" type="SSN" minOccurs="0"/>
			<xsd:element name="FaxNumber" type="FaxNumber" minOccurs="0"/>
			<xsd:element name="EmailAddress" type="String50" minOccurs="0"/>
			<xsd:element name="Gender" type="Genders" minOccurs="0"/>
			<xsd:element name="BirthDate" type="xsd:date" minOccurs="0"/>
			<xsd:element name="MaritalStatus" type="MaritalStatus" minOccurs="0"/>
			<xsd:element name="BusinessPhone" type="FaxNumber" minOccurs="0"/>
			<xsd:element name="Extension" type="String10" minOccurs="0"/>
			<xsd:element name="HomePhone" type="FaxNumber" minOccurs="0"/>
			<xsd:element name="HomeExtension" type="String10" minOccurs="0"/>
			<xsd:element name="ResidentialStreetAddress" type="String75" minOccurs="0"/>
			<xsd:element name="ResidentialCity" type="String50" minOccurs="0"/>
			<xsd:element name="ResidentialState" type="String6" minOccurs="0"/>
			<xsd:element name="ResidentialZipcode" type="String10" minOccurs="0"/>
			<xsd:element name="ResidentialCountry" type="String50" minOccurs="0"/>
			<xsd:element name="MailingStreetAddress" type="String75" minOccurs="0"/>
			<xsd:element name="MailingCity" type="String50" minOccurs="0"/>
			<xsd:element name="MailingState" type="String6" minOccurs="0"/>
			<xsd:element name="MailingZipcode" type="String10" minOccurs="0"/>
			<xsd:element name="MailingCountry" type="String50" minOccurs="0"/>
			<xsd:element name="BrokerageAccountNumber" type="String10" minOccurs="0"/>
			<xsd:element name="StateOfResidence" type="String6" minOccurs="0"/>
			<xsd:element name="Citzenship" type="String50" minOccurs="0"/>
			<xsd:element name="EmploymentStatus" type="EmploymentStatusType"/>
			<xsd:element name="EmploymentInformation" type="EmploymentInformation" minOccurs="0"/>
			<xsd:element name="AffiliationInformation" type="AfiliationInformation" minOccurs="0"/>
			<xsd:element name="InvestmentProfile" type="InvestmentProfile" minOccurs="0"/>
			<xsd:element name="PhoneType" type="String50" minOccurs="0"/>
			<xsd:element name="GrantorInd" type="YesOrNoOption" minOccurs="0"/>		
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="EmploymentInformation">
		<xsd:sequence>
			<xsd:element name="EmployerName" type="FullName" minOccurs="0"/>
			<xsd:element name="EmployerOccupation" type="String75" minOccurs="0"/>
			<xsd:element name="EmploymentType" type="EmploymentTypes" minOccurs="0"/>
			<xsd:element name="EmploymentTitle" type="String75" minOccurs="0"/>
			<xsd:element name="EmployerStreetAddress" type="String75" minOccurs="0"/>
			<xsd:element name="EmployerCity" type="String50" minOccurs="0"/>
			<xsd:element name="EmployerState" type="String6" minOccurs="0"/>
			<xsd:element name="EmployerZipcode" type="String10" minOccurs="0"/>
			<xsd:element name="EmployerCountry" type="String50" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AfiliationInformation">
		<xsd:sequence>
			<xsd:element name="TradeCompany" type="TradeCompanyType" minOccurs="0"/>
			<xsd:element name="TiaaCref" type="TiaaCrefType" minOccurs="0"/>
			<xsd:element name="MemberFirm" type="MemberFirmType" minOccurs="0"/>
			<xsd:element name="SeniorMilitaryCheckBox" type="YesOrNoOption" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="BeneficiaryInformation">
		<xsd:sequence>
			<xsd:element name="Category" type="Category"  minOccurs="0"/>
			<xsd:element name="BeneficiaryName" type="FullName"/>
			<xsd:element name="BeneficiaryPercentage" type="Percentage" minOccurs="0"/>
			<xsd:element name="BeneficiarySSN" type="SSN" minOccurs="0"/>
			<xsd:element name="BeneficiaryRelationship" type="String50" minOccurs="0"/>
			<xsd:element name="BeneficiaryDateOfBirth" type="xsd:date" minOccurs="0"/>
			<xsd:element name="BeneficiaryGender" type="Genders" minOccurs="0"/>
			<xsd:element name="BeneficiaryStreetAddress" type="String75" minOccurs="0"/>
			<xsd:element name="BeneficiaryCity" type="String50" minOccurs="0"/>
			<xsd:element name="BeneficiaryState" type="String6" minOccurs="0"/>
			<xsd:element name="BeneficiaryZipcode" type="String10" minOccurs="0"/>
			<xsd:element name="BeneficiaryCountry" type="String50" minOccurs="0"/>
			<xsd:element name="BeneficiaryContactPhoneNumber" type="FaxNumber" minOccurs="0"/>
			<xsd:element name="BeneficiaryLDPS" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="BeneficiaryType" type="BeneficiaryTypes" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="InheritedIRAInformation">
		<xsd:sequence>
			<xsd:element name="DecendentFirstName" type="UserName" minOccurs="0"/>
			<xsd:element name="DecendentMiddleName" type="UserName" minOccurs="0"/>
			<xsd:element name="DecendentLastName" type="UserName" minOccurs="0"/>
			<xsd:element name="DecendentSSN" type="SSN" minOccurs="0"/>
			<xsd:element name="DecendentBirthDate" type="xsd:date" minOccurs="0"/>
			<xsd:element name="DecendentDeathDate" type="xsd:date" minOccurs="0"/>
			<xsd:element name="DecendentRelationship" type="String35" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="InitialTransactions">
		<xsd:sequence>
			<xsd:element name="TransferFunds" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="AccountNumber" type="String20" minOccurs="0"/>
			<xsd:element name="FullTransfer" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="DeliveringAccountClosed" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="PartialTransfer" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="TransferCashAmount" type="Decimal152" minOccurs="0"/>
			<xsd:element name="VoteProxy" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="TransferShares" type="TransferShares" minOccurs="0" maxOccurs="3"/>
			<xsd:element name="ConfirmSuppressionStatus" type="YesOrNoOption" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="SweepSelection">
		<xsd:sequence>
			<xsd:element name="SweepAccount" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Cash" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Liquid" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Prime" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Govemment" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Municipal" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Treasury" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="StateSpecific" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="Sate" type="States" minOccurs="0"/>
			<xsd:element name="DreyfusGovCashManageServiceShares" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="DreyfusGovSecCashManageInvestor" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="DreyfusGovCashManageInvester" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="FederatedHermesGovObligationsCash" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="FederatedHermesTrustUSTreasuryOblicationsCash" type="YesOrNoOption" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="TrustCertification">
		<xsd:sequence>
			<xsd:element name="DelegateToAppointedAgent" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="DelegateToOutsideProfessional" type="YesOrNoOption" minOccurs="0"/>
		</xsd:sequence>
	</xsd:simpleType>
	<xsd:complexType name="TransferShares">
		<xsd:sequence>
			<xsd:element name="SharesDescription" type="String35" minOccurs="0"/>
			<xsd:element name="SharesQuantity" type="xsd:decimal" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AnotherTiaaCrefAccount">
		<xsd:choice>
			<xsd:element name="ExistingAccount" type="ExistingAccount" minOccurs="0"/>
			<xsd:element name="NewAccount" type="NewAccount" minOccurs="0"/>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="ExistingAccount">
		<xsd:sequence>
			<xsd:element name="ExistingAccountOption" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="TiaaNumber" type="String10" minOccurs="0"/>
			<xsd:element name="CrefNumber" type="String10" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="NewAccount">
		<xsd:sequence>
			<xsd:element name="NewAccountOption" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="IraOption" type="IraOptions" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="BankCompanyInformation">
		<xsd:sequence>
			<xsd:element name="BankCompanyName" type="FullName" minOccurs="0"/>
			<xsd:element name="StreetAddress" type="AddressLines" minOccurs="0"/>
			<xsd:element name="PhoneNumber" type="FaxNumber" minOccurs="0"/>
			<xsd:element name="Extension" type="String10" minOccurs="0"/>
			<xsd:element name="AccountNumber" type="String20" minOccurs="0"/>
			<xsd:element name="BankRoutingNumber" type="String10" minOccurs="0"/>
			<xsd:element name="BankRolloverAmount" type="Decimal152" minOccurs="0"/>
			<xsd:element name="AccountOwnerFirstName" type="FullName" minOccurs="0"/>
			<xsd:element name="AccountOwnerLastName" type="FullName" minOccurs="0"/>
			<xsd:element name="AccountJointFirstName" type="FullName" minOccurs="0"/>
			<xsd:element name="AccountJointLastName" type="FullName" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="Genders">
		<xsd:restriction base="String6">
			<xsd:enumeration value="Male"/>
			<xsd:enumeration value="Female"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EmploymentTypes">
		<xsd:restriction base="String8">
			<xsd:enumeration value="Active"/>
			<xsd:enumeration value="Retired"/>
			<xsd:enumeration value="Trustee"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MaritalStatus">
		<xsd:restriction base="String8">
			<xsd:enumeration value="Single"/>
			<xsd:enumeration value="Married"/>
			<xsd:enumeration value="Unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="EmploymentStatusType">
		<xsd:sequence>
			<xsd:element name="EmploymentStatus" type="EmploymentStatus" minOccurs="0"/>
			<xsd:element name="TypeOfBusiness" type="xsd:string" minOccurs="0"/>
			<xsd:element name="SourceOfIncome" type="xsd:string" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="EmploymentStatus">
		<xsd:restriction base="String6">
			<xsd:enumeration value="EMPL"/>
			<xsd:enumeration value="SEMP"/>
			<xsd:enumeration value="UEMP"/>
			<xsd:enumeration value="RETD"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IraTypes">
		<xsd:restriction base="String35">
			<xsd:enumeration value="Traditional/Rollover IRA"/>
			<xsd:enumeration value="Roth IRA"/>
			<xsd:enumeration value="SEP IRA"/>
			<xsd:enumeration value="Inherited Traditional IRA"/>
			<xsd:enumeration value="Inherited Roth IRA"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IraOptions">
		<xsd:restriction base="String20">
			<xsd:enumeration value="Traditional IRA"/>
			<xsd:enumeration value="Roth IRA"/>
			<xsd:enumeration value="SEP IRA"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RolloverAccountTypes">
		<xsd:restriction base="String20">
			<xsd:enumeration value="401(a)"/>
			<xsd:enumeration value="401(k)"/>
			<xsd:enumeration value="403(b)"/>
			<xsd:enumeration value="403(b)(7)"/>
			<xsd:enumeration value="414(h)"/>
			<xsd:enumeration value="457(b) Public"/>
			<xsd:enumeration value="Keogh Profit Sharing"/>
			<xsd:enumeration value="SEP IRA"/>
			<xsd:enumeration value="Roth IRA"/>
			<xsd:enumeration value="Simple IRA"/>
			<xsd:enumeration value="Traditional IRA"/>
			<xsd:enumeration value="Keogh Money Purchase"/>
			<xsd:enumeration value="Roth 403(b)"/>
			<xsd:enumeration value="Roth 401(k)"/>
			<xsd:enumeration value="Defined Benefit Plan"/>
			<xsd:enumeration value="Roth 457(b)"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="NonIraOptions">
		<xsd:restriction base="String35">
			<xsd:enumeration value="Individual"/>
			<xsd:enumeration value="Joint"/>
			<xsd:enumeration value="Joint Tenants in Common"/>
			<xsd:enumeration value="Trust"/>
			<xsd:enumeration value="Gifts/Transfer to Minors"/>
			<xsd:enumeration value="Other"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="States">
		<xsd:restriction base="String6">
			<xsd:enumeration value="NY"/>
			<xsd:enumeration value="NJ"/>
			<xsd:enumeration value="CT"/>
			<xsd:enumeration value="MA"/>
			<xsd:enumeration value="FL"/>
			<xsd:enumeration value="CA"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SourceOfFunds">
		<xsd:restriction base="String35">
			<xsd:enumeration value="Income from Earnings"/>
			<xsd:enumeration value="Investment Proceeds"/>
			<xsd:enumeration value="Gift"/>
			<xsd:enumeration value="Sale of Business"/>
			<xsd:enumeration value="Legal Settlement"/>
			<xsd:enumeration value="Pension/IRA Retirement Savings"/>
			<xsd:enumeration value="Spouse/Parent"/>
			<xsd:enumeration value="Inheritance"/>
			<xsd:enumeration value="Insurance Proceeds"/>
			<xsd:enumeration value="Other"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Percentage">
		<xsd:restriction base="String6"/>
	</xsd:simpleType>
	<xsd:simpleType name="InvestmentProfile">
		<xsd:restriction base="String10">
			<xsd:enumeration value="NONE"/>
			<xsd:enumeration value="LIMITED"/>
			<xsd:enumeration value="GOOD"/>
			<xsd:enumeration value="EXTENSIVE"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AnnualIncome">
		<xsd:restriction base="String20">
			<xsd:enumeration value="UNDER24999"/>
			<xsd:enumeration value="FROM75000TO99999"/>
			<xsd:enumeration value="FROM200000TO249999"/>
			<xsd:enumeration value="FROM25000TO49999"/>
			<xsd:enumeration value="FROM100000TO149999"/>
			<xsd:enumeration value="OVER250000"/>
			<xsd:enumeration value="FROM50000TO74999"/>
			<xsd:enumeration value="FROM150000TO199999"/>
			<xsd:enumeration value="FROM50000TO99999"/>
			<xsd:enumeration value="FROM100000TO249999"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Networth">
		<xsd:restriction base="String20">
			<xsd:enumeration value="UNDER49999"/>
			<xsd:enumeration value="FROM200000TO499999"/>
			<xsd:enumeration value="OVER2500000"/>
			<xsd:enumeration value="FROM50000TO99999"/>
			<xsd:enumeration value="FROM500000TO999999"/>
			<xsd:enumeration value="FROM100000TO199999"/>
			<xsd:enumeration value="FROM1000000TO2499999"/>	
			<xsd:enumeration value="FROM250000TOMILLION"/>
			<xsd:enumeration value="OVERMILLION"/>			
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TaxBraket">
		<xsd:restriction base="String6">
			<xsd:enumeration value="LWTB"/>
			<xsd:enumeration value="MDTB"/>
			<xsd:enumeration value="HITB"/>
			<xsd:enumeration value="TPTB"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="InvestmentObjective">
		<xsd:restriction base="String20">
			<xsd:enumeration value="CAPR"/>
			<xsd:enumeration value="INCM"/>
			<xsd:enumeration value="GRTH"/>
			<xsd:enumeration value="SPEC"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="TradeCompanyType">
		<xsd:sequence>
			<xsd:element name="TradeCompanyCheckBox" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="NameOfPerson" type="FullName" minOccurs="0"/>
			<xsd:element name="CompanyNameSymbol" type="FullName" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TiaaCrefType">
		<xsd:sequence>
			<xsd:element name="TIAACREFCheckBox" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="RelationshipToEmployee" type="FullName" minOccurs="0"/>
			<xsd:element name="NameOfEmployee" type="FullName" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="MemberFirmType">
		<xsd:sequence>
			<xsd:element name="FirmCheckBox" type="YesOrNoOption" minOccurs="0"/>
			<xsd:element name="RelationshipToEmployee" type="FullName" minOccurs="0"/>
			<xsd:element name="NameOfEmployee" type="FullName" minOccurs="0"/>
			<xsd:element name="NameOfFirm" type="FullName" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="DeliveryMethod">
		<xsd:restriction base="String50">
			<xsd:enumeration value="E-Signature-Agent"/>
			<xsd:enumeration value="Paper Local Print-Agent"/>
			<xsd:enumeration value="Paper HH Advisor-Agent"/>
			<xsd:enumeration value="Paper HH Client-Agent"/>
			<xsd:enumeration value="E-Signature-Direct"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Category">
		<xsd:restriction base="String20">
			<xsd:enumeration value="A"/>
			<xsd:enumeration value="B"/>
			<xsd:enumeration value="C"/>
			<xsd:enumeration value="D"/>
		</xsd:restriction>
	</xsd:simpleType>	
		<xsd:simpleType name="BeneficiaryTypes">
		<xsd:restriction base="String35">
			<xsd:enumeration value="Organization"/>
			<xsd:enumeration value="Trust"/>
			<xsd:enumeration value="Individual"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="JointTenantAccountType">
		<xsd:restriction base="String35">
			<xsd:enumeration value="RightsOfSurvivorship"/>
			<xsd:enumeration value="Entirety"/>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>