2025-07-30 09:06:02 Complete Payload Generator INFO     ========================= COMPLETE PAYLOAD GENERATION =========================
2025-07-30 09:06:02 Complete Payload Generator INFO     Sample payload XML used: BAO_COM_REQ_PKG.xml

2025-07-30 09:06:02 Complete Payload Generator INFO     Augmenting main schema with app schema...
2025-07-30 09:06:02 Complete Payload Generator INFO     	Using DocumentInfoXml from the choices (based on sample payload XML)...
2025-07-30 09:06:02 Complete Payload Generator INFO     	Added 1 element/s under DocumentInfoXml from app XSD: BAORequest...
2025-07-30 09:06:02 Complete Payload Generator INFO     	Complete Schema: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xsd/complete_schema.xsd

2025-07-30 09:06:02 Complete Payload Generator INFO     Getting all the elements and their type details from complete schema...
2025-07-30 09:06:02 Complete Payload Generator INFO     	Element details: 289 unique elements
2025-07-30 09:06:02 Complete Payload Generator INFO     	/Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/complete_payload/element_types.tsv

2025-07-30 09:06:02 Complete Payload Generator INFO     Preparing complete payload XML with valid values...
2025-07-30 09:06:11 Complete Payload Generator WARNING  	Choosing <DeliveryType> (/DocumentRequests/DocumentRequest/MailItemInfo/DeliveryType) based on sample payload XML (Choices: PrintDeliveryType, DeliveryType)...
2025-07-30 09:06:23 Complete Payload Generator WARNING  	Choosing <DocumentInfoXml> (/DocumentRequests/DocumentRequest/DocumentInfoXml) based on sample payload XML (Choices: DocumentInfo, DocumentInfoXml)...
2025-07-30 09:08:08 Complete Payload Generator WARNING  	Choosing <ExistingAccount> (/DocumentRequests/DocumentRequest/DocumentInfoXml/BAORequest/FundingForm/SixtyRollover/AnotherTiaaCrefAccount/ExistingAccount) - 1st choice element (Choices: ExistingAccount, NewAccount)...
2025-07-30 09:08:19 Complete Payload Generator WARNING  	Some elements were skipped because of 'choice' in XSD!
2025-07-30 09:08:19 Complete Payload Generator INFO     	Details of skipped elements: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/complete_payload/skipped_elems.tsv
2025-07-30 09:08:19 Complete Payload Generator INFO     	Complete XML payload: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/complete_payload/sample_payload.xml
2025-07-30 09:08:19 Complete Payload Generator INFO     	Metadata for complete XML: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/complete_payload/sample_payload_log.tsv

2025-07-30 09:08:19 Complete Payload Generator INFO     APP INPUT VALIDATION: Checking if XML elements in input XML provided are present in complete schema...
2025-07-30 09:08:19 Complete Payload Generator INFO     	Found 87 leaf elements in input XML
2025-07-30 09:08:19 Complete Payload Generator INFO     	Found 380 leaf elements from the schema
2025-07-30 09:08:19 Complete Payload Generator INFO     	VALIDATION PASSED: ALL elements from sample XML are present in the schema!

2025-07-30 09:08:19 Complete Payload Generator INFO     APP INPUT VALIDATION: Checking if XML elements used in rules are present in complete schema...
2025-07-30 09:08:19 Complete Payload Generator INFO     	Validating 106 XML tags from rules...
2025-07-30 09:08:19 Complete Payload Generator CRITICAL [ERR_XSD_002] VALIDATION FAILED: XML tags used in rules not found in schema
	Missing tags: 3
		1. DelegateToOutsideProfessional
		2. DelegateToAppointedAgent
		3. TrustType
	App specific XSD might be wrong! FIX THIS ISSUE BEFORE PROCEEDING!
