2025-07-30 09:05:51 App Report Parser INFO     ========================= APPLICATION: Brokerage Account Opening - BAO - PassThrough =========================

2025-07-30 09:05:51 App Report Parser INFO     ========================= APP REPORT PARSING =========================
2025-07-30 09:05:51 App Report Parser INFO     Extracts relevant sections and details from application report.

2025-07-30 09:05:51 App Report Parser INFO     Extracting sections...
2025-07-30 09:05:51 App Report Parser INFO     	DocumentList: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_sections/DocumentList.xml
2025-07-30 09:05:51 App Report Parser INFO     	PageList: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_sections/PageList.xml
2025-07-30 09:05:51 App Report Parser INFO     	VariableList: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_sections/VariableList.xml
2025-07-30 09:05:51 App Report Parser INFO     	RuleList: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_sections/RuleList.xml
2025-07-30 09:05:51 App Report Parser INFO     	XMLLayout from 'BAO Driver File_Updated': /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_sections/XmlLayout.xml

2025-07-30 09:05:51 App Report Parser INFO     Extracting variables...
2025-07-30 09:05:52 App Report Parser INFO     	Variables: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_info/variables.tsv
2025-07-30 09:05:52 App Report Parser INFO     Extracting XML tags and variable-xml mappings...
2025-07-30 09:05:52 App Report Parser INFO     	XML Tags: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_info/xml_tags.tsv
2025-07-30 09:05:52 App Report Parser INFO     	Variables-XML Tags Mapping: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_info/xml_tags_to_variables.tsv
2025-07-30 09:05:52 App Report Parser INFO     Extracting all the rules...
2025-07-30 09:05:52 App Report Parser INFO     	Rules: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_info/rules.tsv

2025-07-30 09:05:52 App Report Parser INFO     Preparing final JSON report and details for test cases generation...
2025-07-30 09:05:53 App Report Parser INFO     	JSON application report: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/AppReport.json..
2025-07-30 09:05:53 App Report Parser INFO     	Testcase Generation Details: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/xml_parse/app_info/testcase_generation_details.tsv..

2025-07-30 09:05:53 App Report Parser ERROR    [ERR_RULE_001] Some rules have missing details:
	Unique Rules affected due to errors: 619
	Rule IDs: 237087, 376817, 377560, 377561, 377562, 377563, 377564, 377565, 377566, 377567, 377570, 377576, 377608, 377630, 377631, 377632, 377633, 377634, 377637, 377639, 377670, 377671, 377673, 377674, 377675, 377676, 377677, 377680, 377682, 377713, 377714, 342094, 341896, 341859, 341860, 341867, 341971, 341898, 341899, 341900, 341901, 341905, 341935, 341950, 341951, 341953, 342059, 341989, 341990, 341996, 342042, 342043, 342045, 342165, 342131, 342132, 342133, 342134, 342136, 342151, 342173, 342527, 341603, 343968, 343969, 343970, 343971, 343972, 343973, 343974, 343977, 341632, 341633, 341667, 341671, 341673, 341674, 341675, 341676, 341677, 341679, 341680, 341695, 343939, 341711, 341712, 341713, 341714, 341716, 341733, 341747, 341748, 341751, 341765, 341766, 341767, 341768, 341769, 341771, 341786, 341802, 341803, 341806, 341820, 341821, 232830, 290282, 290283, 290284, 290285, 290286, 290287, 290288, 290289, 290292, 290298, 245280, 245281, 245282, 245283, 245284, 245320, 245362, 245321, 245342, 245343, 245344, 245345, 245350, 245363, 230758, 376656, 376557, 376558, 376566, 376570, 376556, 376631, 345067, 345786, 345787, 248650, 252654, 252655, 252656, 252657, 252658, 252659, 252660, 252661, 252662, 252663, 252664, 252666, 252667, 252668, 252669, 252670, 252671, 252672, 252673, 252674, 252675, 252676, 252677, 252678, 252680, 252681, 252682, 252683, 252684, 252685, 252686, 252687, 252688, 252689, 252690, 252691, 252692, 252694, 252695, 252696, 252697, 252698, 252699, 252700, 252701, 252702, 252703, 252704, 252705, 252706, 252708, 252709, 248998, 252710, 252711, 252712, 252713, 252714, 252715, 252716, 252717, 252718, 252719, 252720, 252722, 252723, 252726, 252727, 252728, 252729, 252730, 252731, 252732, 252733, 252734, 252735, 252736, 252738, 252739, 252740, 252741, 252742, 252743, 252744, 252745, 252746, 252747, 252748, 252749, 252750, 252752, 252753, 252754, 252755, 252756, 252757, 252758, 252759, 252760, 252761, 252762, 252763, 252764, 252766, 252767, 341061, 341137, 341099, 341100, 341107, 341190, 341191, 341192, 341194, 341139, 341140, 341141, 341142, 341146, 341176, 341279, 341210, 341211, 341217, 341262, 341263, 341265, 341356, 341322, 341323, 341324, 341325, 341327, 341341, 341358, 342530, 342227, 342181, 342182, 342183, 342184, 342185, 342186, 342187, 342188, 342189, 342190, 342191, 342195, 342207, 342208, 342283, 342228, 342229, 342230, 342231, 342232, 342233, 342266, 342331, 342284, 342285, 342286, 342287, 342288, 342289, 342322, 342376, 342332, 342333, 342334, 342335, 342336, 342337, 342370, 342421, 342377, 342378, 342379, 342380, 342381, 342382, 342415, 363613, 363552, 363553, 363554, 363555, 363556, 363557, 363590, 342507, 342466, 342467, 342468, 342523, 342526, 363596, 345265, 343282, 342868, 342869, 342870, 343099, 343100, 343101, 343102, 343103, 343104, 343108, 343184, 343131, 343232, 343185, 343204, 343205, 343206, 343207, 343208, 343225, 343281, 343233, 343252, 343253, 343254, 343255, 343256, 343273, 343330, 343283, 343284, 343285, 343286, 343287, 343302, 343310, 343378, 343331, 343350, 343351, 343352, 343353, 343354, 343369, 343425, 343379, 343398, 343399, 343400, 343401, 343402, 343417, 343471, 343426, 343427, 343428, 343429, 343435, 343436, 343437, 343438, 343439, 343445, 343476, 343479, 343480, 343488, 342528, 340076, 340010, 340011, 340012, 340013, 340014, 340015, 340016, 340019, 340077, 340038, 340086, 340087, 341357, 340088, 340089, 340090, 340091, 340095, 340096, 340125, 343938, 340712, 340713, 340714, 340715, 340719, 340750, 340946, 340947, 340950, 341000, 340964, 340965, 340966, 340967, 340969, 340984, 341001, 341002, 341005, 341359, 345294, 343823, 343517, 343518, 343519, 343520, 343521, 343522, 343525, 343531, 343824, 343547, 344914, 344915, 344916, 344917, 344918, 344919, 344922, 344925, 343826, 343591, 344939, 344940, 344941, 344942, 344943, 344944, 344947, 344950, 343828, 343635, 344964, 344965, 344966, 344967, 344968, 344969, 344972, 344975, 343829, 343679, 344989, 344990, 344991, 344992, 344993, 344994, 344997, 345000, 343830, 343723, 345014, 345015, 345016, 345017, 345018, 345019, 345022, 345025, 343831, 343767, 344863, 344864, 345065, 366683, 344865, 377270, 378116, 377271, 377272, 377273, 377274, 377275, 377276, 377277, 377280, 377281, 377287, 378159, 377302, 378160, 378161, 377474, 377475, 377476, 378162, 377722, 377723, 377725, 377740, 377741, 377743, 378163, 377758, 377759, 377760, 377761, 377764, 377778, 377794, 377795, 377797, 378164, 377812, 377813, 377814, 377815, 377819, 377833, 377848, 377849, 377851, 378165, 377868, 377869, 377870, 377871, 378166, 331062, 331068, 331525, 338805, 339672, 339130, 339131, 339132, 339133, 339134, 339135, 339139, 339140, 339285, 339251, 340078, 340082, 340083, 339616, 339313, 339314, 339316, 339317, 339617, 339618, 339621, 339884, 339466, 339467, 339468, 339469, 339471, 339488, 339782, 339783, 339786, 339965, 339725, 339726, 339727, 339728, 339730, 339747, 339947, 339948, 339951, 339968, 339967

2025-07-30 09:05:53 App Report Parser ERROR    	[ERR_RULE_001_01] Rules without variables count: 78
	Rules without variables list: 341896, 341971, 342059, 342165, 342173, 341603, 341632, 341667, 341671, 341673, 343939, 341765, 341820, 341821, 341137, 341190, 341279, 341356, 341358, 342227, 342283, 342331, 342376, 342421, 363613, 342507, 342523, 342526, 363596, 343282, 343184, 343232, 343281, 343330, 343378, 343425, 343471, 343476, 343479, 343480, 343488, 340076, 340077, 340086, 340087, 341357, 343938, 341000, 341359, 343823, 343824, 343826, 343828, 343829, 343830, 343831, 344863, 344864, 345065, 366683, 344865, 378116, 378159, 378160, 378161, 378162, 378163, 378164, 378165, 378166, 339672, 339285, 340078, 339616, 339884, 339965, 339968, 339967

2025-07-30 09:05:53 App Report Parser ERROR    	[ERR_RULE_001_02] Undefined rules count: 8
	Undefined rules list: 252658, 252672, 252686, 252700, 252714, 252730, 252744, 252758

2025-07-30 09:05:53 App Report Parser ERROR    	[ERR_RULE_001_05] Unique total unmapped variables count: 62
	Unique total unmapped variables list: FR_PrimaryBeneficiary_City_Display, FR_BAO_02_TrustAccountName, FR_ContingentBeneficiary_City_Display, FR_02_MiddleName, FR_ContingentBeneficiaryName_Display, FR_ContingentBeneficiaryContactPhoneNumber_Display, FR_BAO_02_BeneficiaryFlag4, FR_BAO_02_Trust_AmmendmentDate, FR_BAO_02_AccountOwnerSSN2, FR_BAO_02_BeneficiaryFlag3, FR_ContingentBeneficiary_StreetAddress_Display, FR_ContingentBeneficiaryGender_Display, FR_ContingentBeneficiary_State_Display, BAO_Captiva_2D_Barcode_flag, SYS_PageTotalPrinted, FR_BAO_02_TrustSSN, FR_BAO_02_SEPIRA_TIN_SSN, FR_BAO_02_ContingencyFlag2, FR_BAO_02_BeneficiaryFlag1, FR_02_CurrentForm, FR_02_FirstName, FR_02_Suffix, FR_02_FormId, FR_ContingentBeneficiaryBirthDate_Display, FR_ContingentBeneficiary_zipcode_Display, FR_ContingentBeneficiarySSN_Display, SYS_PagePrintedValue, FR_02_Title, FR_PrimaryBeneficiaryBirthDate_Display, FR_BAO_HighlightSSN_Field, FR_BAO_02_ContingencyFlag3, FR_BAO_02_Decendent_BirthDate_display, FR_PrimaryBeneficiaryRelationship_Display, FR_BAO_02_SpousalWaiverFlag1, FR_PrimaryBeneficiary_StreetAddress_Display, FR_BAO_02_ContingencyFlag1, FR_PrimaryBeneficiarySSN_Display, FR_ER_02_PrimaryBeneficiarySSN, FR_PrimaryBeneficiaryName_Display, FR_ContingentBeneficiaryPercentage_Display, FR_BAO_02_BeneficiaryFlag2, FR_BAO_02_AccountOwnerBrokerageAccountNumber, FR_02_CurrentVersionDate, FR_BAO_02_Decendent_DeathDate_display, SYS_PageTotalInDocument, FR_BAO_02_TrustEffectiveDate, FR_02_WPID, FR_PrimaryBeneficiary_State_Display, FR_BAO_02_ContingencyFlag4, FR_BAO_02_AccountOwner_BirthDate, FR_02_TempArray, SYS_PageInDocument, FR_02_LastName, SYS_SubDocInDocument, FR_BAO_02_State, FR_ER_02_ContingentBeneficiarySSN, FR_BAO_02_Beneficiary_BirthDate, FR_PrimaryBeneficiary_PhoneNumber_Display, FR_BAO_02_AccountOwner_BirthDate2, FR_BAO_02_AccountOwnerSSN, FR_BAO_02_SEPIRA_TIN_SSN_BAO_MANAGED, FR_PrimaryBeneficiary_zipcode_Display
	NOTE: Not all the unmapped variables may be used in rule conditions!

2025-07-30 09:05:53 App Report Parser INFO     Error details file based on parsing: /Users/<USER>/Desktop/xml-generation/Untitled/Brokerage Account Opening - BAO - PassThrough/logs/rule_errors_from_parsing.tsv


2025-07-30 09:05:53 App Report Parser INFO     ========================= APP REPORT STATS =========================
2025-07-30 09:05:53 App Report Parser INFO     		#Unique Documents: 28
2025-07-30 09:05:53 App Report Parser INFO     			#Unique Pages: 139
2025-07-30 09:05:53 App Report Parser INFO     				#Components for Pages: 945
2025-07-30 09:05:53 App Report Parser INFO     				#Component Refs for Pages: 2698
2025-07-30 09:05:53 App Report Parser INFO     			#Unique Sections: 0
2025-07-30 09:05:53 App Report Parser INFO     				#Unique Paragraphs: 0
2025-07-30 09:05:53 App Report Parser INFO     					#Components for Paragraphs: 0
2025-07-30 09:05:53 App Report Parser INFO     					#Component Refs for Paragraphs: 0
2025-07-30 09:05:53 App Report Parser INFO     		#Unique Rules (based on formula): 725
