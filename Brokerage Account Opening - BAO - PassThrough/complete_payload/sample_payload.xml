<?xml version='1.0' encoding='UTF-8'?>
<DocumentRequests>
  <DocumentRequest>
    <RequestUserId>TST_BRKG_QA_012</RequestUserId>
    <RequestUserName>OneIRA</RequestUserName>
    <RequestDateTime>2025-04-17T08:00:17.988Z</RequestDateTime>
    <CompositeOrchestrationId>C3I02302C</CompositeOrchestrationId>
    <OrchestrationId>C3I02302C</OrchestrationId>
    <TransactionReqID>ORCH-C3I02302C</TransactionReqID>
    <BusinessUnitCode>BROKERAGE</BusinessUnitCode>
    <DocCode>BAO_SD_IRA_PKG</DocCode>
    <Version>1</Version>
    <DocSequence>1</DocSequence>
    <BatchInd>N</BatchInd>
    <BatchCounter>1</BatchCounter>
    <BatchTotal>0</BatchTotal>
    <MailItemInfo>
      <ApplicationId>OneIRA</ApplicationId>
      <UniversalId>************</UniversalId>
      <UniversalType>P</UniversalType>
      <DocumentRequestId>08000220250417080017989************0001</DocumentRequestId>
      <EventIdentifier>EVT-20240625-00127</EventIdentifier>
      <PrinterId>PRT12345</PrinterId>
      <FullName>Jean Cassanos</FullName>
      <Prefix>Mr.</Prefix>
      <FirstName>Jean</FirstName>
      <MiddleName>Elizabeth</MiddleName>
      <LastName>Cassanos</LastName>
      <Suffix>Jr.</Suffix>
      <SSN>*********</SSN>
      <AddressTypeCode>U</AddressTypeCode>
      <AddressLines>
        <AddressLine>414 1ST ST</AddressLine>
      </AddressLines>
      <PostalBarCode>9400111899223859721348</PostalBarCode>
      <FaxNumber>9944118186</FaxNumber>
      <EmailAddress><EMAIL></EmailAddress>
      <LetterDate>2025-07-30</LetterDate>
      <Salutation>Dear Mr. Johnson</Salutation>
      <SignatoryName>Maria S. Lopez</SignatoryName>
      <SignatoryUnit>Department of Legal Affairs</SignatoryUnit>
      <BinItems>
        <BinItem>A1234B56</BinItem>
      </BinItems>
      <OtherItems>
        <OtherItem>
          <Name>Certified Mail Receipt</Name>
          <Value>Coffee mug</Value>
        </OtherItem>
      </OtherItems>
      <DeliveryType>S</DeliveryType>
      <EXPAGInd>N</EXPAGInd>
      <ReturnDocumentType>PDF</ReturnDocumentType>
      <PortalDocDesc>TIAA-CREF Brokerage - IRA Application</PortalDocDesc>
      <EmailDocDesc>Monthly account statement for your checking account ending in 4321</EmailDocDesc>
      <ArchivalInd>M</ArchivalInd>
      <PlanId>IRA001</PlanId>
      <BusinessDate>2025-07-30</BusinessDate>
      <ArchivalAck>Y</ArchivalAck>
      <EdelAck>Y</EdelAck>
      <EmailAck>Y</EmailAck>
      <PrintAck>Y</PrintAck>
      <HHAck>Y</HHAck>
      <FaxAck>Y</FaxAck>
      <DeliveryPreferences>
        <DeliveryPreference>
          <Category>Certified Mail</Category>
          <PreferenceId>EMAIL</PreferenceId>
          <PreferenceAction>Email</PreferenceAction>
          <Preference>Email</Preference>
        </DeliveryPreference>
      </DeliveryPreferences>
      <CcpPackageId>C000000000000000084041006000************</CcpPackageId>
      <OrderNumber>ORD12345678</OrderNumber>
      <CcpLetterType>Loans</CcpLetterType>
      <WebRegistrationIndicator>Y</WebRegistrationIndicator>
      <ParticipantLastLoginDate>2025-07-30</ParticipantLastLoginDate>
      <BillingProfile>A101</BillingProfile>
    </MailItemInfo>
    <EmailInfo><EMAIL></EmailInfo>
    <ExpAgWorkflowInfo>
      <ExportInd>I</ExportInd>
      <TaskId>WF12345678</TaskId>
      <TaskGuid>bbd15d62-3ad0-4792-981e-886ab43bc547</TaskGuid>
      <TaskType>Review</TaskType>
      <ActionStep>PROCESS</ActionStep>
      <DocContent>Automated Export Agreement Letter</DocContent>
      <TaskStatus>O</TaskStatus>
      <PlanId>IRA001</PlanId>
      <TiaaDateTime>2025-07-30T09:06:20</TiaaDateTime>
      <SSN>*********</SSN>
      <UniversalId>************</UniversalId>
      <UniversalType>P</UniversalType>
      <ExpAgContracts>
        <ContractNumber>CN84927361</ContractNumber>
      </ExpAgContracts>
      <OtherItems>
        <OtherItem>
          <Name>Expedited Processing Fee</Name>
          <Value>Additional files required</Value>
        </OtherItem>
      </OtherItems>
    </ExpAgWorkflowInfo>
    <DocumentInfoXml>
      <BAORequest>
        <AccountType>Traditional IRA</AccountType>
        <JointTenantAccountType>RightsOfSurvivorship</JointTenantAccountType>
        <AccountCategory>Self-directed</AccountCategory>
        <ProposalNumber>PRP20240615012345</ProposalNumber>
        <LetterRequest>
          <LetterID>A13198</LetterID>
          <ListofForms>TIAA-CREF Brokerage - IRA Application</ListofForms>
        </LetterRequest>
        <AccountOwnerInformation>
          <FullName>Jean Cassanos</FullName>
          <Prefix>Mr.</Prefix>
          <FirstName>Jean</FirstName>
          <MiddleName>Elizabeth</MiddleName>
          <LastName>Cassanos</LastName>
          <Suffix>Jr.</Suffix>
          <SSN>*********</SSN>
          <FaxNumber>**********</FaxNumber>
          <EmailAddress><EMAIL></EmailAddress>
          <Gender>Male</Gender>
          <BirthDate>1991-07-10</BirthDate>
          <MaritalStatus>Single</MaritalStatus>
          <BusinessPhone>**********</BusinessPhone>
          <Extension>**********</Extension>
          <HomePhone>**********</HomePhone>
          <HomeExtension>12345</HomeExtension>
          <ResidentialStreetAddress>414 1ST ST</ResidentialStreetAddress>
          <ResidentialCity>JUNEAU</ResidentialCity>
          <ResidentialState>AK</ResidentialState>
          <ResidentialZipcode>*********</ResidentialZipcode>
          <ResidentialCountry>US</ResidentialCountry>
          <MailingStreetAddress>414 1ST ST</MailingStreetAddress>
          <MailingCity>JUNEAU</MailingCity>
          <MailingState>AK</MailingState>
          <MailingZipcode>*********</MailingZipcode>
          <MailingCountry>US</MailingCountry>
          <BrokerageAccountNumber>**********</BrokerageAccountNumber>
          <StateOfResidence>AK</StateOfResidence>
          <Citzenship>US</Citzenship>
          <EmploymentStatus>
            <EmploymentStatus>EMPL</EmploymentStatus>
            <TypeOfBusiness>Information Technology Services</TypeOfBusiness>
            <SourceOfIncome>Salary and Wages</SourceOfIncome>
          </EmploymentStatus>
          <EmploymentInformation>
            <EmployerName>Test</EmployerName>
            <EmployerOccupation>Software Engineer</EmployerOccupation>
            <EmploymentType>Active</EmploymentType>
            <EmploymentTitle>Other</EmploymentTitle>
            <EmployerStreetAddress>845 Park Avenue Suite 300</EmployerStreetAddress>
            <EmployerCity>San Francisco</EmployerCity>
            <EmployerState>CA</EmployerState>
            <EmployerZipcode>10017</EmployerZipcode>
            <EmployerCountry>United States</EmployerCountry>
          </EmploymentInformation>
          <AffiliationInformation>
            <TradeCompany>
              <TradeCompanyCheckBox>N</TradeCompanyCheckBox>
              <NameOfPerson>Emily K. Thompson</NameOfPerson>
              <CompanyNameSymbol>AAPL</CompanyNameSymbol>
            </TradeCompany>
            <TiaaCref>
              <TIAACREFCheckBox>Y</TIAACREFCheckBox>
              <RelationshipToEmployee>Spouse</RelationshipToEmployee>
              <NameOfEmployee>Emily R. Thompson</NameOfEmployee>
            </TiaaCref>
            <MemberFirm>
              <FirmCheckBox>N</FirmCheckBox>
              <RelationshipToEmployee>Spouse</RelationshipToEmployee>
              <NameOfEmployee>Sarah T. Reynolds</NameOfEmployee>
              <NameOfFirm>Greenfield Capital Partners LLC</NameOfFirm>
            </MemberFirm>
            <SeniorMilitaryCheckBox>N</SeniorMilitaryCheckBox>
          </AffiliationInformation>
          <InvestmentProfile>NONE</InvestmentProfile>
          <PhoneType>Home</PhoneType>
          <GrantorInd>Y</GrantorInd>
        </AccountOwnerInformation>
        <GrantorInformation>
          <FullName>Jean Cassanos</FullName>
          <Prefix>Mr.</Prefix>
          <FirstName>Jean</FirstName>
          <MiddleName>Elizabeth</MiddleName>
          <LastName>Cassanos</LastName>
          <Suffix>Jr.</Suffix>
          <SSN>*********</SSN>
          <FaxNumber>**********</FaxNumber>
          <EmailAddress><EMAIL></EmailAddress>
          <Gender>Male</Gender>
          <BirthDate>1991-07-10</BirthDate>
          <MaritalStatus>Single</MaritalStatus>
          <BusinessPhone>**********</BusinessPhone>
          <Extension>Apt 204</Extension>
          <HomePhone>**********</HomePhone>
          <HomeExtension>4452</HomeExtension>
          <ResidentialStreetAddress>414 1ST ST</ResidentialStreetAddress>
          <ResidentialCity>JUNEAU</ResidentialCity>
          <ResidentialState>AK</ResidentialState>
          <ResidentialZipcode>*********</ResidentialZipcode>
          <ResidentialCountry>US</ResidentialCountry>
          <MailingStreetAddress>414 1ST ST</MailingStreetAddress>
          <MailingCity>JUNEAU</MailingCity>
          <MailingState>AK</MailingState>
          <MailingZipcode>*********</MailingZipcode>
          <MailingCountry>US</MailingCountry>
          <BrokerageAccountNumber>**********</BrokerageAccountNumber>
          <StateOfResidence>AK</StateOfResidence>
          <Citzenship>US</Citzenship>
          <EmploymentStatus>
            <EmploymentStatus>EMPL</EmploymentStatus>
            <TypeOfBusiness>Retail Trade</TypeOfBusiness>
            <SourceOfIncome>Salary from Acme Corporation</SourceOfIncome>
          </EmploymentStatus>
          <EmploymentInformation>
            <EmployerName>Test</EmployerName>
            <EmployerOccupation>Software Engineer</EmployerOccupation>
            <EmploymentType>Active</EmploymentType>
            <EmploymentTitle>Other</EmploymentTitle>
            <EmployerStreetAddress>742 Evergreen Terrace</EmployerStreetAddress>
            <EmployerCity>San Francisco</EmployerCity>
            <EmployerState>CA</EmployerState>
            <EmployerZipcode>94103</EmployerZipcode>
            <EmployerCountry>United States</EmployerCountry>
          </EmploymentInformation>
          <AffiliationInformation>
            <TradeCompany>
              <TradeCompanyCheckBox>N</TradeCompanyCheckBox>
              <NameOfPerson>Samantha L. Brooks</NameOfPerson>
              <CompanyNameSymbol>AlphaTech Solutions Inc.</CompanyNameSymbol>
            </TradeCompany>
            <TiaaCref>
              <TIAACREFCheckBox>Y</TIAACREFCheckBox>
              <RelationshipToEmployee>Spouse</RelationshipToEmployee>
              <NameOfEmployee>Samantha R. Bennett</NameOfEmployee>
            </TiaaCref>
            <MemberFirm>
              <FirmCheckBox>N</FirmCheckBox>
              <RelationshipToEmployee>Spouse</RelationshipToEmployee>
              <NameOfEmployee>Sarah L. Thompson</NameOfEmployee>
              <NameOfFirm>Greenberg &amp; Associates LLP</NameOfFirm>
            </MemberFirm>
            <SeniorMilitaryCheckBox>N</SeniorMilitaryCheckBox>
          </AffiliationInformation>
          <InvestmentProfile>NONE</InvestmentProfile>
          <PhoneType>Home</PhoneType>
          <GrantorInd>Y</GrantorInd>
        </GrantorInformation>
        <JointGrantorInformation>
          <FullName>Jean Cassanos</FullName>
          <Prefix>Mr.</Prefix>
          <FirstName>Jean</FirstName>
          <MiddleName>Elizabeth</MiddleName>
          <LastName>Cassanos</LastName>
          <Suffix>Jr.</Suffix>
          <SSN>*********</SSN>
          <FaxNumber>4443508107</FaxNumber>
          <EmailAddress><EMAIL></EmailAddress>
          <Gender>Male</Gender>
          <BirthDate>1991-07-10</BirthDate>
          <MaritalStatus>Single</MaritalStatus>
          <BusinessPhone>**********</BusinessPhone>
          <Extension>JNTGRANT</Extension>
          <HomePhone>**********</HomePhone>
          <HomeExtension>214</HomeExtension>
          <ResidentialStreetAddress>414 1ST ST</ResidentialStreetAddress>
          <ResidentialCity>JUNEAU</ResidentialCity>
          <ResidentialState>AK</ResidentialState>
          <ResidentialZipcode>*********</ResidentialZipcode>
          <ResidentialCountry>US</ResidentialCountry>
          <MailingStreetAddress>414 1ST ST</MailingStreetAddress>
          <MailingCity>JUNEAU</MailingCity>
          <MailingState>AK</MailingState>
          <MailingZipcode>*********</MailingZipcode>
          <MailingCountry>US</MailingCountry>
          <BrokerageAccountNumber>**********</BrokerageAccountNumber>
          <StateOfResidence>AK</StateOfResidence>
          <Citzenship>US</Citzenship>
          <EmploymentStatus>
            <EmploymentStatus>EMPL</EmploymentStatus>
            <TypeOfBusiness>Retail Sales</TypeOfBusiness>
            <SourceOfIncome>Salary from ABC Corporation</SourceOfIncome>
          </EmploymentStatus>
          <EmploymentInformation>
            <EmployerName>Test</EmployerName>
            <EmployerOccupation>Software Engineer</EmployerOccupation>
            <EmploymentType>Active</EmploymentType>
            <EmploymentTitle>Other</EmploymentTitle>
            <EmployerStreetAddress>2457 Maplewood Drive</EmployerStreetAddress>
            <EmployerCity>San Diego</EmployerCity>
            <EmployerState>CA</EmployerState>
            <EmployerZipcode>10018</EmployerZipcode>
            <EmployerCountry>United States</EmployerCountry>
          </EmploymentInformation>
          <AffiliationInformation>
            <TradeCompany>
              <TradeCompanyCheckBox>N</TradeCompanyCheckBox>
              <NameOfPerson>Patricia A. Gomez</NameOfPerson>
              <CompanyNameSymbol>ACME Industries (ACME)</CompanyNameSymbol>
            </TradeCompany>
            <TiaaCref>
              <TIAACREFCheckBox>Y</TIAACREFCheckBox>
              <RelationshipToEmployee>Spouse</RelationshipToEmployee>
              <NameOfEmployee>Emily R. Thompson</NameOfEmployee>
            </TiaaCref>
            <MemberFirm>
              <FirmCheckBox>N</FirmCheckBox>
              <RelationshipToEmployee>Spouse</RelationshipToEmployee>
              <NameOfEmployee>Emily R. Saunders</NameOfEmployee>
              <NameOfFirm>Harmon &amp; Strickland Financial Group LLC</NameOfFirm>
            </MemberFirm>
            <SeniorMilitaryCheckBox>N</SeniorMilitaryCheckBox>
          </AffiliationInformation>
          <InvestmentProfile>NONE</InvestmentProfile>
          <PhoneType>Home</PhoneType>
          <GrantorInd>Y</GrantorInd>
        </JointGrantorInformation>
        <PrimaryBeneficiary>
          <Category>A</Category>
          <BeneficiaryName>Amanda L. Rodriguez</BeneficiaryName>
          <BeneficiaryPercentage>100</BeneficiaryPercentage>
          <BeneficiarySSN>367098395</BeneficiarySSN>
          <BeneficiaryRelationship>Daughter</BeneficiaryRelationship>
          <BeneficiaryDateOfBirth>2025-07-30</BeneficiaryDateOfBirth>
          <BeneficiaryGender>Male</BeneficiaryGender>
          <BeneficiaryStreetAddress>742 Evergreen Terrace</BeneficiaryStreetAddress>
          <BeneficiaryCity>San Francisco</BeneficiaryCity>
          <BeneficiaryState>Texas</BeneficiaryState>
          <BeneficiaryZipcode>90210</BeneficiaryZipcode>
          <BeneficiaryCountry>United States</BeneficiaryCountry>
          <BeneficiaryContactPhoneNumber>6093194117</BeneficiaryContactPhoneNumber>
          <BeneficiaryLDPS>Y</BeneficiaryLDPS>
          <BeneficiaryType>Organization</BeneficiaryType>
        </PrimaryBeneficiary>
        <ContingentBeneficiary>
          <Category>A</Category>
          <BeneficiaryName>Alice Marie Johnson</BeneficiaryName>
          <BeneficiaryPercentage>50</BeneficiaryPercentage>
          <BeneficiarySSN>809595423</BeneficiarySSN>
          <BeneficiaryRelationship>Cousin</BeneficiaryRelationship>
          <BeneficiaryDateOfBirth>2025-07-30</BeneficiaryDateOfBirth>
          <BeneficiaryGender>Male</BeneficiaryGender>
          <BeneficiaryStreetAddress>742 Evergreen Terrace</BeneficiaryStreetAddress>
          <BeneficiaryCity>San Francisco</BeneficiaryCity>
          <BeneficiaryState>TX</BeneficiaryState>
          <BeneficiaryZipcode>94110-1234</BeneficiaryZipcode>
          <BeneficiaryCountry>United States</BeneficiaryCountry>
          <BeneficiaryContactPhoneNumber>3357360525</BeneficiaryContactPhoneNumber>
          <BeneficiaryLDPS>Y</BeneficiaryLDPS>
          <BeneficiaryType>Organization</BeneficiaryType>
        </ContingentBeneficiary>
        <SpousalWaiver>N</SpousalWaiver>
        <DeliveryMethod>USPS First Class Mail</DeliveryMethod>
        <ApplicationForm>
          <FormNumber>F11143</FormNumber>
          <FormSequenceNumber>1111</FormSequenceNumber>
          <OrchestrationIdAF>AF-********-48321</OrchestrationIdAF>
          <BrokerageServices>
            <NonIraOption>Individual</NonIraOption>
            <IraOption>Traditional/Rollover IRA</IraOption>
            <EmployerTaxID>94-1234567</EmployerTaxID>
            <OtherDescription>Access to international stock markets and global trading support</OtherDescription>
            <TrustAccount>
              <TrustAccountName>The Harrison Family Revocable Trust</TrustAccountName>
              <TrustSSN>*********</TrustSSN>
              <TrustEffectiveDate>2025-07-30</TrustEffectiveDate>
              <TrustAmmendmentDate>2025-07-30</TrustAmmendmentDate>
            </TrustAccount>
            <InvestmentObjective>GRTH</InvestmentObjective>
            <InvestmentExperience>
              <Equity>1111</Equity>
              <MutualFunds>1111</MutualFunds>
              <Options>1111</Options>
              <FixedIncome>1111</FixedIncome>
            </InvestmentExperience>
            <AnnualIncome>FROM100000TO249999</AnnualIncome>
            <NetWorth>FROM50000TO99999</NetWorth>
            <TaxBraket>LWTB</TaxBraket>
            <InterestedParty>
              <Title>Ms.</Title>
              <PartyFirstName>Michelle</PartyFirstName>
              <PartyMiddleName>Elizabeth</PartyMiddleName>
              <PartyLastName>Henderson</PartyLastName>
              <PartyAddress>
                <AddressLine>414 1ST ST</AddressLine>
              </PartyAddress>
            </InterestedParty>
            <SourceOfFunds>Investment Proceeds</SourceOfFunds>
            <SourceOfFundsOtherDescription>Inheritance from family estate</SourceOfFundsOtherDescription>
            <DecendentPlanType>Traditional IRA</DecendentPlanType>
            <TypeOfTrust>Charitable</TypeOfTrust>
          </BrokerageServices>
          <InheritedIRAInformation>
            <DecendentFirstName>Margaret</DecendentFirstName>
            <DecendentMiddleName>Marie</DecendentMiddleName>
            <DecendentLastName>Henderson</DecendentLastName>
            <DecendentSSN>*********</DecendentSSN>
            <DecendentBirthDate>2025-07-30</DecendentBirthDate>
            <DecendentDeathDate>2025-07-30</DecendentDeathDate>
            <DecendentRelationship>Parent</DecendentRelationship>
          </InheritedIRAInformation>
          <InitialTransactions>
            <TransferFunds>Y</TransferFunds>
            <AccountNumber>10239876543211234567</AccountNumber>
            <FullTransfer>Y</FullTransfer>
            <DeliveringAccountClosed>Y</DeliveringAccountClosed>
            <PartialTransfer>Y</PartialTransfer>
            <TransferCashAmount>*************.00</TransferCashAmount>
            <VoteProxy>Y</VoteProxy>
            <TransferShares>
              <SharesDescription>Global Equity Fund Class A</SharesDescription>
              <SharesQuantity>0.0</SharesQuantity>
            </TransferShares>
            <ConfirmSuppressionStatus>Y</ConfirmSuppressionStatus>
          </InitialTransactions>
          <SweepSelection>
            <SweepAccount>Y</SweepAccount>
            <Cash>Y</Cash>
            <Liquid>Y</Liquid>
            <Prime>Y</Prime>
            <Govemment>Y</Govemment>
            <Municipal>Y</Municipal>
            <Treasury>Y</Treasury>
            <StateSpecific>Y</StateSpecific>
            <Sate>NY</Sate>
            <DreyfusGovCashManageServiceShares>Y</DreyfusGovCashManageServiceShares>
            <DreyfusGovSecCashManageInvestor>Y</DreyfusGovSecCashManageInvestor>
            <DreyfusGovCashManageInvester>Y</DreyfusGovCashManageInvester>
            <FederatedHermesGovObligationsCash>Y</FederatedHermesGovObligationsCash>
            <FederatedHermesTrustUSTreasuryOblicationsCash>Y</FederatedHermesTrustUSTreasuryOblicationsCash>
          </SweepSelection>
          <Edelivery>Y</Edelivery>
          <Margin>N</Margin>
          <TrustCertification>Irrevocable Trust Document Certified</TrustCertification>
        </ApplicationForm>
        <FundingForm>
          <FormNumber>F11143</FormNumber>
          <FormSequenceNumber>1111</FormSequenceNumber>
          <OrchestrationIdFF>ORCH20240625123456</OrchestrationIdFF>
          <SixtyRollover>
            <AnotherTiaaCrefAccount>
              <ExistingAccount>
                <ExistingAccountOption>Y</ExistingAccountOption>
                <TiaaNumber>**********</TiaaNumber>
                <CrefNumber>**********</CrefNumber>
              </ExistingAccount>
            </AnotherTiaaCrefAccount>
            <InvestmentCompanyInformation>
              <BankCompanyName>First National Trust Bank</BankCompanyName>
              <StreetAddress>
                <AddressLine>414 1ST ST</AddressLine>
              </StreetAddress>
              <PhoneNumber>**********</PhoneNumber>
              <Extension>FormA1234</Extension>
              <AccountNumber>0034567891**********</AccountNumber>
              <BankRoutingNumber>*********</BankRoutingNumber>
              <BankRolloverAmount>*************.00</BankRolloverAmount>
              <AccountOwnerFirstName>Sarah</AccountOwnerFirstName>
              <AccountOwnerLastName>Rogers</AccountOwnerLastName>
              <AccountJointFirstName>Jennifer</AccountJointFirstName>
              <AccountJointLastName>Harrison</AccountJointLastName>
            </InvestmentCompanyInformation>
            <RolloverAccountType>401(a)</RolloverAccountType>
          </SixtyRollover>
        </FundingForm>
        <InputDocuments>
          <Document>
            <Name>Driver License Front</Name>
            <Type>PDF</Type>
            <Content>c2FtcGxlX2JpbmFyeV9kYXRh</Content>
          </Document>
          <Document_SDP>
            <Name>Social Security Statement</Name>
            <Type>PDF</Type>
            <Content>c2FtcGxlX2JpbmFyeV9kYXRh</Content>
          </Document_SDP>
          <Document_SSP>
            <Name>SocialSecurityStatement2024</Name>
            <Type>PDF</Type>
            <Content>c2FtcGxlX2JpbmFyeV9kYXRh</Content>
          </Document_SSP>
          <Document_SHL>
            <Name>Shareholding Ledger Statement</Name>
            <Type>PDF</Type>
            <Content>c2FtcGxlX2JpbmFyeV9kYXRh</Content>
          </Document_SHL>
          <Document_STL>
            <Name>Employee Statement 2024</Name>
            <Type>PDF</Type>
            <Content>c2FtcGxlX2JpbmFyeV9kYXRh</Content>
          </Document_STL>
          <Document_NSP>
            <Name>NonStandardPolicyStatement</Name>
            <Type>PDF</Type>
            <Content>c2FtcGxlX2JpbmFyeV9kYXRh</Content>
          </Document_NSP>
        </InputDocuments>
        <HHBAOControlInfo>PrimaryClaimControl987654</HHBAOControlInfo>
      </BAORequest>
    </DocumentInfoXml>
  </DocumentRequest>
</DocumentRequests>
