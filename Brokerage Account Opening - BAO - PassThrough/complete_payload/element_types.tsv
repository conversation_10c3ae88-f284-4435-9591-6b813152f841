element_name	type	type_base	type_restriction	info
DocumentRequests	DocumentRequests			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
DocumentRequest	DocumentRequest			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': 'unbounded', 'nillable': 'false'}
EmailInfo	xsd:anyType			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ExpAgWorkflowInfo	ExpAgWorkflowInfo			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocumentInfo	xsd:anyType			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
DocumentInfoXml				{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
BAORequest	BAORequest			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
InputDocuments	Documents			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
HarteHanksControlInfo	xsd:anyType			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocumentRequestId	DocumentRequestId	xsd:string	<xsd:minLength value="34"/><xsd:maxLength value="40"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocumentLocator	DocumentLocator			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
EDelAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmailAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
HHAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ArchivalAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PrintAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FaxAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': 'N', 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocumentContent	xsd:base64Binary			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Type	DocumentLocatorType	xsd:string	<xsd:enumeration value="EMLContentLocationID"/><xsd:enumeration value="TCIContentLocationID"/><xsd:enumeration value="InternalWebRetrievalURL"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Value	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
RequestUserId	String15	xsd:string	<xsd:maxLength value="15"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
RequestUserName	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
RequestDateTime	xsd:dateTime			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
CompositeOrchestrationId	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
OrchestrationId	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TransactionReqID	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BusinessUnitCode	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocCode	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Version	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocSequence	Int4	xsd:int	<xsd:totalDigits value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BatchInd	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
BatchCounter	xsd:long			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BatchTotal	xsd:long			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MailItemInfo	MailItemInfo			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
ApplicationId	ApplicationId	xsd:string	<xsd:minLength value="2"/><xsd:maxLength value="30"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
UniversalId	UniversalId	xsd:string	<xsd:minLength value="7"/><xsd:maxLength value="13"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
UniversalType	UniversalTypeCodes	xsd:string	<xsd:enumeration value="P"/><xsd:enumeration value="N"/><xsd:enumeration value="I"/><xsd:enumeration value="T"/><xsd:enumeration value="E"/><xsd:enumeration value="CN"/><xsd:enumeration value="C"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
EventIdentifier	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PrinterId	PrinterId	xsd:string	<xsd:minLength value="4"/><xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FullName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Prefix	PrefixSuffix	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FirstName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MiddleName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
LastName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Suffix	PrefixSuffix	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SSN	SSN	xsd:string	<xsd:pattern value="([0-9]{9})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AddressTypeCode	AddressTypeCodes	xsd:string	<xsd:enumeration value="U"/><xsd:enumeration value="F"/><xsd:enumeration value="C"/><xsd:enumeration value="B"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AddressLines	AddressLines			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PostalBarCode	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FaxNumber	FaxNumber	xsd:string	<xsd:pattern value="([0-9]{10})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmailAddress	String100	xsd:string	<xsd:maxLength value="100"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
LetterDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Salutation	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SignatoryName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SignatoryUnit	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BinItems	BinItems			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
OtherItems	OtherItems			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PrintDeliveryType	PrintDeliveryTypes	xsd:string	<xsd:enumeration value="S"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
DeliveryType	DeliveryTypes	xsd:string	<xsd:enumeration value="S"/><xsd:enumeration value="O"/><xsd:enumeration value="R"/><xsd:enumeration value="E"/><xsd:enumeration value="F"/><xsd:enumeration value="A"/><xsd:enumeration value="L"/><xsd:enumeration value="C"/><xsd:enumeration value="H"/><xsd:enumeration value="EMAIL"/><xsd:enumeration value="M"/><xsd:enumeration value="P"/><xsd:enumeration value="D"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
EXPAGInd	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ReturnDocumentType	ReturnDocumentType	xsd:string	<xsd:enumeration value="PDF"/><xsd:enumeration value="TIFF"/>	{'default': 'PDF', 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PortalDocDesc	PortalDocDesc	xsd:string	<xsd:maxLength value="250"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmailDocDesc	EmailDocDesc	xsd:string	<xsd:maxLength value="250"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ArchivalInd	ArchivalIndicator	xsd:string	<xsd:enumeration value="M"/><xsd:enumeration value="I"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PlanId	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BusinessDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EdelAck	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': 'N', 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DeliveryPreferences				{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DeliveryPreference				{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': 'unbounded', 'nillable': 'false'}
Category	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
PreferenceId	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
PreferenceAction	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Preference	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
CcpPackageId		xsd:string	<xsd:pattern value="C[0-9]{24}[a-zA-z0-9]{15}"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
OrderNumber		xsd:string		{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
CcpLetterType	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
WebRegistrationIndicator	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': 'N', 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ParticipantLastLoginDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BillingProfile	String4	xsd:string	<xsd:maxLength value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BinItem	String8	xsd:string	<xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '10', 'nillable': 'false'}
ExportInd	ExportOptions	xsd:string	<xsd:enumeration value="I"/><xsd:enumeration value="G"/><xsd:enumeration value="T"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
TaskId	ExpAgTaskId	xsd:string		{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TaskGuid	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TaskType	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ActionStep	ActionSteps	xsd:string	<xsd:enumeration value="PROCESS"/><xsd:enumeration value="NONE"/><xsd:enumeration value="FAIL"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocContent	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TaskStatus	ExpAgWorkflowTaskStatus	xsd:string	<xsd:enumeration value="O"/><xsd:enumeration value="C"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TiaaDateTime	xsd:dateTime			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ExpAgContracts	ExpAgContracts			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ContractNumber	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '10', 'nillable': 'false'}
OtherItem	OtherItem			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': 'unbounded', 'nillable': 'false'}
Name	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Value	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Document	Document			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Document_SDP	Document			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Document_SSP	Document			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Document_SHL	Document			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Document_STL	Document			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Document_NSP	Document			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
Name	String200	xsd:string	<xsd:maxLength value="200"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Type	DocumentType	xsd:string	<xsd:enumeration value="PDF"/><xsd:enumeration value="RTF"/>	{'default': 'PDF', 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Content	xsd:base64Binary			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PlanName	EntityName	xsd:string	<xsd:maxLength value="100"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
PlanNumber	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
PlanAdminName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PlanAdminTitle	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PlanSignatoryName	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PlanSignatoryUnit	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AddressLine	String200	xsd:string	<xsd:maxLength value="200"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '6', 'nillable': 'false'}
CoRecordId	String2	xsd:string	<xsd:maxLength value="2"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
CoApplicationId	String8	xsd:string	<xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
CoStatementDate	String8	xsd:string	<xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountType	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
JointTenantAccountType	JointTenantAccountType	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountCategory	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ProposalNumber	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
LetterRequest	LetterRequest			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountOwnerInformation	AccountOwnerInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
GrantorInformation	AccountOwnerInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
JointGrantorInformation	AccountOwnerInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
PrimaryBeneficiary	BeneficiaryInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
ContingentBeneficiary	BeneficiaryInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
SpousalWaiver	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DeliveryMethod	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ApplicationForm	ApplicationForm			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FundingForm	FundingForm			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
HHBAOControlInfo	xsd:anyType			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
LetterID	String8	xsd:string	<xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ListofForms	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
FormNumber	String15	xsd:string	<xsd:maxLength value="15"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FormSequenceNumber	Int4	xsd:int	<xsd:totalDigits value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
OrchestrationIdAF	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BrokerageServices	BrokerageServices			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InheritedIRAInformation	InheritedIRAInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InitialTransactions	InitialTransactions			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SweepSelection	SweepSelection			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Edelivery	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Margin	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TrustCertification	TrustCertification			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
OrchestrationIdFF	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SixtyRollover	SixtyRollover			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NonIraOption	NonIraOptions	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
IraOption	IraTypes	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerTaxID	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
OtherDescription	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TrustAccount	TrustAccount			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InvestmentObjective	InvestmentObjective	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InvestmentExperience	InvestmentExperience			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AnnualIncome	AnnualIncome	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NetWorth	Networth	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TaxBraket	TaxBraket	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InterestedParty	InterestedParty			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SourceOfFunds	SourceOfFunds	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': 'unbounded', 'nillable': 'false'}
SourceOfFundsOtherDescription	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentPlanType	PlanTypes	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TypeOfTrust	TrustTypes	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Title	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PartyFirstName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PartyMiddleName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PartyLastName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PartyAddress	AddressLines			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AnotherTiaaCrefAccount	AnotherTiaaCrefAccount			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InvestmentCompanyInformation	BankCompanyInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
RolloverAccountType	RolloverAccountTypes	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TrustAccountName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TrustSSN	SSN	xsd:string	<xsd:pattern value="([0-9]{9})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TrustEffectiveDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TrustAmmendmentDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Equity	Int4	xsd:int	<xsd:totalDigits value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MutualFunds	Int4	xsd:int	<xsd:totalDigits value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Options	Int4	xsd:int	<xsd:totalDigits value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FixedIncome	Int4	xsd:int	<xsd:totalDigits value="4"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmailAddress	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Gender	Genders	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BirthDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MaritalStatus	MaritalStatus	xsd:string	<xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BusinessPhone	FaxNumber	xsd:string	<xsd:pattern value="([0-9]{10})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Extension	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
HomePhone	FaxNumber	xsd:string	<xsd:pattern value="([0-9]{10})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
HomeExtension	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ResidentialStreetAddress	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ResidentialCity	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ResidentialState	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ResidentialZipcode	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ResidentialCountry	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MailingStreetAddress	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MailingCity	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MailingState	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MailingZipcode	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MailingCountry	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BrokerageAccountNumber	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
StateOfResidence	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Citzenship	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmploymentStatus	EmploymentStatusType			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
EmploymentInformation	EmploymentInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AffiliationInformation	AfiliationInformation			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
InvestmentProfile	InvestmentProfile	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PhoneType	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
GrantorInd	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerOccupation	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmploymentType	EmploymentTypes	xsd:string	<xsd:maxLength value="8"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmploymentTitle	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerStreetAddress	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerCity	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerState	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerZipcode	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmployerCountry	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TradeCompany	TradeCompanyType			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TiaaCref	TiaaCrefType			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
MemberFirm	MemberFirmType			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SeniorMilitaryCheckBox	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Category	Category	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryPercentage	Percentage	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiarySSN	SSN	xsd:string	<xsd:pattern value="([0-9]{9})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryRelationship	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryDateOfBirth	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryGender	Genders	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryStreetAddress	String75	xsd:string	<xsd:maxLength value="75"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryCity	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryState	String6	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryZipcode	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryCountry	String50	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryContactPhoneNumber	FaxNumber	xsd:string	<xsd:pattern value="([0-9]{10})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryLDPS	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BeneficiaryType	BeneficiaryTypes	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentFirstName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentMiddleName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentLastName	UserName	xsd:string	<xsd:maxLength value="50"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentSSN	SSN	xsd:string	<xsd:pattern value="([0-9]{9})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentBirthDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentDeathDate	xsd:date			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DecendentRelationship	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TransferFunds	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountNumber	String20	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FullTransfer	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DeliveringAccountClosed	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PartialTransfer	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TransferCashAmount	Decimal152	xsd:decimal	<xsd:totalDigits value="15"/><xsd:fractionDigits value="2"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
VoteProxy	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TransferShares	TransferShares			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '3', 'nillable': 'false'}
ConfirmSuppressionStatus	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SweepAccount	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Cash	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Liquid	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Prime	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Govemment	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Municipal	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Treasury	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
StateSpecific	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
Sate	States	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DreyfusGovCashManageServiceShares	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DreyfusGovSecCashManageInvestor	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DreyfusGovCashManageInvester	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FederatedHermesGovObligationsCash	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FederatedHermesTrustUSTreasuryOblicationsCash	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DelegateToAppointedAgent	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DelegateToOutsideProfessional	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SharesDescription	String35	xsd:string	<xsd:maxLength value="35"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SharesQuantity	xsd:decimal			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ExistingAccount	ExistingAccount			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NewAccount	NewAccount			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
ExistingAccountOption	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TiaaNumber	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
CrefNumber	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NewAccountOption	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
IraOption	IraOptions	xsd:string	<xsd:maxLength value="20"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BankCompanyName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
StreetAddress	AddressLines			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
PhoneNumber	FaxNumber	xsd:string	<xsd:pattern value="([0-9]{10})"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BankRoutingNumber	String10	xsd:string	<xsd:maxLength value="10"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
BankRolloverAmount	Decimal152	xsd:decimal	<xsd:totalDigits value="15"/><xsd:fractionDigits value="2"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountOwnerFirstName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountOwnerLastName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountJointFirstName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
AccountJointLastName	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
EmploymentStatus	EmploymentStatus	xsd:string	<xsd:maxLength value="6"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TypeOfBusiness	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
SourceOfIncome	xsd:string			{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TradeCompanyCheckBox	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NameOfPerson	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
CompanyNameSymbol	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
TIAACREFCheckBox	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
RelationshipToEmployee	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NameOfEmployee	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
FirmCheckBox	YesOrNoOption	xsd:string	<xsd:enumeration value="Y"/><xsd:enumeration value="N"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
NameOfFirm	FullName	xsd:string	<xsd:maxLength value="185"/>	{'default': None, 'fixed': None, 'minOccurs': '0', 'maxOccurs': '1', 'nillable': 'false'}
DocumentInfoXml	xsd:anyType			{'default': None, 'fixed': None, 'minOccurs': '1', 'maxOccurs': '1', 'nillable': 'false'}
