# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-02      #
# --------------------------------- #

"""
Main script to run the application report analysis
"""
import logging
import os
import sys

from utils.logging_utils import setup_logging
from utils.app_report_html import HTMLApplicationReportParser
from utils.app_report_xml import XMLApplicationReportParser
from utils.argument_utils import ApplicationArgumentParser
from utils.error_codes import ERR_FILE_001, ERR_FILE_003


LOGGER_NAME = "App Report Parser"
logger = logging.getLogger(LOGGER_NAME)


def parse_arguments(_):
    # Set the default application path directly in this file
    DEFAULT_APP_PATH = "../../applications/ACKLET"

    parser = ApplicationArgumentParser("1_get_app_report_json",
                                     "Main script to run the application report analysis",
                                     default_application_path=DEFAULT_APP_PATH)

    parser.add_file_argument('app_report_path',
                           'ACKLET Letters (AK) - PTRKTPAD AppReport.xml',
                           'HTML or XML app report file path')

    return parser.parse_args()


def main(args):

    # Configure logging
    log_dir = os.path.join(os.path.dirname(args.app_report_path), "logs")
    if not os.path.exists(log_dir):
        os.mkdir(log_dir)
    log_file = f"1_{os.path.basename(args.app_report_path).split('.')[-1]}_app_report_parser.log"
    log_file_path = os.path.join(log_dir, log_file)
    setup_logging(log_file=log_file_path, level=logging.DEBUG, max_bytes=1 * 1024 * 1024, backup_count=3, app_name=LOGGER_NAME)

    try:
        # Check for input arguments
        if not os.path.exists(args.app_report_path):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.app_report_path}")

        # Create an instance of the ApplicationReport class
        if args.app_report_path.endswith((".html", ".htm")):
            app_report_parser = HTMLApplicationReportParser(args.app_report_path)
        elif args.app_report_path.endswith(".xml"):
            app_report_parser = XMLApplicationReportParser(args.app_report_path, log_dir,  logger)
        else:
            raise ValueError(f"[{ERR_FILE_003}] Invalid file format, must be HTML or XML:\n{args.app_report_path}")
        app_report_parser.run()

    except Exception:
        logger.exception("")


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))
