# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-05-08      #
# --------------------------------- #

"""
Generate XML files for positive and negative test cases using sample payload XML
    Requires JSON application report with test cases generated using 3_generate_test_cases.py
"""


import ast
import csv
import json
import logging
import os
import sys

import pandas as pd

from utils.logging_utils import setup_logging
from utils.misc_utils import create_xml_from_payload, __freeze
from utils.xml_utils import parse_xml, concatenate_xmls
from utils.argument_utils import ApplicationArgumentParser
from utils.error_codes import ERR_FILE_001

# Use a flag to indicate if you want to include component rules
INCLUDE_COMPONENTS = True

LOGGER_NAME = "XML Files Generator"
logger = logging.getLogger(LOGGER_NAME)


def parse_arguments(_):
    # Set the default application path directly in this file
    DEFAULT_APP_PATH = "../../applications/Beneficiary Bundling"

    parser = ApplicationArgumentParser("4_generate_xml_files",
                                     "Generate XML files for positive and negative test cases using sample payload XML",
                                     default_application_path=DEFAULT_APP_PATH)

    parser.add_file_argument('test_cases_json_path',
                           'xml_parse/test_cases/Beneficiary Bundling (BB) - TNG PTRKBBWI & PTRKBBAT - AFP AppReport_test_cases.json',
                           'JSON application report containing test cases')
    parser.add_file_argument('complete_xml_path',
                           'complete_payload/sample_payload.xml',
                           'Complete payload XML file to be used for generating test cases')
    parser.add_file_argument('element_types_tsv_path',
                           'complete_payload/element_types.tsv',
                           'Path of the TSV file containing element type details',
                           required=False)
    parser.add_file_argument('skipped_elements_tsv_path',
                             'complete_payload/skipped_elems.tsv',
                             'Path of the TSV file containing skipped elements name and path')

    return parser.parse_args()


def main(args):
    # Configure logging
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(args.test_cases_json_path))), "logs")
    if not os.path.exists(log_dir):
        os.mkdir(log_dir)
    log_file = "4_xml_files_generator.log"
    log_file_path = os.path.join(log_dir, log_file)
    setup_logging(log_file=log_file_path, level=logging.DEBUG, max_bytes=1 * 1024 * 1024, backup_count=3,
                  app_name=LOGGER_NAME)

    """Check for input arguments"""
    if not os.path.exists(args.test_cases_json_path):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.test_cases_json_path}")
    if not os.path.exists(args.complete_xml_path):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.complete_xml_path}")
    if not os.path.exists(args.element_types_tsv_path):
        raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.element_types_tsv_path}")
    if args.skipped_elements_tsv_path:
        if not os.path.exists(args.skipped_elements_tsv_path):
            raise FileNotFoundError(f"[{ERR_FILE_001}] File not found: {args.skipped_elements_tsv_path}")

    # Read the element types TSV
    element_types_df = pd.read_csv(args.element_types_tsv_path, sep='\t', header=0, index_col=None, quoting=csv.QUOTE_NONE, escapechar="\\")
    element_types_df["info"] = element_types_df["info"].apply(ast.literal_eval)
    element_types_df['maxOccurs'] = element_types_df['info'].apply(lambda d: d['maxOccurs'])

    # Read the skipped elements TSV
    skipped_elems_df = None
    if args.skipped_elements_tsv_path:
        skipped_elems_df = pd.read_csv(args.skipped_elements_tsv_path, sep="\t", header=0, index_col=None)

    with open(args.test_cases_json_path, 'r') as json_file:
        test_cases_data = json.load(json_file)
    logger.info(f"========================= APPLICATION: {test_cases_data["app-name"]} =========================")
    logger.new_line()

    # Create directory for XML files and prepare the output file path
    xml_files_dir = os.path.join(os.path.dirname(args.test_cases_json_path), "xmls")
    xml_dir = {
        "positives": os.path.join(xml_files_dir, "positives"),
        "negatives": os.path.join(xml_files_dir, "negatives")
    }
    for dir_path in xml_dir.values():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

    # Read the sample XML file
    xml_tree, _ = parse_xml(args.complete_xml_path)

    # Iterate through each test case in the documents and pages to create XML files
    combined_result_data = []
    logger.info("Generating XML files for all the testcases..")
    for document in test_cases_data["documents"]:
        tsv_entries = create_xml_from_payload(document, xml_tree, xml_dir, skipped_elems_df, element_types_df, logger, include_components=INCLUDE_COMPONENTS)
        combined_result_data.extend(tsv_entries)
    logger.new_line()

    # Get the combined data in a dataframe and post-process to remove duplicates
    columns = ["xml_file", "payload_type", "payload", "level_1", "level_2", "level_3", "level_4", "level_5", "missing_tags"]
    df = pd.DataFrame(combined_result_data, columns=columns)
    df_uniq, df_drop = filter_dataframe(df)

    # Delete the XML files for dropped rows - contain duplicate test cases for same rule combination
    if len(df_drop) > 0:
        logger.info(f"Dropping {len(df_drop)} (out of {len(df)}) test cases - contain duplicates for same rule combination..")
        logger.new_line()
        for _, row in df_drop.iterrows():
            payload_type = row["payload_type"]
            file_name = row["xml_file"]
            if os.path.exists(os.path.join(xml_dir[payload_type], file_name)):
                os.remove(os.path.join(xml_dir[payload_type], file_name))

    # Save the combined result data to TSV dataframe
    tsv_file = os.path.basename(args.test_cases_json_path)[:-5] + ".tsv"
    tsv_path = os.path.join(xml_files_dir, tsv_file)
    df_uniq.to_csv(tsv_path, sep="\t", header=True, index=False, columns=columns)
    logger.info(f"Combined test cases details: {os.path.abspath(tsv_path)}")
    logger.new_line()

    # Concatenate the XMLs into one big file, each for positive and negative
    logger.info("Concatenating XML files into single XML for positive and negative each..")
    for xml_type, xml_dir_path in xml_dir.items():
        output_file_path = os.path.join(xml_files_dir, xml_type + ".xml")
        total_files = concatenate_xmls(xml_dir_path, output_file_path, root_tag="DocumentRequests")
        logger.info(f"\t#XMLs for {xml_type}: {total_files}")
        logger.info(f"\tSingle XML for {xml_type}: {os.path.abspath(output_file_path)}")
        logger.new_line()

def filter_dataframe(dataframe):
    """Filter dataframe based on XML file prefix and payload details - remove duplicates"""
    dataframe["xml_file_prefix"] = dataframe["xml_file"].apply(lambda x: x.rsplit("_", 1)[0])
    # Freeze the final payload and test cases for all the parts of application - to be able to find the duplicates
    for part in ["payload", "level_1", "level_2", "level_3", "level_4", "level_5"]:
        dataframe["frozen_" + part] = dataframe[part].apply(lambda x: __freeze(x))

    # Drop duplicates based on xml prefix, frozen test cases and payload type
    df_unique = dataframe.drop_duplicates(subset=["xml_file_prefix", "payload_type", "frozen_payload", "frozen_level_1",
                                                  "frozen_level_2", "frozen_level_3", "frozen_level_4", "frozen_level_5"], keep="first")
    # Record the dropped rows to delete the XML files
    df_dropped = dataframe.loc[~dataframe.index.isin(df_unique.index)]

    return df_unique, df_dropped


if __name__ == '__main__':
    main(parse_arguments(sys.argv[1:]))
