# -*- coding: utf-8 -*-
# --------------------------------- #
#       Author: <PERSON><PERSON><PERSON>     #
#       Python Version: 3.12        #
#       Created on: 2025-06-26      #
# --------------------------------- #

"""
Main script to run all the steps
"""

import subprocess
import sys
from utils.argument_utils import Application<PERSON>rgument<PERSON>arser

def run_script(script_path, args=None):
    """
    Runs a python script with the given arguments.
    Raises CalledProcessError if the script exits with non-zero.
    """
    cmd = [sys.executable, script_path]
    if args:
        cmd += args
    print("\n\n======================================")
    print(f"Running: {' '.join(cmd)}")
    subprocess.run(cmd, check=True)

def main():
    # Set the default application path directly in this file
    DEFAULT_APP_PATH = "../../applications/Combined RPPM Report"

    parser = ApplicationArgumentParser("main",
                                     "Main script to run all the steps in sequence",
                                     default_application_path=DEFAULT_APP_PATH)

    # Add all file arguments that will be passed to individual scripts
    parser.add_file_argument("app_report_path",
                           'AppReport.xml',
                           "HTML or XML app report file path")

    parser.add_file_argument("main_xsd_path",
                           'xsd/ccp-document-request-v1-types.xsd',
                           "Main XSD file path for the application")
    parser.add_file_argument("app_xsd_path",
                           'xsd/ccp-document-request-onlinerppm-rsarep-v1.xsd',
                           "XSD file path specifying the application request")
    parser.add_file_argument("sample_xml_path",
                           'ONLINECMBREP_V2 Input.xml',
                           "Sample XML instance file path")
    parser.add_file_argument("mapping_path",
                           'xml_parse/app_info/xml_tags_to_variables.tsv',
                           "Var-Tag mapping TSV file path")

    parser.add_file_argument("app_report_json_path",
                           'xml_parse/AppReport.json',
                           "JSON application report")
    parser.add_file_argument("testcase_generation_details_tsv",
                           'xml_parse/app_info/testcase_generation_details.tsv',
                           "TSV containing test case generation details")
    parser.add_file_argument('complete_xml_log',
                           'complete_payload/sample_payload_log.tsv',
                           'Complete payload XML log to get the sample values')
    parser.add_file_argument('element_types_tsv_path',
                           'complete_payload/element_types.tsv',
                           'Path of the TSV file containing element type details')
    parser.add_file_argument('rule_errors_tsv',
                           'logs/rule_errors_from_parsing.tsv',
                           'Path of the TSV file containing rule error details obtained during parsing')

    parser.add_file_argument('test_cases_json_path',
                           'xml_parse/test_cases/AppReport_test_cases.json',
                           'JSON application report containing test cases')
    parser.add_file_argument('complete_xml_path',
                           'complete_payload/sample_payload.xml',
                           'Complete payload XML file to be used for generating test cases')
    parser.add_file_argument('skipped_elements_tsv_path',
                             'complete_payload/skipped_elems.tsv',
                             'Path of the TSV file containing skipped elements name and path')

    args = parser.parse_args()

    # Pass application_path to each script so they can use it
    common_args = ["--application_path", args.application_path] if args.application_path else []

    tasks = [
        ("1_get_app_report_json.py", common_args + ["--app_report_path", args.app_report_path]),
        ("2_get_complete_payload.py", common_args + ["--main_xsd_path", args.main_xsd_path,
                                                   "--app_xsd_path", args.app_xsd_path,
                                                   "--sample_xml_path", args.sample_xml_path,
                                                   "--mapping_path", args.mapping_path,
                                                   "--testcase_generation_details_tsv", args.testcase_generation_details_tsv]),
        ("3_generate_test_cases.py", common_args + ["--app_report_json_path", args.app_report_json_path,
                                                  "--testcase_generation_details_tsv", args.testcase_generation_details_tsv,
                                                  "--complete_xml_log", args.complete_xml_log,
                                                  "--element_types_tsv_path", args.element_types_tsv_path,
                                                  "--rule_errors_tsv", args.rule_errors_tsv]),
        ("4_generate_xml_files.py", common_args + ["--test_cases_json_path", args.test_cases_json_path,
                                                 "--complete_xml_path", args.complete_xml_path,
                                                 "--element_types_tsv_path", args.element_types_tsv_path,
                                                   "--skipped_elements_tsv_path", args.skipped_elements_tsv_path]),
    ]

    for script, script_args in tasks:
        try:
            run_script(script, script_args)
        except subprocess.CalledProcessError as e:
            print(f"Error running {script}: {e}")
            sys.exit(1)

    print("\n\n======================================")
    print("All scripts completed successfully!")


if __name__ == "__main__":
    main()